// Importer une bibliothèque nécessaireusing Microsoft.AspNetCore.Mvc;using Microsoft.AspNetCore.Mvc;
// Importer une bibliothèque nécessaireusing Microsoft.EntityFrameworkCore;using Microsoft.EntityFrameworkCore;
// Importer une bibliothèque nécessaireusing AxiaLivraisonAPI.Data;using AxiaLivraisonAPI.Data;
// Importer une bibliothèque nécessaireusing AxiaLivraisonAPI.Models;using AxiaLivraisonAPI.Models;
// Importer une bibliothèque nécessaireusing System.Collections.Generic;using System.Collections.Generic;
// Importer une bibliothèque nécessaireusing System.Linq;using System.Linq;
// Importer une bibliothèque nécessaireusing System.Threading.Tasks;using System.Threading.Tasks;
// Importer une bibliothèque nécessaireusing Microsoft.Extensions.Logging;using Microsoft.Extensions.Logging;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Controllers
namespace AxiaLivraisonAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    // Classe publique qui définit un objetpublic class NotificationController    public class NotificationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(ApplicationDbContext context, ILogger<NotificationController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/notification/by-commande/{commandeId}
        // Méthode qui répond aux requêtes GET[HttpGet        [HttpGet("by-commande/{commandeId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetNotificationsByCommande(int commandeId)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.CommandeId == commandeId)
                    .Include(n => n.Commande)
                    .OrderByDescending(n => n.DateEnvoi)
                    .Select(n => new
                    {
                        id = n.Id,
                        recipient = n.Destinataire,
                        subject = n.Sujet,
                        body = n.Contenu,
                        sentDate = n.DateEnvoi.ToString("o"),
                        isRead = n.EstLue,
                        commandeId = n.CommandeId,
                        codeSuivi = n.Commande.CodeSuivi
                    })
                    .ToListAsync();

                return Ok(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting notifications for commande {commandeId}");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/notification/{id}
        // Méthode qui répond aux requêtes GET[HttpGet        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetNotification(int id)
        {
            var notification = await _context.Notifications
                .Include(n => n.Commande)
                .Where(n => n.Id == id)
                .Select(n => new
                {
                    id = n.Id,
                    recipient = n.Destinataire,
                    subject = n.Sujet,
                    body = n.Contenu,
                    sentDate = n.DateEnvoi.ToString("o"),
                    isRead = n.EstLue,
                    commandeId = n.CommandeId,
                    codeSuivi = n.Commande.CodeSuivi
                })
                .FirstOrDefaultAsync();

            if (notification == null)
            {
                return NotFound();
            }

            return Ok(notification);
        }

        // GET: api/notification/unread-count/by-commande/{commandeId}
        // Méthode qui répond aux requêtes GET[HttpGet        [HttpGet("unread-count/by-commande/{commandeId}")]
        public async Task<ActionResult<object>> GetUnreadCountByCommande(int commandeId)
        {
            var count = await _context.Notifications
                .CountAsync(n => n.CommandeId == commandeId && !n.EstLue);

            return Ok(new { count });
        }

        // PUT: api/notification/mark-read/{id}
        [HttpPut("mark-read/{id}")]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
            {
                return NotFound();
            }

            notification.EstLue = true;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // PUT: api/notification/mark-all-read/by-commande/{commandeId}
        [HttpPut("mark-all-read/by-commande/{commandeId}")]
        public async Task<IActionResult> MarkAllAsReadForCommande(int commandeId)
        {
            await _context.Notifications
                .Where(n => n.CommandeId == commandeId && !n.EstLue)
                .ForEachAsync(n => n.EstLue = true);

            await _context.SaveChangesAsync();

            return NoContent();
        }

        // DELETE: api/notification/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNotification(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
            {
                return NotFound();
            }

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
}