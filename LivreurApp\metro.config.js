const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    // Add support for Expo modules
    sourceExts: ['jsx', 'js', 'ts', 'tsx', 'json', 'mjs'],
    assetExts: ['db', 'sqlite', 'png', 'jpg', 'jpeg', 'gif', 'webp'],
    extraNodeModules: {
      // Ensure components can be resolved
      'components': path.resolve(__dirname, 'src/components'),
    },
  },
  watchFolders: [
    // Add src directory to watch folders
    path.resolve(__dirname, 'src'),
  ],
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
