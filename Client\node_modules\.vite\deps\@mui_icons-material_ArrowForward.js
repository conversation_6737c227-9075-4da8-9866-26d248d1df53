"use client";
import {
  require_createSvgIcon
} from "./chunk-F6HH2RS4.js";
import {
  require_interopRequireDefault
} from "./chunk-TDKLIJLH.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/ArrowForward.js
var require_ArrowForward = __commonJS({
  "node_modules/@mui/icons-material/ArrowForward.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "m12 4-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"
    }), "ArrowForward");
  }
});
export default require_ArrowForward();
//# sourceMappingURL=@mui_icons-material_ArrowForward.js.map
