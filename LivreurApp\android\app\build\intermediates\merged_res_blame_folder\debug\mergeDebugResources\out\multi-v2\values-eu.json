{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "280,389,487,597,683,789,913,999,1080,1172,1266,1362,1456,1557,1651,1747,1844,1936,2029,2111,2220,2329,2428,2537,2644,2755,2926,10378", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "384,482,592,678,784,908,994,1075,1167,1261,1357,1451,1552,1646,1742,1839,1931,2024,2106,2215,2324,2423,2532,2639,2750,2921,3020,10456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4132,4236,4408,4532,4641,4793,4918,5042,5293,5471,5579,5742,5870,6024,6184,6250,6315", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "4231,4403,4527,4636,4788,4913,5037,5145,5466,5574,5737,5865,6019,6179,6245,6310,6402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "34,35,36,37,38,39,40,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3105,3203,3306,3406,3509,3614,3717,10461", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3198,3301,3401,3504,3609,3712,3831,10557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,310,408,523,606,673,772,840,901,989,1055,1119,1190,1253,1307,1416,1475,1538,1592,1666,1791,1881,1961,2076,2159,2241,2332,2399,2465,2536,2616,2702,2780,2858,2931,3006,3093,3180,3271,3364,3436,3512,3604,3655,3721,3805,3891,3953,4017,4080,4187,4292,4388,4494", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "225,305,403,518,601,668,767,835,896,984,1050,1114,1185,1248,1302,1411,1470,1533,1587,1661,1786,1876,1956,2071,2154,2236,2327,2394,2460,2531,2611,2697,2775,2853,2926,3001,3088,3175,3266,3359,3431,3507,3599,3650,3716,3800,3886,3948,4012,4075,4182,4287,4383,4489,4572"}, "to": {"startLines": "2,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3836,3934,4049,6407,6474,6573,6641,6702,6790,6856,6920,6991,7054,7108,7217,7276,7339,7393,7467,7592,7682,7762,7877,7960,8042,8133,8200,8266,8337,8417,8503,8581,8659,8732,8807,8894,8981,9072,9165,9237,9313,9405,9456,9522,9606,9692,9754,9818,9881,9988,10093,10189,10295", "endLines": "5,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "275,3100,3929,4044,4127,6469,6568,6636,6697,6785,6851,6915,6986,7049,7103,7212,7271,7334,7388,7462,7587,7677,7757,7872,7955,8037,8128,8195,8261,8332,8412,8498,8576,8654,8727,8802,8889,8976,9067,9160,9232,9308,9400,9451,9517,9601,9687,9749,9813,9876,9983,10088,10184,10290,10373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5150", "endColumns": "142", "endOffsets": "5288"}}]}]}