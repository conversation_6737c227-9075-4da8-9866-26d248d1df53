#!/usr/bin/env pwsh

# Script pour restaurer les fichiers corrompus par les commentaires automatiques
Write-Host "🔧 Restauration des fichiers corrompus..." -ForegroundColor Green

# Fonction pour nettoyer les doublons de commentaires
function Clean-File {
    param([string]$FilePath)
    
    Write-Host "Nettoyage: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Supprimer les doublons de commentaires créés par le script automatique
    $content = $content -replace '// Importer des fonctionnalités depuis un autre fichierimport ([^i]+)import ', 'import '
    $content = $content -replace '// Créer une constanteconst ([^c]+)const ', 'const '
    $content = $content -replace '// Définir une fonctionfunction ([^f]+)function ', 'function '
    $content = $content -replace '// Exporter comme élément principal de ce fichierexport default([^e]+)export default', 'export default'
    $content = $content -replace '// Importer une bibliothèque nécessaireusing ([^u]+)using ', 'using '
    $content = $content -replace '// Définir l''espace de noms pour organiser le codenamespace ([^n]+)namespace ', 'namespace '
    $content = $content -replace '// Classe publique qui définit un objetpublic class ([^p]+)public class ', 'public class '
    $content = $content -replace '// Marquer comme clé primaire de la base de données\[Key\]([^\[]+)\[Key\]', '[Key]'
    $content = $content -replace '// Cette propriété est obligatoire\[Required\]([^\[]+)\[Required\]', '[Required]'
    $content = $content -replace '// Méthode qui répond aux requêtes GET\[HttpGet([^\[]+)\[HttpGet', '[HttpGet'
    $content = $content -replace '// Méthode qui répond aux requêtes POST\[HttpPost([^\[]+)\[HttpPost', '[HttpPost'
    
    # Nettoyer les lignes vides multiples
    $content = $content -replace '(\r?\n){3,}', "`n`n"
    
    # Supprimer les caractères de fin de ligne en trop
    $content = $content.TrimEnd()
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Nettoyer tous les fichiers TypeScript/JavaScript de l'admin
Write-Host "🔧 Nettoyage des fichiers admin..." -ForegroundColor Cyan
if (Test-Path "admin/src") {
    $adminFiles = Get-ChildItem -Path "admin/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
        $_.FullName -notmatch "node_modules|\.d\.ts$" 
    }

    foreach ($file in $adminFiles) {
        try {
            Clean-File -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Nettoyer tous les fichiers TypeScript/JavaScript du client
Write-Host "🌐 Nettoyage des fichiers client..." -ForegroundColor Cyan
if (Test-Path "Client/src") {
    $clientFiles = Get-ChildItem -Path "Client/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
        $_.FullName -notmatch "node_modules|\.d\.ts$" 
    }

    foreach ($file in $clientFiles) {
        try {
            Clean-File -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Nettoyer tous les fichiers JavaScript/JSX de l'app mobile
Write-Host "📱 Nettoyage des fichiers mobile..." -ForegroundColor Cyan
if (Test-Path "LivreurApp/src") {
    $mobileFiles = Get-ChildItem -Path "LivreurApp/src" -Recurse -Include "*.js", "*.jsx"

    foreach ($file in $mobileFiles) {
        try {
            Clean-File -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Nettoyer tous les fichiers C# du backend
Write-Host "🔧 Nettoyage des fichiers backend..." -ForegroundColor Cyan
$csFiles = Get-ChildItem -Path "backend" -Recurse -Include "*.cs" | Where-Object { 
    $_.FullName -notmatch "\\obj\\|\\bin\\|Migrations" 
}

foreach ($file in $csFiles) {
    try {
        Clean-File -FilePath $file.FullName
    }
    catch {
        Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Nettoyage termine!" -ForegroundColor Green
Write-Host "Resume:" -ForegroundColor White
Write-Host "   - Fichiers admin nettoyes: $($adminFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers client nettoyes: $($clientFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers mobile nettoyes: $($mobileFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers backend nettoyes: $($csFiles.Count)" -ForegroundColor Gray
