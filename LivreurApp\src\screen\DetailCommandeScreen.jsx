import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Image,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { colors, getStatusColor } from '../theme';

const DetailCommandeScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {commande} = route.params || {};

  // Formatage de la date
  const formatDate = dateString => {
    if (!dateString) return 'Date inconnue';
    try {
      const date = new Date(dateString);
      const options = {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      };
      return date.toLocaleDateString('fr-FR', options);
    } catch (e) {
      return 'Date inconnue';
    }
  };



  // Calcul du total
  const total = commande?.montantTotale || 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FFAA80" />

      {/* Enhanced Header with Orange Gradient */}
      <LinearGradient
        colors={['#FFAA80', '#FFD6C2']}
        style={styles.headerGradient}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}>
            <AntDesign name="arrowleft" size={24} color={colors.neutral.white} />
          </TouchableOpacity>
          <View style={styles.headerSpacer} />
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/images/logo.png')}
              style={styles.headerLogo}
            />
          </View>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enhanced Order Header Section */}
        <View style={styles.orderHeaderSection}>
          <LinearGradient
            colors={['#FFFFFF', '#FFF5F0']}
            style={styles.orderHeaderGradient}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}>
            <View style={styles.orderHeaderContent}>
              {commande?.statut && (
                <View style={styles.statusBadgeSmall}>
                  <View
                    style={[
                      styles.statusDotSmall,
                      {backgroundColor: getStatusColor(commande.statut)},
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusTextSmall,
                      {color: getStatusColor(commande.statut)},
                    ]}>
                    {commande.statut}
                  </Text>
                </View>
              )}

              <View style={styles.orderIdBadge}>
                <MaterialIcons name="confirmation-number" size={16} color="#995623" />
                <Text style={styles.orderIdText}>CMD-{commande?.id || 'N/A'}</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Enhanced Client Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="person" size={20} color="#FFAA80" />
            <Text style={styles.sectionTitle}>Informations Client</Text>
          </View>
          <View style={styles.infoContainer}>
            <View style={styles.infoRow}>
              <View style={[styles.iconContainer, {backgroundColor: '#FFF5F0'}]}>
                <FontAwesome name="user" size={16} color="#FFAA80" />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Nom</Text>
                <Text style={styles.infoText}>
                  {commande?.nomClient || 'Client inconnu'}
                </Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <View style={[styles.iconContainer, {backgroundColor: '#FFF5F0'}]}>
                <Ionicons name="mail" size={16} color="#FFAA80" />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoText}>
                  {commande?.emailClient || 'Email non disponible'}
                </Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <View style={[styles.iconContainer, {backgroundColor: '#FFF5F0'}]}>
                <FontAwesome name="phone" size={16} color="#FFAA80" />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Téléphone</Text>
                <Text style={styles.infoText}>
                  {commande?.telephoneClient || 'Téléphone non disponible'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Enhanced Articles Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="shopping-bag" size={20} color="#FFAA80" />
            <Text style={styles.sectionTitle}>Articles Commandés</Text>
          </View>
          <View style={[styles.articleCard, {backgroundColor: '#FFF5F0'}]}>
            <View style={styles.articleHeader}>
              <Text style={styles.itemName}>
                {commande?.description || 'Détails non disponibles'}
              </Text>
              <Text style={[styles.itemPrice, {color: '#FFAA80'}]}>
                {(commande?.prixUnitaire || 0).toFixed(2)} DT
              </Text>
            </View>
            <View style={styles.articleDetails}>
              <View style={styles.quantityContainer}>
                <MaterialIcons name="inventory" size={16} color="#FFAA80" />
                <Text style={styles.quantityText}>
                  Quantité: {commande?.quantite || 0}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Enhanced Delivery Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="local-shipping" size={20} color="#FFAA80" />
            <Text style={styles.sectionTitle}>Informations de Livraison</Text>
          </View>
          <View style={styles.infoContainer}>
            <View style={styles.infoRow}>
              <View style={[styles.iconContainer, {backgroundColor: '#FFF5F0'}]}>
                <FontAwesome name="map-marker" size={16} color="#FFAA80" />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Adresse</Text>
                <Text style={styles.infoText}>
                  {commande?.adressClient || 'Adresse non disponible'}
                </Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <View style={[styles.iconContainer, {backgroundColor: '#FFF5F0'}]}>
                <FontAwesome name="calendar" size={16} color="#FFAA80" />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Date de commande</Text>
                <Text style={styles.infoText}>
                  {formatDate(commande?.dateCreation)}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Enhanced Payment Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="payment" size={20} color="#FFAA80" />
            <Text style={styles.sectionTitle}>Mode de Paiement</Text>
          </View>
          <View style={styles.paymentContainer}>
            <View style={[styles.paymentMethod, {backgroundColor: '#FFF5F0'}]}>
              <MaterialIcons name="money" size={18} color="#FFAA80" />
              <Text style={styles.paymentText}>Espèces à la livraison</Text>
            </View>
          </View>
        </View>

        {/* Enhanced Summary Section */}
        <View style={styles.summarySection}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="receipt" size={20} color="#FFAA80" />
            <Text style={styles.sectionTitle}>Récapitulatif Financier</Text>
          </View>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Sous-total</Text>
              <Text style={styles.summaryValue}>
                {(commande?.montantHorsTax || 0).toFixed(2)} DT
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>TVA</Text>
              <Text style={styles.summaryValue}>
                {(commande?.tva || 0).toFixed(2)} DT
              </Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total à payer</Text>
              <Text style={styles.totalValue}>{total.toFixed(2)} DT</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral.background,
  },
  // Enhanced Header Styles
  headerGradient: {
    paddingBottom: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 6,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginTop: 15,
  },
  headerSpacer: {
    flex: 1,

  },
  logoContainer: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 2,
    borderColor: '#FFD6C2',
    overflow: 'hidden',
    shadowColor: '#FFAA80',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
    marginTop: 3,
  },
  headerLogo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  content: {
    flex: 1,
    padding: 12,
  },

  section: {
    backgroundColor: colors.neutral.surface,
    borderRadius: 12,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#FFAA80',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#FFD6C2',
  },
  // Order Header Section Styles
  orderHeaderSection: {
    marginBottom: 12,
  },
  orderHeaderGradient: {
    borderRadius: 12,
    padding: 14,
    shadowColor: colors.shadow.primary,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  orderHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  statusBadgeSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginLeft: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  statusDotSmall: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  statusTextSmall: {
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 1,
  },
  orderIdBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(153, 86, 35, 0.2)',
  },
  orderIdText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#995623',
    marginLeft: 6,
    letterSpacing: 0.3,
  },

  // Section Header Styles
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#FFD6C2',
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#995623',
    marginLeft: 8,
  },
  // Info Container Styles
  infoContainer: {
    marginTop: 6,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    paddingVertical: 3,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary.lighter,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 3,
  },
  infoText: {
    fontSize: 14,
    color: colors.text.dark,
    fontWeight: '500',
    lineHeight: 18,
  },
  // Article Card Styles
  articleCard: {
    backgroundColor: colors.primary.lighter,
    borderRadius: 10,
    padding: 12,
    marginTop: 6,
  },
  articleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  itemName: {
    fontSize: 14,
    color: colors.text.dark,
    fontWeight: '600',
    flex: 1,
    marginRight: 10,
    lineHeight: 18,
  },
  itemPrice: {
    fontSize: 15,
    fontWeight: 'bold',
    color: colors.primary.main,
  },
  articleDetails: {
    marginTop: 6,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 12,
    color: colors.text.secondary,
    marginLeft: 6,
    fontWeight: '500',
  },
  // Payment Styles
  paymentContainer: {
    marginTop: 6,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary.lighter,
    borderRadius: 10,
    padding: 12,
  },
  paymentText: {
    fontSize: 14,
    color: colors.text.dark,
    fontWeight: '500',
    marginLeft: 10,
  },
  // Summary Section Styles
  summarySection: {
    backgroundColor: colors.neutral.surface,
    borderRadius: 12,
    padding: 14,
    marginBottom: 16,
    shadowColor: '#FFAA80',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 10,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#FFD6C2',
  },
  summaryContainer: {
    marginTop: 6,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    marginBottom: 3,
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 14,
    color: colors.text.dark,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#FFD6C2',
    marginVertical: 8,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: '#FFF5F0',
    borderRadius: 10,
    marginTop: 6,
  },
  totalLabel: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#995623',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFAA80',
  },
});

export default DetailCommandeScreen;
