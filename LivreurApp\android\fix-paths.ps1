$oldPath = "E:/PFE/Test/AxiaLivraison/LivreurApp"
$newPath = "E:/PFE/Test/AxiaLivraison/LivreurApp"

# Find all files that contain the old path
$files = Get-ChildItem -Path . -Recurse -File | Select-String -Pattern $oldPath -List | Select-Object Path

Write-Host "Found $($files.Count) files with the old path"

foreach ($file in $files) {
    $filePath = $file.Path
    Write-Host "Processing $filePath"

    # Skip binary files
    if ($filePath -match "\.(so|jar|bin|obj|class|dex|apk|aar|png|jpg|jpeg|gif|ttf|otf)$") {
        Write-Host "Skipping binary file: $filePath"
        continue
    }

    # Read the file content
    $content = Get-Content -Path $filePath -Raw -ErrorAction SilentlyContinue

    if ($content) {
        # Replace the old path with the new path
        $newContent = $content -replace [regex]::Escape($oldPath), $newPath

        # Write the updated content back to the file
        if ($content -ne $newContent) {
            Write-Host "Updating $filePath"
            Set-Content -Path $filePath -Value $newContent -NoNewline
        }
    }
}

Write-Host "Path replacement completed"
