﻿// Importer des fonctionnalités depuis un autre fichierimport { useEffect, useState } fromimport { useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useNavigate } fromimport { useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport { authService } fromimport { authService } from 'services/authService';
// Importer des fonctionnalités depuis un autre fichierimport { rootPaths } fromimport { rootPaths } from 'routes/paths';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport CircularProgress fromimport CircularProgress from '@mui/material/CircularProgress';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';

// Définir une interface TypeScriptinterface AuthGuardProps
interface AuthGuardProps {
  children: React.ReactNode;
}

// Créer une constanteconst AuthGuard =
const AuthGuard = ({ children }: AuthGuardProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  // Créer une constanteconst navigate =  const navigate = useNavigate();

  useEffect(() => {
    // Créer une constanteconst checkAuth =    const checkAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          // Try to get a valid token (this will refresh if needed)
          // Créer une constanteconst token =          const token = await authService.getValidToken();
          if (token) {
            setIsAuthenticated(true);
          } else {
            // Token refresh failed, redirect to login
            navigate(rootPaths.root);
          }
        } else {
          // Not authenticated, redirect to login
          navigate(rootPaths.root);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        navigate(rootPaths.root);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate]);

  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress />
        <Typography variant="body2" color="text.secondary">
          Vérification de l'authentification...
        </Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return <>{children}</>;
};

// Exporter comme élément principal de ce fichierexport default
export default AuthGuard;
