"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _os() {
  const data = _interopRequireDefault(require("os"));
  _os = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _fsExtra() {
  const data = _interopRequireDefault(require("fs-extra"));
  _fsExtra = function () {
    return data;
  };
  return data;
}
var _validate = require("./validate");
var _DirectoryAlreadyExistsError = _interopRequireDefault(require("./errors/DirectoryAlreadyExistsError"));
var _printRunInstructions = _interopRequireDefault(require("./printRunInstructions"));
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var _template = require("./template");
var _editTemplate = require("./editTemplate");
var PackageManager = _interopRequireWildcard(require("../../tools/packageManager"));
function _cliDoctor() {
  const data = require("@react-native-community/cli-doctor");
  _cliDoctor = function () {
    return data;
  };
  return data;
}
var _banner = _interopRequireDefault(require("./banner"));
var _TemplateAndVersionError = _interopRequireDefault(require("./errors/TemplateAndVersionError"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const DEFAULT_VERSION = 'latest';
function doesDirectoryExist(dir) {
  return _fsExtra().default.existsSync(dir);
}
async function setProjectDirectory(directory) {
  if (doesDirectoryExist(directory)) {
    throw new _DirectoryAlreadyExistsError.default(directory);
  }
  try {
    _fsExtra().default.mkdirSync(directory, {
      recursive: true
    });
    process.chdir(directory);
  } catch (error) {
    throw new (_cliTools().CLIError)('Error occurred while trying to create project directory.', error);
  }
  return process.cwd();
}
function getTemplateName(cwd) {
  // We use package manager to infer the name of the template module for us.
  // That's why we get it from temporary package.json, where the name is the
  // first and only dependency (hence 0).
  const name = Object.keys(JSON.parse(_fsExtra().default.readFileSync(_path().default.join(cwd, './package.json'), 'utf8')).dependencies)[0];
  return name;
}
async function createFromTemplate({
  projectName,
  templateUri,
  npm,
  directory,
  projectTitle,
  skipInstall,
  packageName
}) {
  _cliTools().logger.debug('Initializing new project');
  _cliTools().logger.log(_banner.default);
  const projectDirectory = await setProjectDirectory(directory);
  const loader = (0, _cliTools().getLoader)({
    text: 'Downloading template'
  });
  const templateSourceDir = _fsExtra().default.mkdtempSync(_path().default.join(_os().default.tmpdir(), 'rncli-init-template-'));
  try {
    loader.start();
    await (0, _template.installTemplatePackage)(templateUri, templateSourceDir, npm);
    loader.succeed();
    loader.start('Copying template');
    const templateName = getTemplateName(templateSourceDir);
    const templateConfig = (0, _template.getTemplateConfig)(templateName, templateSourceDir);
    await (0, _template.copyTemplate)(templateName, templateConfig.templateDir, templateSourceDir);
    loader.succeed();
    loader.start('Processing template');
    await (0, _editTemplate.changePlaceholderInTemplate)({
      projectName,
      projectTitle,
      placeholderName: templateConfig.placeholderName,
      placeholderTitle: templateConfig.titlePlaceholder,
      packageName
    });
    loader.succeed();
    const {
      postInitScript
    } = templateConfig;
    if (postInitScript) {
      loader.info('Executing post init script ');
      await (0, _template.executePostInitScript)(templateName, postInitScript, templateSourceDir);
    }
    if (!skipInstall) {
      await installDependencies({
        npm,
        loader,
        root: projectDirectory,
        directory
      });
    } else {
      loader.succeed('Dependencies installation skipped');
    }
  } catch (e) {
    loader.fail();
    throw e;
  } finally {
    _fsExtra().default.removeSync(templateSourceDir);
  }
}
async function installDependencies({
  directory,
  npm,
  loader,
  root
}) {
  loader.start('Installing dependencies');
  await PackageManager.installAll({
    preferYarn: !npm,
    silent: true,
    root
  });
  if (process.platform === 'darwin') {
    await (0, _cliDoctor().installPods)({
      directory,
      loader
    });
  }
  loader.succeed();
}
function createTemplateUri(options, version) {
  const isTypescriptTemplate = options.template === 'react-native-template-typescript';
  if (isTypescriptTemplate) {
    _cliTools().logger.warn("Ignoring custom template: 'react-native-template-typescript'. Starting from React Native v0.71 TypeScript is used by default.");
    return 'react-native';
  }
  return options.template || `react-native@${version}`;
}
async function createProject(projectName, directory, version, options) {
  const templateUri = createTemplateUri(options, version);
  return createFromTemplate({
    projectName,
    templateUri,
    npm: options.npm,
    directory,
    projectTitle: options.title,
    skipInstall: options.skipInstall,
    packageName: options.packageName
  });
}
var initialize = async function initialize([projectName], options) {
  (0, _validate.validateProjectName)(projectName);
  if (!!options.template && !!options.version) {
    throw new _TemplateAndVersionError.default(options.template);
  }
  const root = process.cwd();
  const version = options.version || DEFAULT_VERSION;
  const directoryName = _path().default.relative(root, options.directory || projectName);
  await createProject(projectName, directoryName, version, options);
  const projectFolder = _path().default.join(root, directoryName);
  (0, _printRunInstructions.default)(projectFolder, projectName);
};
exports.default = initialize;

//# sourceMappingURL=init.ts.map