// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

const Checkbox: Components<Omit<Theme, 'components'>>['MuiCheckbox'] = {
  defaultProps: {
    icon: <IconifyIcon icon="mdi:checkbox-blank" />,
    checkedIcon: <IconifyIcon icon="mdi:checkbox-marked" />,
    indeterminateIcon: <IconifyIcon icon="mdi:indeterminate-check-box" />,
  },
  styleOverrides: {
    root: ({ theme }) => ({
      color: theme.palette.info.dark,
    }),
    sizeMedium: ({ theme }) => ({
      padding: theme.spacing(0.75),
      '& .MuiBox-root': {
        fontSize: theme.typography.h5.fontSize,
      },
    }),
    sizeSmall: ({ theme }) => ({
      '& .MuiBox-root': {
        fontSize: theme.typography.h6.fontSize,
      },
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default Checkbox;
