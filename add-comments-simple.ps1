#!/usr/bin/env pwsh

# Script pour ajouter des commentaires en français simple
Write-Host "Début de l'ajout de commentaires..." -ForegroundColor Green

# Fonction pour ajouter des commentaires basiques à un fichier C#
function Add-BasicCSharpComments {
    param([string]$FilePath)
    
    Write-Host "Traitement: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Ajouter des commentaires simples
    $content = $content -replace '(?m)^(\s*)(using\s+[^;]+;)', '$1// Importer une bibliothèque nécessaire$2$1$2'
    $content = $content -replace '(?m)^(\s*)(namespace\s+[^\s{]+)', '$1// Définir l''espace de noms pour organiser le code$2$1$2'
    $content = $content -replace '(?m)^(\s*)(public\s+class\s+\w+)', '$1// Classe publique qui définit un objet$2$1$2'
    $content = $content -replace '(?m)^(\s*)(\[Key\])', '$1// Marquer comme clé primaire de la base de données$2$1$2'
    $content = $content -replace '(?m)^(\s*)(\[Required\])', '$1// Cette propriété est obligatoire$2$1$2'
    $content = $content -replace '(?m)^(\s*)(\[HttpGet)', '$1// Méthode qui répond aux requêtes GET$2$1$2'
    $content = $content -replace '(?m)^(\s*)(\[HttpPost)', '$1// Méthode qui répond aux requêtes POST$2$1$2'
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Fonction pour ajouter des commentaires basiques à un fichier TS/JS
function Add-BasicTSComments {
    param([string]$FilePath)
    
    Write-Host "Traitement: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Ajouter des commentaires simples
    $content = $content -replace '(?m)^(\s*)(import\s+.*from)', '$1// Importer des fonctionnalités depuis un autre fichier$2$1$2'
    $content = $content -replace '(?m)^(\s*)(export\s+default)', '$1// Exporter comme élément principal de ce fichier$2$1$2'
    $content = $content -replace '(?m)^(\s*)(const\s+\w+\s*=)', '$1// Créer une constante$2$1$2'
    $content = $content -replace '(?m)^(\s*)(function\s+\w+)', '$1// Définir une fonction$2$1$2'
    $content = $content -replace '(?m)^(\s*)(interface\s+\w+)', '$1// Définir une interface TypeScript$2$1$2'
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Traiter les fichiers C# du backend
Write-Host "Traitement des fichiers C#..." -ForegroundColor Cyan
$csFiles = Get-ChildItem -Path "backend" -Recurse -Include "*.cs" | Where-Object { 
    $_.FullName -notmatch "obj|bin|Migrations" 
}

foreach ($file in $csFiles) {
    try {
        Add-BasicCSharpComments -FilePath $file.FullName
    }
    catch {
        Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Traiter les fichiers TS de l'admin
Write-Host "Traitement des fichiers admin..." -ForegroundColor Cyan
if (Test-Path "admin/src") {
    $adminFiles = Get-ChildItem -Path "admin/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
        $_.FullName -notmatch "node_modules" 
    }

    foreach ($file in $adminFiles) {
        try {
            Add-BasicTSComments -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Traiter les fichiers TS du client
Write-Host "Traitement des fichiers client..." -ForegroundColor Cyan
if (Test-Path "Client/src") {
    $clientFiles = Get-ChildItem -Path "Client/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
        $_.FullName -notmatch "node_modules" 
    }

    foreach ($file in $clientFiles) {
        try {
            Add-BasicTSComments -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Traiter les fichiers JS de l'app mobile
Write-Host "Traitement des fichiers mobile..." -ForegroundColor Cyan
if (Test-Path "LivreurApp/src") {
    $mobileFiles = Get-ChildItem -Path "LivreurApp/src" -Recurse -Include "*.js", "*.jsx"

    foreach ($file in $mobileFiles) {
        try {
            Add-BasicTSComments -FilePath $file.FullName
        }
        catch {
            Write-Host "Erreur avec $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Traiter App.jsx
if (Test-Path "LivreurApp/App.jsx") {
    try {
        Add-BasicTSComments -FilePath "LivreurApp/App.jsx"
    }
    catch {
        Write-Host "Erreur avec App.jsx: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Terminé!" -ForegroundColor Green
Write-Host "Fichiers C# traités: $($csFiles.Count)" -ForegroundColor Gray
