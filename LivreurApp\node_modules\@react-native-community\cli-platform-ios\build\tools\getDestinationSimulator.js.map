{"version": 3, "names": ["getDestinationSimulator", "args", "fallbackSimulators", "simulators", "JSON", "parse", "child_process", "execFileSync", "encoding", "error", "CLIError", "selectedSimulator", "reduce", "simulator", "fallback", "findMatchingSimulator", "udid"], "sources": ["../../src/tools/getDestinationSimulator.ts"], "sourcesContent": ["import child_process from 'child_process';\nimport {CLIError} from '@react-native-community/cli-tools';\nimport {Device} from '../types';\nimport findMatchingSimulator from './findMatchingSimulator';\n\ntype FlagsT = {\n  simulator?: string;\n  udid?: string;\n};\n\nexport function getDestinationSimulator(\n  args: FlagsT,\n  fallbackSimulators: string[] = [],\n) {\n  let simulators: {devices: {[index: string]: Array<Device>}};\n  try {\n    simulators = JSON.parse(\n      child_process.execFileSync(\n        'xcrun',\n        ['simctl', 'list', '--json', 'devices'],\n        {encoding: 'utf8'},\n      ),\n    );\n  } catch (error) {\n    throw new CLIError(\n      'Could not get the simulator list from Xcode. Please open Xcode and try running project directly from there to resolve the remaining issues.',\n      error as Error,\n    );\n  }\n\n  const selectedSimulator = fallbackSimulators.reduce((simulator, fallback) => {\n    return (\n      simulator || findMatchingSimulator(simulators, {simulator: fallback})\n    );\n  }, findMatchingSimulator(simulators, args));\n\n  if (!selectedSimulator) {\n    throw new CLIError(\n      `No simulator available with ${\n        args.simulator ? `name \"${args.simulator}\"` : `udid \"${args.udid}\"`\n      }`,\n    );\n  }\n  return selectedSimulator;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAA4D;AAOrD,SAASA,uBAAuB,CACrCC,IAAY,EACZC,kBAA4B,GAAG,EAAE,EACjC;EACA,IAAIC,UAAuD;EAC3D,IAAI;IACFA,UAAU,GAAGC,IAAI,CAACC,KAAK,CACrBC,wBAAa,CAACC,YAAY,CACxB,OAAO,EACP,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,EACvC;MAACC,QAAQ,EAAE;IAAM,CAAC,CACnB,CACF;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,6IAA6I,EAC7ID,KAAK,CACN;EACH;EAEA,MAAME,iBAAiB,GAAGT,kBAAkB,CAACU,MAAM,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;IAC3E,OACED,SAAS,IAAI,IAAAE,8BAAqB,EAACZ,UAAU,EAAE;MAACU,SAAS,EAAEC;IAAQ,CAAC,CAAC;EAEzE,CAAC,EAAE,IAAAC,8BAAqB,EAACZ,UAAU,EAAEF,IAAI,CAAC,CAAC;EAE3C,IAAI,CAACU,iBAAiB,EAAE;IACtB,MAAM,KAAID,oBAAQ,EACf,+BACCT,IAAI,CAACY,SAAS,GAAI,SAAQZ,IAAI,CAACY,SAAU,GAAE,GAAI,SAAQZ,IAAI,CAACe,IAAK,GAClE,EAAC,CACH;EACH;EACA,OAAOL,iBAAiB;AAC1B"}