﻿// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';
// Importer des fonctionnalités depuis un autre fichierimport customShadows fromimport customShadows from 'theme/shadows';

const Paper: Components<Omit<Theme, 'components'>>['MuiPaper'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      padding: theme.spacing(3.5),
      backgroundColor: theme.palette.info.lighter, // Même couleur pour tous les breakpoints
      borderRadius: theme.shape.borderRadius * 5,
      boxShadow: 'none',

      '&.MuiMenu-paper': {
        padding: 0,
        boxShadow: customShadows[0],
        borderRadius: theme.shape.borderRadius * 2.5,
      },
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default Paper;
