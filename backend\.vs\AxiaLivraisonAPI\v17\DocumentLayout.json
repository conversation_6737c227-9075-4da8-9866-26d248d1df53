{"Version": 1, "WorkspaceRootPath": "E:\\PFE\\Test\\AxiaLivraison\\backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\authentificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\authentificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\utilisateurcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\utilisateurcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\axialivraisonapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:axialivraisonapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\commandecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\commandecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\commande.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\commande.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\axialivraisonapi.csproj.backup.tmp||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:axialivraisonapi.csproj.backup.tmp||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\statistiquescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\statistiquescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\utilisateur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\utilisateur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\fournisseurcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\fournisseurcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\permissioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\permissioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\utilisateurpermission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\utilisateurpermission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\permission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\permission.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\rapport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\rapport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\locationupdatedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\locationupdatedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\commandedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\commandedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\fournisseur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\fournisseur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\fournisseurdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\fournisseurdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\models\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:models\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\updatestatutdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\updatestatutdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\services\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:services\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\commandedetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\commandedetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\utilisateurdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\utilisateurdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\utilisateuravecpermissionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\utilisateuravecpermissionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\axialivraisonapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:axialivraisonapi.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\assignerpermissionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\assignerpermissionsdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|e:\\pfe\\test\\axialivraison\\backend\\dto\\permissiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2CFF250A-B8FC-494D-8F97-0B33439DD30D}|AxiaLivraisonAPI.csproj|solutionrelative:dto\\permissiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 6, "Title": "AxiaLivraisonAPI.csproj.Backup.tmp", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj.Backup.tmp", "RelativeDocumentMoniker": "AxiaLivraisonAPI.csproj.Backup.tmp", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj.Backup.tmp", "RelativeToolTip": "AxiaLivraisonAPI.csproj.Backup.tmp", "ViewState": "AgIAAAUAAAAAAAAAAAAkwAYAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-04-18T10:06:06.524Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAABAAAAAAAAAAAAAAAIMAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-06T00:32:51.477Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthentificationController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\AuthentificationController.cs", "RelativeDocumentMoniker": "Controllers\\AuthentificationController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\AuthentificationController.cs", "RelativeToolTip": "Controllers\\AuthentificationController.cs", "ViewState": "AgIAACYAAAAAAAAAAAAwwEwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T11:53:42.834Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "StatistiquesController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\StatistiquesController.cs", "RelativeDocumentMoniker": "Controllers\\StatistiquesController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\StatistiquesController.cs", "RelativeToolTip": "Controllers\\StatistiquesController.cs", "ViewState": "AgIAAL8BAAAAAAAAAAAgwNIBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T10:50:11.176Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Utilisateur.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Utilisateur.cs", "RelativeDocumentMoniker": "Models\\Utilisateur.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Utilisateur.cs", "RelativeToolTip": "Models\\Utilisateur.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAYwCQAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-06T00:22:58.349Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "LoginDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\LoginDTO.cs", "RelativeDocumentMoniker": "DTO\\LoginDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\LoginDTO.cs", "RelativeToolTip": "DTO\\LoginDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-07T22:26:39.056Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "AxiaLivraisonAPI.csproj", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj", "RelativeDocumentMoniker": "AxiaLivraisonAPI.csproj", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj", "RelativeToolTip": "AxiaLivraisonAPI.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-03-05T23:06:58.373Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "UtilisateurController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\UtilisateurController.cs", "RelativeDocumentMoniker": "Controllers\\UtilisateurController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\UtilisateurController.cs", "RelativeToolTip": "Controllers\\UtilisateurController.cs", "ViewState": "AgIAACEAAAAAAAAAAAAYwCoAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-27T22:44:45.217Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CommandeController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\CommandeController.cs", "RelativeDocumentMoniker": "Controllers\\CommandeController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\CommandeController.cs", "RelativeToolTip": "Controllers\\CommandeController.cs", "ViewState": "AgIAAOIAAAAAAAAAAAAkwPIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-07T23:29:37.161Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Commande.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Commande.cs", "RelativeDocumentMoniker": "Models\\Commande.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Commande.cs", "RelativeToolTip": "Models\\Commande.cs", "ViewState": "AgIAADIAAAAAAAAAAAA4wD0AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-05T23:27:59.351Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "FournisseurController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\FournisseurController.cs", "RelativeDocumentMoniker": "Controllers\\FournisseurController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\FournisseurController.cs", "RelativeToolTip": "Controllers\\FournisseurController.cs", "ViewState": "AgIAALwAAAAAAAAAAAAAwNQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-22T00:37:25.381Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PermissionController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\PermissionController.cs", "RelativeDocumentMoniker": "Controllers\\PermissionController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\PermissionController.cs", "RelativeToolTip": "Controllers\\PermissionController.cs", "ViewState": "AgIAAHcAAAAAAAAAAAAAwIMAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T01:06:38.577Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "UtilisateurPermission.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\UtilisateurPermission.cs", "RelativeDocumentMoniker": "Models\\UtilisateurPermission.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\UtilisateurPermission.cs", "RelativeToolTip": "Models\\UtilisateurPermission.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAiwBQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-05T23:48:05.739Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "Permission.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Permission.cs", "RelativeDocumentMoniker": "Models\\Permission.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Permission.cs", "RelativeToolTip": "Models\\Permission.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAiwBIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-17T03:06:50.079Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "Rapport.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Rapport.cs", "RelativeDocumentMoniker": "Models\\Rapport.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Rapport.cs", "RelativeToolTip": "Models\\Rapport.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAuwBAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-06T19:03:26.358Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "LocationUpdateDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\LocationUpdateDTO.cs", "RelativeDocumentMoniker": "DTO\\LocationUpdateDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\LocationUpdateDTO.cs", "RelativeToolTip": "DTO\\LocationUpdateDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T13:53:08.601Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "CommandeDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\CommandeDTO.cs", "RelativeDocumentMoniker": "DTO\\CommandeDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\CommandeDTO.cs", "RelativeToolTip": "DTO\\CommandeDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-07T23:25:40.57Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Fournisseur.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Fournisseur.cs", "RelativeDocumentMoniker": "Models\\Fournisseur.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Fournisseur.cs", "RelativeToolTip": "Models\\Fournisseur.cs", "ViewState": "AgIAABAAAAAAAAAAAAAQwBgAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-05T23:44:35.719Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "FournisseurDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\FournisseurDTO.cs", "RelativeDocumentMoniker": "DTO\\FournisseurDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\FournisseurDTO.cs", "RelativeToolTip": "DTO\\FournisseurDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-05T23:44:29.475Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "Notification.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Notification.cs", "RelativeDocumentMoniker": "Models\\Notification.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Models\\Notification.cs", "RelativeToolTip": "Models\\Notification.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAiwB0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-02T09:05:27.165Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "NotificationController.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\NotificationController.cs", "RelativeDocumentMoniker": "Controllers\\NotificationController.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Controllers\\NotificationController.cs", "RelativeToolTip": "Controllers\\NotificationController.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAQwIsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-02T09:12:51.827Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "UpdateStatutDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UpdateStatutDTO.cs", "RelativeDocumentMoniker": "DTO\\UpdateStatutDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UpdateStatutDTO.cs", "RelativeToolTip": "DTO\\UpdateStatutDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:37:04.238Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "launchSettings.json", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAA8AAAAAAAAAAAAYwBYAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-22T12:13:35.285Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "EmailService.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Services\\EmailService.cs", "RelativeDocumentMoniker": "Services\\EmailService.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Services\\EmailService.cs", "RelativeToolTip": "Services\\EmailService.cs", "ViewState": "AgIAADMAAAAAAAAAAAAYwD0AAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T10:49:22.493Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "IEmailService.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Services\\IEmailService.cs", "RelativeDocumentMoniker": "Services\\IEmailService.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Services\\IEmailService.cs", "RelativeToolTip": "Services\\IEmailService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T10:53:42.082Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Data\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Data\\ApplicationDbContext.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\Data\\ApplicationDbContext.cs", "RelativeToolTip": "Data\\ApplicationDbContext.cs", "ViewState": "AgIAABgAAAAAAAAAAAAIwCgAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-05T23:50:03.714Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "appsettings.json", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-03-06T00:32:26.172Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "CommandeDetailsDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\CommandeDetailsDTO.cs", "RelativeDocumentMoniker": "DTO\\CommandeDetailsDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\CommandeDetailsDTO.cs", "RelativeToolTip": "DTO\\CommandeDetailsDTO.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAgAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-19T20:36:35.208Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "UtilisateurDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UtilisateurDTO.cs", "RelativeDocumentMoniker": "DTO\\UtilisateurDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UtilisateurDTO.cs", "RelativeToolTip": "DTO\\UtilisateurDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-07T21:58:26.813Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "UtilisateurAvecPermissionsDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UtilisateurAvecPermissionsDTO.cs", "RelativeDocumentMoniker": "DTO\\UtilisateurAvecPermissionsDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\UtilisateurAvecPermissionsDTO.cs", "RelativeToolTip": "DTO\\UtilisateurAvecPermissionsDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T03:13:01.31Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "AxiaLivraisonAPI.http", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.http", "RelativeDocumentMoniker": "AxiaLivraisonAPI.http", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.http", "RelativeToolTip": "AxiaLivraisonAPI.http", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-03-06T00:32:52.391Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "AssignerPermissionsDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\AssignerPermissionsDTO.cs", "RelativeDocumentMoniker": "DTO\\AssignerPermissionsDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\AssignerPermissionsDTO.cs", "RelativeToolTip": "DTO\\AssignerPermissionsDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T03:12:39.476Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "PermissionDTO.cs", "DocumentMoniker": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\PermissionDTO.cs", "RelativeDocumentMoniker": "DTO\\PermissionDTO.cs", "ToolTip": "E:\\PFE\\Test\\AxiaLivraison\\backend\\DTO\\PermissionDTO.cs", "RelativeToolTip": "DTO\\PermissionDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAYwAwAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T01:05:17.005Z"}]}]}]}