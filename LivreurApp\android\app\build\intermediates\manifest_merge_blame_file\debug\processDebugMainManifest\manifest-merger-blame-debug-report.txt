1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livreurapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:3:5-67
11-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:4:5-79
12-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:5:5-81
13-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
14-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:6:5-85
14-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:6:22-82
15    <uses-permission android:name="android.permission.CAMERA" />
15-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:7:5-65
15-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:7:22-62
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->[:react-native-community_netinfo] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
16-->[:react-native-community_netinfo] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
17    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
17-->[:react-native-community_netinfo] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
17-->[:react-native-community_netinfo] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->[:react-native-camera-kit] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-camera-kit\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-80
18-->[:react-native-camera-kit] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-camera-kit\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->[:react-native-camera-kit] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-camera-kit\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
19-->[:react-native-camera-kit] E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-camera-kit\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-78
20    <!--
21    This manifest file is used only by Gradle to configure debug-only capabilities
22    for React Native Apps.
23    -->
24    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
24-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\072209afb1c52d43fe8bcfe0160879d7\transformed\react-android-0.79.1-debug\AndroidManifest.xml:16:5-78
24-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\072209afb1c52d43fe8bcfe0160879d7\transformed\react-android-0.79.1-debug\AndroidManifest.xml:16:22-75
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.livreurapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.livreurapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:9:5-29:19
33        android:name="com.livreurapp.MainApplication"
33-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:10:7-38
34        android:allowBackup="false"
34-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:14:7-34
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:icon="@mipmap/ic_launcher"
38-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:12:7-41
39        android:label="@string/app_name"
39-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:11:7-39
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:13:7-52
41        android:supportsRtl="true"
41-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:16:7-33
42        android:theme="@style/AppTheme"
42-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:15:7-38
43        android:usesCleartextTraffic="true" >
43-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\debug\AndroidManifest.xml:6:9-44
44        <activity
44-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:17:7-28:18
45            android:name="com.livreurapp.MainActivity"
45-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:18:9-37
46            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
46-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:20:9-118
47            android:exported="true"
47-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:23:9-32
48            android:label="@string/app_name"
48-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:19:9-41
49            android:launchMode="singleTask"
49-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:21:9-40
50            android:windowSoftInputMode="adjustResize" >
50-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:22:9-51
51            <intent-filter>
51-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:24:9-27:25
52                <action android:name="android.intent.action.MAIN" />
52-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:25:13-65
52-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:25:21-62
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:26:13-73
54-->E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\AndroidManifest.xml:26:23-70
55            </intent-filter>
56        </activity>
57        <activity
57-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\072209afb1c52d43fe8bcfe0160879d7\transformed\react-android-0.79.1-debug\AndroidManifest.xml:19:9-21:40
58            android:name="com.facebook.react.devsupport.DevSettingsActivity"
58-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\072209afb1c52d43fe8bcfe0160879d7\transformed\react-android-0.79.1-debug\AndroidManifest.xml:20:13-77
59            android:exported="false" />
59-->[com.facebook.react:react-android:0.79.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\072209afb1c52d43fe8bcfe0160879d7\transformed\react-android-0.79.1-debug\AndroidManifest.xml:21:13-37
60
61        <service
61-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:26:9-35:19
62            android:name="androidx.camera.core.impl.MetadataHolderService"
62-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:27:13-75
63            android:enabled="false"
63-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:28:13-36
64            android:exported="false" >
64-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:29:13-37
65            <meta-data
65-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:32:13-34:89
66                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
66-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:33:17-103
67                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
67-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a6f5cc8f7e5f746ffff8991bac1fe48\transformed\camera-camera2-1.1.0\AndroidManifest.xml:34:17-86
68        </service>
69        <service
69-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
70            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
70-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
71            android:directBootAware="true"
71-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
72            android:exported="false" >
72-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
73            <meta-data
73-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
74                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
74-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
75                android:value="com.google.firebase.components.ComponentRegistrar" />
75-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5386fc66f6079bb372b8d05637ef21\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
76            <meta-data
76-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70ebf56c2b1ec3098af2b081aaa6b9de\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
77                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
77-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70ebf56c2b1ec3098af2b081aaa6b9de\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
78                android:value="com.google.firebase.components.ComponentRegistrar" />
78-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70ebf56c2b1ec3098af2b081aaa6b9de\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
79            <meta-data
79-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
80                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
80-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
81                android:value="com.google.firebase.components.ComponentRegistrar" />
81-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
82        </service>
83
84        <provider
84-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
85            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
85-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
86            android:authorities="com.livreurapp.mlkitinitprovider"
86-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
87            android:exported="false"
87-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
88            android:initOrder="99" />
88-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fed70709bac260c65db994c64726be47\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
89
90        <activity
90-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e85007912d4b927031f578e26c9bbb\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
91            android:name="com.google.android.gms.common.api.GoogleApiActivity"
91-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e85007912d4b927031f578e26c9bbb\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
92            android:exported="false"
92-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e85007912d4b927031f578e26c9bbb\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
93            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
93-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e85007912d4b927031f578e26c9bbb\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
94
95        <meta-data
95-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf7bafef873f44c654e32eb384e37ad\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
96            android:name="com.google.android.gms.version"
96-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf7bafef873f44c654e32eb384e37ad\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
97            android:value="@integer/google_play_services_version" />
97-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf7bafef873f44c654e32eb384e37ad\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
98
99        <provider
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
100            android:name="androidx.startup.InitializationProvider"
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
101            android:authorities="com.livreurapp.androidx-startup"
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
102            android:exported="false" >
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
103            <meta-data
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.emoji2.text.EmojiCompatInitializer"
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
105                android:value="androidx.startup" />
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
107-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
108                android:value="androidx.startup" />
108-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
109            <meta-data
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
111                android:value="androidx.startup" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
112        </provider>
113
114        <receiver
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
115            android:name="androidx.profileinstaller.ProfileInstallReceiver"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
116            android:directBootAware="false"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
117            android:enabled="true"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
118            android:exported="true"
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
119            android:permission="android.permission.DUMP" >
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
121                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
124                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
125            </intent-filter>
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
127                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
128            </intent-filter>
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
130                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
131            </intent-filter>
132        </receiver>
133
134        <service
134-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
135            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
135-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
136            android:exported="false" >
136-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
137            <meta-data
137-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
138                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
138-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
139                android:value="cct" />
139-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5ca271b9d646a4291841651427db3c\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
140        </service>
141        <service
141-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
142            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
142-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
143            android:exported="false"
143-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
144            android:permission="android.permission.BIND_JOB_SERVICE" >
144-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
145        </service>
146
147        <receiver
147-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
148            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
148-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
149            android:exported="false" />
149-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3950501cf9d72f637d45b3f07666a297\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
150
151        <meta-data
151-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
152            android:name="com.facebook.soloader.enabled"
152-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
153            android:value="false" />
153-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
154    </application>
155
156</manifest>
