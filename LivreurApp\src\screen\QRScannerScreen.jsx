// Importer des fonctionnalités depuis un autre fichierimport React, { useState, useEffect } fromimport React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Linking,
  PermissionsAndroid,
  Platform,
} from 'react-native';
// Importer des fonctionnalités depuis un autre fichierimport { useNavigation, useRoute } fromimport { useNavigation, useRoute } from '@react-navigation/native';
// Importer des fonctionnalités depuis un autre fichierimport AntDesign fromimport AntDesign from 'react-native-vector-icons/AntDesign';
// Importer des fonctionnalités depuis un autre fichierimport MaterialIcons fromimport MaterialIcons from 'react-native-vector-icons/MaterialIcons';

// Import the real camera
// Importer des fonctionnalités depuis un autre fichierimport { Camera, CameraType } fromimport { Camera, CameraType } from 'react-native-camera-kit';
// Importer des fonctionnalités depuis un autre fichierimport { colors } fromimport { colors } from '../theme';

// API Base URL
// Créer une constanteconst API_BASE_URL =const API_BASE_URL = Platform.select({
  android: 'http://*************:5283', // Remplacez par l'adresse IP de votre serveur
  ios: 'http://localhost:5283',
  default: 'http://localhost:5283',
});

// Créer une constanteconst QRScannerScreen =
const QRScannerScreen = () => {
  // Créer une constanteconst navigation =  const navigation = useNavigation();
  // Créer une constanteconst route =  const route = useRoute();
  const { user } = route.params || {};

  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [scannedData, setScannedData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [commandeDetails, setCommandeDetails] = useState(null);
  const [userCommandes, setUserCommandes] = useState([]);
  const [commandeFound, setCommandeFound] = useState(false);
  const [foundCommande, setFoundCommande] = useState(null);

  // Nous pouvons garder cette fonction mais ne plus l'appeler
  // ou la supprimer complètement si vous préférez
  // Créer une constanteconst fetchCommandeDetails =  const fetchCommandeDetails = async (commandId) => {
    // Cette fonction n'est plus utilisée
    console.log("Cette fonction n'est plus utilisée");
  };

  // Fonction pour récupérer les commandes de l'utilisateur
  // Créer une constanteconst fetchUserCommandes =  const fetchUserCommandes = async () => {
    if (!user?.id) {
      console.log('Aucun ID utilisateur disponible');
      return;
    }

    try {
      setLoading(true);
      console.log(`Récupération des commandes pour l'utilisateur ID: ${user.id}`);
      // Créer une constanteconst response =      const response = await fetch(
        `${API_BASE_URL}/api/Commandes/livreur/${user.id}`,
      );

      if (!response.ok) {
        console.error(`Erreur HTTP: ${response.status}`);
        // Créer une constanteconst errorText =        const errorText = await response.text();
        console.error('Réponse d\'erreur:', errorText);
        return;
      }

      // Créer une constanteconst data =
      const data = await response.json();
      console.log('Commandes récupérées:', data);

      if (Array.isArray(data)) {
        setUserCommandes(data);
      } else {
        console.error('Format de données inattendu:', data);
        setUserCommandes([]);
      }
    } catch (err) {
      console.error('Erreur de connexion au serveur:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour vérifier si une commande existe dans la liste de l'utilisateur
  // Créer une constanteconst checkCommandeExists =  const checkCommandeExists = (commandId) => {
    if (!userCommandes || userCommandes.length === 0) {
      console.log('Aucune commande disponible pour cet utilisateur');
      setCommandeFound(false);
      setFoundCommande(null);
      return false;
    }

    // Essayer de parser l'ID si c'est un objet JSON
    let parsedId = commandId;
    try {
      // Vérifier si l'ID est une chaîne JSON
      if (typeof commandId === 'string' && (commandId.startsWith('{') || commandId.includes('id'))) {
        // Créer une constanteconst jsonObj =        const jsonObj = JSON.parse(commandId);
        if (jsonObj && jsonObj.id) {
          parsedId = jsonObj.id;
          console.log('ID extrait du JSON:', parsedId);
        }
      }
    } catch (e) {
      console.log('Pas un objet JSON valide, utilisation de l\'ID tel quel');
    }

    // Supprimer tout préfixe et convertir en nombre
    let numericId;
    try {
      // Si c'est encore un objet avec une propriété id
      if (parsedId && typeof parsedId === 'object' && parsedId.id) {
        numericId = parseInt(String(parsedId.id).trim());
      } else {
        numericId = parseInt(String(parsedId).replace('CMD-', '').trim());
      }
      console.log('ID converti en nombre:', numericId);
    } catch (e) {
      console.error('Erreur de conversion de l\'ID:', e);
      setCommandeFound(false);
      setFoundCommande(null);
      return false;
    }

    // Si la conversion a échoué (NaN)
    if (isNaN(numericId)) {
      console.error('ID invalide après conversion:', parsedId);
      setCommandeFound(false);
      setFoundCommande(null);
      return false;
    }

    console.log('Recherche de l\'ID:', numericId);
    console.log('Liste des commandes:', userCommandes);

    // Utiliser une comparaison numérique
    // Créer une constanteconst commande =    const commande = userCommandes.find(cmd => {
      // Créer une constanteconst cmdId =      const cmdId = parseInt(String(cmd.id).trim());
      console.log(`Comparaison: ${cmdId} === ${numericId} => ${cmdId === numericId}`);
      return cmdId === numericId;
    });

    if (commande) {
      console.log('Commande trouvée:', commande);
      setCommandeFound(true);
      setFoundCommande(commande);
      return true;
    } else {
      console.log('Commande non trouvée dans la liste');
      setCommandeFound(false);
      setFoundCommande(null);
      return false;
    }
  };

  // Demander explicitement les permissions de la caméra et charger les commandes
  useEffect(() => {
    // Créer une constanteconst requestCameraPermission =    const requestCameraPermission = async () => {
      try {
        if (Platform.OS === 'android') {
          // Créer une constanteconst granted =          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
              title: 'Permission de caméra',
              message: 'L\'application a besoin d\'accéder à votre caméra pour scanner les QR codes',
              buttonNeutral: 'Demander plus tard',
              buttonNegative: 'Annuler',
              buttonPositive: 'OK',
            },
          );
          setHasPermission(granted === PermissionsAndroid.RESULTS.GRANTED);
          console.log('Permission caméra:', granted);
        } else {
          // Pour iOS
          setHasPermission(true);
        }
      } catch (err) {
        console.warn('Erreur de permission:', err);
        setHasPermission(false);
      }
    };

    requestCameraPermission();
    fetchUserCommandes();
  }, [user]);

  // Créer une constanteconst onReadCode =
  const onReadCode = (event) => {
    if (!scanned) {
      setScanned(true);
      // Créer une constanteconst qrData =      const qrData = event.nativeEvent.codeStringValue;
      setScannedData(qrData);

      console.log('QR Code scanné:', qrData);

      // Accepter tous les formats de QR code et extraire l'ID
      let commandId = qrData;

      // Si le format est CMD-123456, extraire juste le numéro
      if (qrData && qrData.startsWith('CMD-')) {
        commandId = qrData.replace('CMD-', '');
        console.log('ID extrait après suppression du préfixe CMD-:', commandId);
      }

      // Vérifier si cette commande existe dans la liste de l'utilisateur
      checkCommandeExists(commandId);
    }
  };

  // Créer une constanteconst resetScanner =
  const resetScanner = () => {
    setScanned(false);
    setScannedData(null);
  };

  if (hasPermission === null) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Demande d'autorisation de la caméra...</Text>
      </SafeAreaView>
    );
  }

  // Fonction pour demander à nouveau les permissions
  // Créer une constanteconst requestPermission =  const requestPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        // Créer une constanteconst granted =        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Permission de caméra',
            message: 'L\'application a besoin d\'accéder à votre caméra pour scanner les QR codes',
            buttonNeutral: 'Demander plus tard',
            buttonNegative: 'Annuler',
            buttonPositive: 'OK',
          },
        );
        setHasPermission(granted === PermissionsAndroid.RESULTS.GRANTED);
      } else {
        // Pour iOS, ouvrir les paramètres
        Linking.openSettings();
      }
    } catch (err) {
      console.warn(err);
    }
  };

  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          L'accès à la caméra est nécessaire pour scanner les QR codes.
        </Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>Autoriser la caméra</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.permissionButton, { marginTop: 10, backgroundColor: '#262626' }]}
          onPress={() => navigation.goBack()}>
          <Text style={styles.permissionButtonText}>Retour</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}>
          <AntDesign name="arrowleft" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scanner un QR Code</Text>
        <View style={{width: 40}} />
      </View>

      <View style={styles.scannerContainer}>
        {!scanned ? (
          <Camera
            style={styles.scanner}
            cameraType={CameraType.Back}
            flashMode="auto"
            scanBarcode={true}
            onReadCode={onReadCode}
            showFrame={true}
            laserColor={colors.primary.main}
            frameColor={colors.primary.main}
          />
        ) : (
          <View style={styles.resultContainer}>
            <View style={styles.resultBox}>
              {commandeFound ? (
                // Si la commande existe, afficher l'ID et les boutons d'action
                <>
                  <Text style={styles.resultTitle}>CMD - {foundCommande.id}</Text>


                  {loading ? (
                    <ActivityIndicator size="large" color={colors.primary.main} style={{marginVertical: 20}} />
                  ) : (
                    <View style={styles.actionButtonsContainer}>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => navigation.navigate('DetailCommande', {commande: foundCommande})}>
                        <Text style={styles.actionButtonText}>Détails</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.actionButton, {backgroundColor: '#3B82F6'}]}
                        onPress={() => {
                          navigation.navigate('Accueil', {
                            openStatusForm: true,
                            selectedCommande: foundCommande,
                            user: user
                          });
                        }}>
                        <Text style={styles.actionButtonText}>Modifier statut</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.actionButton, {backgroundColor: '#262626'}]}
                        onPress={resetScanner}>
                        <Text style={styles.actionButtonText}>Retour</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              ) : (
                // Si la commande n'existe pas, afficher seulement l'alerte et le bouton retour
                <>

                  {loading ? (
                    <ActivityIndicator size="large" color="#623AA2" style={{marginVertical: 20}} />
                  ) : (
                    <>
                      <View style={styles.notFoundContainer}>
                        <MaterialIcons name="error-outline" size={24} color="#F87171" />
                        <Text style={styles.notFoundText}>
                          Cette commande n'existe pas dans votre liste
                        </Text>
                      </View>

                      <TouchableOpacity
                        style={[styles.actionButton, {backgroundColor: '#262626', marginTop: 15}]}
                        onPress={resetScanner}>
                        <Text style={styles.actionButtonText}>Retour</Text>
                      </TouchableOpacity>
                    </>
                  )}
                </>
              )}
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

// Créer une constanteconst styles =
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 10,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scannerContainer: {
    flex: 1,
  },
  scanner: {
    flex: 1,
    height: '100%',
  },

  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  resultBox: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#262626',
  },
  resultData: {
    fontSize: 16,
    color: '#262626',
    marginBottom: 20,
    textAlign: 'center',
  },
  foundContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6F4EA',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    width: '80%',
  },
  foundText: {
    color: '#1E8E3E',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  notFoundContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    width: '100%', // Changé de 80% à 100% pour correspondre à la largeur du bouton
  },
  notFoundText: {
    color: '#B91C1C',
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  scanAgainButton: {
    backgroundColor: '#FFAA80',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    width: '80%',
  },
  scanAgainButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#262626',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 20,
  },
  permissionText: {
    fontSize: 16,
    color: '#262626',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#FFAA80',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  actionButtonsContainer: {
    width: '100%',
    marginTop: 20,
  },
  actionButton: {
    backgroundColor: '#FFAA80',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 10,
    width: '100%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
});

// Exporter comme élément principal de ce fichierexport default
export default QRScannerScreen;

















