// Importer une bibliothèque nécessaireusing System;using System;
// Importer une bibliothèque nécessaireusing System.Collections.Generic;using System.Collections.Generic;
// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;
// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations.Schema;using System.ComponentModel.DataAnnotations.Schema;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Models
namespace AxiaLivraisonAPI.Models
{
    // Classe publique qui définit un objetpublic class Fournisseur    public class Fournisseur
    {
        // Marquer comme clé primaire de la base de données[Key]        [Key]
        public int Id { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(255)]
        public string Nom { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public string Adresse { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(20)]
        public string Telephone { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(50)]
        public string Identifiant { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [DataType(DataType.DateTime)]
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;

        public ICollection<Commande> Commandes { get; set; }
    }
}