// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.DTO
namespace AxiaLivraisonAPI.DTO
{
    // Classe publique qui définit un objetpublic class RefreshTokenDTO    public class RefreshTokenDTO
    {
        // Cette propriété est obligatoire[Required]        [Required]
        public string RefreshToken { get; set; }
    }
}
