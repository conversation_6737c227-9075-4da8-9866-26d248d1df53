{"name": "@react-native/codegen", "version": "0.72.8", "description": "⚛️ Code generation tools for React Native", "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/react-native-codegen", "repository": {"type": "git", "url": "**************:facebook/react-native.git", "directory": "packages/react-native-codegen"}, "scripts": {"build": "yarn clean && node scripts/build.js --verbose", "clean": "<PERSON><PERSON><PERSON> lib", "prepare": "yarn run build"}, "license": "MIT", "files": ["lib"], "dependencies": {"@babel/parser": "^7.20.0", "flow-parser": "^0.206.0", "glob": "^7.1.1", "invariant": "^2.2.4", "jscodeshift": "^0.14.0", "mkdirp": "^0.5.1", "nullthrows": "^1.1.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.0", "@babel/plugin-proposal-object-rest-spread": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.20.0", "@babel/plugin-syntax-dynamic-import": "^7.8.0", "@babel/plugin-transform-async-to-generator": "^7.20.0", "@babel/plugin-transform-destructuring": "^7.20.0", "@babel/plugin-transform-flow-strip-types": "^7.20.0", "@babel/preset-env": "^7.20.0", "chalk": "^4.0.0", "micromatch": "^4.0.4", "prettier": "^2.4.1", "rimraf": "^3.0.2"}, "peerDependencies": {"@babel/preset-env": "^7.1.6"}}