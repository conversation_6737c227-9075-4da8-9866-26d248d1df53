﻿// Importer des fonctionnalités depuis un autre fichierimport { useEffect } fromimport { useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useNavigate } fromimport { useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport { rootPaths } fromimport { rootPaths } from 'routes/paths';
// Importer des fonctionnalités depuis un autre fichierimport { authService } fromimport { authService } from 'services/authService';

// Créer une constanteconst Logout =
const Logout = () => {
  // Créer une constanteconst navigate =  const navigate = useNavigate();

  useEffect(() => {
    // Créer une constanteconst performLogout =    const performLogout = async () => {
      try {
        await authService.logout();
      } catch (error) {
        console.error('Error during logout:', error);
      } finally {
        navigate(rootPaths.root);
      }
    };

    performLogout();
  }, [navigate]);

  return null;
};

// Exporter comme élément principal de ce fichierexport default
export default Logout;
