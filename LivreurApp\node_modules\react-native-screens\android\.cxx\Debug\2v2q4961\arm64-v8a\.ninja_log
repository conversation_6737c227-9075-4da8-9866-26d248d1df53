# ninja log v5
0	3075	7718876727497069	CMakeFiles/rnscreens.dir/E_/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	46161102a26fd475
14	3489	7718876731776877	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	571ad1f765c71aa5
6	5851	7718876755473781	CMakeFiles/rnscreens.dir/E_/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	4f3243cf3673db15
28	5953	7718876756516213	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	78966ef5a9ac08ed
21	8510	7718876781908563	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	4fab457a656f0932
8511	8689	7718876783834431	../../../../build/intermediates/cxx/Debug/2v2q4961/obj/arm64-v8a/librnscreens.so	3a081c410137e053
