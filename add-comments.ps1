#!/usr/bin/env pwsh

# Script pour ajouter des commentaires en français simple à tous les fichiers de code du projet AxiaLivraison
Write-Host "🚀 Début de l'ajout de commentaires en français simple..." -ForegroundColor Green

# Fonction pour ajouter des commentaires à un fichier C#
function Add-CSharpComments {
    param([string]$FilePath)
    
    Write-Host "📝 Traitement du fichier C#: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Patterns de commentaires pour C#
    $patterns = @{
        'using\s+([^;]+);' = '// Importer la bibliothèque $1 pour utiliser ses fonctionnalités'
        'namespace\s+([^\s{]+)' = '// Définir l''espace de noms $1 pour organiser le code'
        'public\s+class\s+(\w+)' = '// Classe publique $1 qui peut être utilisée par d''autres parties du code'
        'private\s+readonly\s+(\w+)\s+(\w+);' = '// Variable privée en lecture seule de type $1 nommée $2'
        'public\s+(\w+)\s+(\w+)\s*{' = '// Propriété publique $2 de type $1'
        '\[Key\]' = '// Marquer cette propriété comme clé primaire de la base de données'
        '\[Required\]' = '// Cette propriété est obligatoire (ne peut pas être vide)'
        '\[HttpGet' = '// Méthode qui répond aux requêtes HTTP GET'
        '\[HttpPost' = '// Méthode qui répond aux requêtes HTTP POST'
        '\[HttpPut' = '// Méthode qui répond aux requêtes HTTP PUT'
        '\[HttpDelete' = '// Méthode qui répond aux requêtes HTTP DELETE'
        'public\s+async\s+Task' = '// Méthode publique asynchrone qui retourne une tâche'
        'private\s+async\s+Task' = '// Méthode privée asynchrone qui retourne une tâche'
        'await\s+' = '// Attendre que l''opération asynchrone se termine'
        'return\s+Ok\(' = '// Retourner une réponse HTTP 200 (succès) avec les données'
        'return\s+BadRequest\(' = '// Retourner une réponse HTTP 400 (erreur de requête)'
        'return\s+Unauthorized\(' = '// Retourner une réponse HTTP 401 (non autorisé)'
        'return\s+NotFound\(' = '// Retourner une réponse HTTP 404 (non trouvé)'
    }
    
    # Appliquer les patterns
    foreach ($pattern in $patterns.Keys) {
        $comment = $patterns[$pattern]
        $content = $content -replace "(?m)^(\s*)($pattern)", "`$1$comment`n`$1`$2"
    }
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Fonction pour ajouter des commentaires à un fichier TypeScript/JavaScript
function Add-TSJSComments {
    param([string]$FilePath)
    
    Write-Host "📝 Traitement du fichier TS/JS: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Patterns de commentaires pour TypeScript/JavaScript
    $patterns = @{
        'import\s+.*\s+from\s+[''"]([^''"]+)[''"]' = '// Importer des fonctionnalités depuis le module $1'
        'export\s+default\s+(\w+)' = '// Exporter $1 comme export par défaut de ce fichier'
        'export\s+const\s+(\w+)' = '// Exporter la constante $1 pour qu''elle soit utilisable ailleurs'
        'const\s+(\w+)\s*=' = '// Créer une constante nommée $1'
        'let\s+(\w+)\s*=' = '// Créer une variable modifiable nommée $1'
        'function\s+(\w+)\s*\(' = '// Définir une fonction nommée $1'
        'const\s+(\w+)\s*=\s*\(' = '// Créer une fonction fléchée nommée $1'
        'interface\s+(\w+)' = '// Définir une interface TypeScript nommée $1 pour typer les objets'
        'type\s+(\w+)' = '// Définir un type TypeScript nommé $1'
        'useState\(' = '// Hook React pour gérer l''état local du composant'
        'useEffect\(' = '// Hook React pour exécuter du code lors du cycle de vie du composant'
        'return\s*\(' = '// Retourner le JSX à afficher dans le composant React'
        'onClick=' = '// Gestionnaire d''événement pour les clics de souris'
        'onChange=' = '// Gestionnaire d''événement pour les changements de valeur'
        'onSubmit=' = '// Gestionnaire d''événement pour la soumission de formulaire'
    }
    
    # Appliquer les patterns
    foreach ($pattern in $patterns.Keys) {
        $comment = $patterns[$pattern]
        $content = $content -replace "(?m)^(\s*)($pattern)", "`$1$comment`n`$1`$2"
    }
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Fonction pour traiter les fichiers React Native (JSX)
function Add-ReactNativeComments {
    param([string]$FilePath)
    
    Write-Host "📝 Traitement du fichier React Native: $FilePath" -ForegroundColor Yellow
    
    $content = Get-Content -Path $FilePath -Raw
    if (-not $content) { return }
    
    # Patterns spécifiques à React Native
    $patterns = @{
        'import.*from\s+''react-native''' = '// Importer des composants React Native pour l''interface mobile'
        'import.*from\s+''@react-navigation''' = '// Importer des composants pour la navigation entre écrans'
        'const\s+(\w+)\s*=\s*createNativeStackNavigator' = '// Créer un navigateur en pile pour gérer les écrans'
        'StyleSheet\.create' = '// Créer une feuille de styles pour styliser les composants'
        '<View' = '// Composant conteneur équivalent à une div en HTML'
        '<Text' = '// Composant pour afficher du texte'
        '<TouchableOpacity' = '// Composant bouton qui réagit aux touches'
        '<ScrollView' = '// Composant qui permet de faire défiler le contenu'
        '<FlatList' = '// Composant pour afficher une liste d''éléments de manière optimisée'
        'navigation\.navigate' = '// Naviguer vers un autre écran'
        'AsyncStorage' = '// Stockage local persistant sur l''appareil mobile'
    }
    
    # Appliquer les patterns
    foreach ($pattern in $patterns.Keys) {
        $comment = $patterns[$pattern]
        $content = $content -replace "(?m)^(\s*)($pattern)", "`$1$comment`n`$1`$2"
    }
    
    Set-Content -Path $FilePath -Value $content -NoNewline
}

# Traiter tous les fichiers C# du backend
Write-Host "`n🔧 Traitement des fichiers C# du backend..." -ForegroundColor Cyan
$csFiles = Get-ChildItem -Path "backend" -Recurse -Include "*.cs" | Where-Object { 
    $_.FullName -notmatch "\\obj\\|\\bin\\|Migrations" 
}

foreach ($file in $csFiles) {
    Add-CSharpComments -FilePath $file.FullName
}

# Traiter tous les fichiers TypeScript/JavaScript de l'admin
Write-Host "`n⚛️ Traitement des fichiers TypeScript de l'admin..." -ForegroundColor Cyan
$adminFiles = Get-ChildItem -Path "admin/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
    $_.FullName -notmatch "node_modules|\.d\.ts$" 
}

foreach ($file in $adminFiles) {
    Add-TSJSComments -FilePath $file.FullName
}

# Traiter tous les fichiers TypeScript/JavaScript du client
Write-Host "`n🌐 Traitement des fichiers TypeScript du client..." -ForegroundColor Cyan
$clientFiles = Get-ChildItem -Path "Client/src" -Recurse -Include "*.ts", "*.tsx" | Where-Object { 
    $_.FullName -notmatch "node_modules|\.d\.ts$" 
}

foreach ($file in $clientFiles) {
    Add-TSJSComments -FilePath $file.FullName
}

# Traiter tous les fichiers JavaScript/JSX de l'app mobile
Write-Host "`n📱 Traitement des fichiers React Native de l'app mobile..." -ForegroundColor Cyan
$mobileFiles = Get-ChildItem -Path "LivreurApp/src" -Recurse -Include "*.js", "*.jsx" | Where-Object { 
    $_.FullName -notmatch "node_modules" 
}

# Traiter aussi le fichier App.jsx principal
$appFile = Get-ChildItem -Path "LivreurApp" -Include "App.jsx"
if ($appFile) {
    Add-ReactNativeComments -FilePath $appFile.FullName
}

foreach ($file in $mobileFiles) {
    Add-ReactNativeComments -FilePath $file.FullName
}

Write-Host "`n✅ Ajout de commentaires terminé pour tous les fichiers!" -ForegroundColor Green
Write-Host "📊 Résumé:" -ForegroundColor White
Write-Host "   - Fichiers C# traités: $($csFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers Admin traités: $($adminFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers Client traités: $($clientFiles.Count)" -ForegroundColor Gray
Write-Host "   - Fichiers Mobile traités: $($mobileFiles.Count + 1)" -ForegroundColor Gray
