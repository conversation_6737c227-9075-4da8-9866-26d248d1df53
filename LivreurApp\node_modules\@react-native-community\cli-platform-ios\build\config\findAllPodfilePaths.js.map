{"version": 3, "names": ["GLOB_EXCLUDE_PATTERN", "findAllPodfilePaths", "cwd", "glob", "sync", "ignore"], "sources": ["../../src/config/findAllPodfilePaths.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport glob from 'glob';\n\n// These folders will be excluded from search to speed it up\nconst GLOB_EXCLUDE_PATTERN = ['**/@(Pods|node_modules|Carthage)/**'];\n\nexport default function findAllPodfilePaths(cwd: string) {\n  return glob.sync('**/Podfile', {\n    cwd,\n    ignore: GLOB_EXCLUDE_PATTERN,\n  });\n}\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAPxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA,MAAMA,oBAAoB,GAAG,CAAC,qCAAqC,CAAC;AAErD,SAASC,mBAAmB,CAACC,GAAW,EAAE;EACvD,OAAOC,eAAI,CAACC,IAAI,CAAC,YAAY,EAAE;IAC7BF,GAAG;IACHG,MAAM,EAAEL;EACV,CAAC,CAAC;AACJ"}