﻿// Importer des fonctionnalités depuis un autre fichierimport { useMemo } fromimport { useMemo } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps, useTheme } fromimport { SxProps, useTheme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport * as echarts fromimport * as echarts from 'echarts/core';
// Importer des fonctionnalités depuis un autre fichierimport { BarChart } fromimport { BarChart } from 'echarts/charts';
// Importer des fonctionnalités depuis un autre fichierimport ReactEchart fromimport ReactEchart from 'components/base/ReactEchart';
import {
  TooltipComponent,
  GridComponent,
  AxisPointerComponent,
  LegendComponent,
} from 'echarts/components';
// Importer des fonctionnalités depuis un autre fichierimport { CanvasRenderer } fromimport { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  BarChart,
  TooltipComponent,
  GridComponent,
  AxisPointerComponent,
  LegendComponent,
  CanvasRenderer,
]);

// Définir une interface TypeScriptinterface SuiviCommandesChartProps
interface SuiviCommandesChartProps {
  data: number[];
  labels: string[];
  sx?: SxProps;
}

// Créer une constanteconst SuiviCommandesChart =
const SuiviCommandesChart = ({ data, labels, ...rest }: SuiviCommandesChartProps) => {
  // Créer une constanteconst theme =  const theme = useTheme();

  // Couleurs pour différents statuts
  // Créer une constanteconst getColor =  const getColor = (statut: string) => {
    switch (statut.toLowerCase()) {
      case 'en attente':
        return '#FFA500'; // Orange
      case 'en cours':
        return '#4318FF'; // Bleu (primary)
      case 'livrée':
        return '#05CD99'; // Vert (success)
      case 'annulée':
        return '#FF5252'; // Rouge (error)
      default:
        return theme.palette.primary.main;
    }
  };

  // Créer une constanteconst option =
  const option = useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} commandes',
      },
      grid: {
        top: '22%',
        left: '5%',
        right: '5%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: labels,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            margin: 20,
            fontWeight: 500,
            color: theme.palette.text.disabled,
            fontSize: theme.typography.caption.fontSize,
            fontFamily: theme.typography.fontFamily,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          minInterval: 1,
          axisLabel: {
            formatter: '{value}',
            color: theme.palette.text.disabled,
          },
          splitLine: {
            lineStyle: {
              color: theme.palette.divider,
            },
          },
        },
      ],
      series: [
        {
          name: 'Commandes',
          type: 'bar',
          barWidth: '40%',
          showBackground: false,
          data: data.map((value, index) => ({
            value,
            itemStyle: {
              color: getColor(labels[index]),
              borderRadius: [10, 10, 0, 0],
            },
          })),
        },
      ],
    }),
    [theme, data, labels],
  );

  return <ReactEchart echarts={echarts} option={option} {...rest} />;
};

// Exporter comme élément principal de ce fichierexport default
export default SuiviCommandesChart;
