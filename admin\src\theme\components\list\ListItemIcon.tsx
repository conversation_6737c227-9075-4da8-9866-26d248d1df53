// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const ListItemIcon: Components<Omit<Theme, 'components'>>['MuiListItemIcon'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      minWidth: '0 !important',
      marginRight: theme.spacing(1.75),
      color: theme.palette.text.disabled,
      fontSize: theme.typography.h5.fontSize,
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default ListItemIcon;
