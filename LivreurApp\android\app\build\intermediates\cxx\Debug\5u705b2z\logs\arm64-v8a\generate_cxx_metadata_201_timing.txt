# C/C++ build system timings
generate_cxx_metadata
  [gap of 68ms]
  create-invalidation-state 262ms
  generate-prefab-packages
    [gap of 17ms]
    exec-prefab 1471ms
    [gap of 110ms]
  generate-prefab-packages completed in 1598ms
  execute-generate-process
    exec-configure 1902ms
    [gap of 264ms]
  execute-generate-process completed in 2169ms
  [gap of 94ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 4223ms

