﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport SuiviCommandesChart fromimport SuiviCommandesChart from './SuiviCommandesChart';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

// Définir une interface TypeScriptinterface StatutCommande
interface StatutCommande {
  statut: string;
  nombre: number;
}

// Définir une interface TypeScriptinterface TotalCommandes
interface TotalCommandes {
  total: number;
  enCours: number;
  pourcentageChangement: number;
}

// Définir une interface TypeScriptinterface SuiviCommandesProps
interface SuiviCommandesProps {
  sx?: SxProps;
}

// Créer une constanteconst SuiviCommandes =
const SuiviCommandes = ({ sx }: SuiviCommandesProps) => {
  const [statutsCommandes, setStatutsCommandes] = useState<StatutCommande[]>([]);
  const [totalCommandes, setTotalCommandes] = useState<TotalCommandes | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchData =    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [statutsResponse, totalResponse] = await Promise.all([
          axios.get(`${API_URL}/api/statistiques/commandes-par-statut`),
          axios.get(`${API_URL}/api/statistiques/total-commandes`),
        ]);

        setStatutsCommandes(statutsResponse.data);
        setTotalCommandes(totalResponse.data);
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques:', err);
        setError('Impossible de charger les statistiques');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Préparer les données pour le graphique
  // Créer une constanteconst chartData =  const chartData = statutsCommandes.map((item) => item.nombre);
  // Créer une constanteconst chartLabels =  const chartLabels = statutsCommandes.map((item) => item.statut);

  return (
    <Paper sx={{ height: 350, ...sx }}>
      <Stack alignItems="flex-start" justifyContent="space-between">
        <div>
          <Typography variant="body2" color="text.disabled" fontWeight={500}>
            Suivi des Commandes
          </Typography>
          <Typography mt={0.5} variant="h2">
            {loading ? '...' : totalCommandes?.enCours || 0}{' '}
            <Typography component="span" variant="body2" color="text.disabled" fontWeight={500}>
              En cours
            </Typography>
          </Typography>
        </div>
        {totalCommandes && (
          <Stack alignItems="center" spacing={0.25}>
            <IconifyIcon
              icon={
                totalCommandes.pourcentageChangement >= 0
                  ? 'ic:baseline-arrow-drop-up'
                  : 'ic:baseline-arrow-drop-down'
              }
              color={totalCommandes.pourcentageChangement >= 0 ? 'success.main' : 'error.main'}
              fontSize="h6.fontSize"
            />
            <Typography
              variant="body2"
              color={totalCommandes.pourcentageChangement >= 0 ? 'success.main' : 'error.main'}
              fontWeight={700}
            >
              {totalCommandes.pourcentageChangement >= 0 ? '+' : ''}
              {totalCommandes.pourcentageChangement}%
            </Typography>
          </Stack>
        )}
      </Stack>

      {loading ? (
        <Typography variant="body1" sx={{ mt: 5, textAlign: 'center' }}>
          Chargement des données...
        </Typography>
      ) : error ? (
        <Typography variant="body1" color="error" sx={{ mt: 5, textAlign: 'center' }}>
          {error}
        </Typography>
      ) : (
        <SuiviCommandesChart
          data={chartData}
          labels={chartLabels}
          sx={{ height: '230px !important' }}
        />
      )}
    </Paper>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default SuiviCommandes;
