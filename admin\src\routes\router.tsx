// Importer des fonctionnalités depuis un autre fichierimport { Suspense, lazy } fromimport { Suspense, lazy } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { Outlet, createBrowserRouter } fromimport { Outlet, createBrowserRouter } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport paths, { rootPaths } fromimport paths, { rootPaths } from './paths';

// Créer une constanteconst App =
const App = lazy(() => import('App'));
// Créer une constanteconst MainLayout =const MainLayout = lazy(() => import('layouts/main-layout'));
// Créer une constanteconst AuthLayout =const AuthLayout = lazy(() => import('layouts/auth-layout'));
// Créer une constanteconst AuthGuard =const AuthGuard = lazy(() => import('components/auth/AuthGuard'));
// Créer une constanteconst Dashboard =const Dashboard = lazy(() => import('pages/dashboard/Dashboard'));
// Créer une constanteconst SignIn =const SignIn = lazy(() => import('pages/authentication/SignIn'));
// Créer une constanteconst Logout =const Logout = lazy(() => import('pages/authentication/Logout'));
// Créer une constanteconst Page404 =const Page404 = lazy(() => import('pages/errors/Page404'));
// Créer une constanteconst TrackingPage =const TrackingPage = lazy(() => import('pages/tracking/TrackingPage'));

// Créer une constanteconst GestionUtilisateur =
const GestionUtilisateur = lazy(() => import('pages/utilisateur/GestionUtilisateur'));
// Créer une constanteconst GestionPermission =const GestionPermission = lazy(() => import('pages/permission/GestionPermission'));
// Créer une constanteconst GestionCommande =const GestionCommande = lazy(() => import('pages/commande/GestionCommande'));
// Créer une constanteconst GestionLivreur =const GestionLivreur = lazy(() => import('pages/livreur/GestionLivreur'));
// Créer une constanteconst GestionFournisseur =const GestionFournisseur = lazy(() => import('pages/fournisseur/GestionFournisseur'));
// Créer une constanteconst RapportLivreur =const RapportLivreur = lazy(() => import('pages/rapport/RapportLivreur'));
// Créer une constanteconst RapportLivreurIndividuel =const RapportLivreurIndividuel = lazy(() => import('pages/rapport/RapportLivreurIndividuel'));
// Créer une constanteconst CommandeDetailsPage =const CommandeDetailsPage = lazy(() => import('pages/commande/CommandeDetailsPage'));
// Créer une constanteconst CommandeMapPage =const CommandeMapPage = lazy(() => import('pages/commande/CommandeMapPage'));

// Importer des fonctionnalités depuis un autre fichierimport PageLoader from
import PageLoader from 'components/loading/PageLoader';
// Importer des fonctionnalités depuis un autre fichierimport Progress fromimport Progress from 'components/loading/Progress';
// Importer des fonctionnalités depuis un autre fichierimport AdminOnlyGuard fromimport AdminOnlyGuard from 'components/guards/AdminOnlyGuard';

export const routes = [
  {
    element: (
      <Suspense fallback={<Progress />}>
        <App />
      </Suspense>
    ),
    children: [
      {
        path: rootPaths.root,
        element: (
          <AuthLayout>
            <Suspense fallback={<PageLoader />}>
              <Outlet />
            </Suspense>
          </AuthLayout>
        ),
        children: [
          {
            index: true,
            element: <SignIn />,
          },
          {
            path: paths.logout,
            element: <Logout />,
          },
        ],
      },
      {
        path: rootPaths.appRoot,
        element: (
          <AuthGuard>
            <MainLayout>
              <Suspense fallback={<PageLoader />}>
                <Outlet />
              </Suspense>
            </MainLayout>
          </AuthGuard>
        ),
        children: [
          {
            index: true,
            element: <Dashboard />,
          },
          {
            path: paths.utilisateur,
            element: (
              <AdminOnlyGuard>
                <GestionUtilisateur />
              </AdminOnlyGuard>
            ),
          },
          {
            path: paths.permission,
            element: (
              <AdminOnlyGuard>
                <GestionPermission />
              </AdminOnlyGuard>
            ),
          },
          {
            path: paths.commande,
            element: <GestionCommande />,
          },
          {
            path: paths.commande + '/details/:id',
            element: <CommandeDetailsPage />,
          },
          {
            path: paths.commande + '/map/:id',
            element: <CommandeMapPage />,
          },
          {
            path: paths.livreur,
            element: <GestionLivreur />,
          },
          {
            path: paths.fournisseur,
            element: <GestionFournisseur />,
          },
          {
            path: paths.rapport,
            element: <RapportLivreur />,
          },
          {
            path: paths.rapport + '/livreur/:livreurId',
            element: <RapportLivreurIndividuel />,
          },
          {
            path: 'tracking-page',
            element: <TrackingPage />,
          },
        ],
      },
      {
        path: '*',
        element: <Page404 />,
      },
    ],
  },
];

// Créer une constanteconst router =
const router = createBrowserRouter(routes, { basename: '/horizon' });

// Exporter comme élément principal de ce fichierexport default
export default router;
