﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\005546d0-8a8c-46e0-ba77-3ff7be7e4b1d.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/005546d0-8a8c-46e0-ba77-3ff7be7e4b1d.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>w7efxvrxrb</Fingerprint>
      <Integrity>vIJmCD++VAExPnfSvIfOyXlQbJMpaLNv6fqrQyZYYJc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\005546d0-8a8c-46e0-ba77-3ff7be7e4b1d.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\06721f68-6df5-4ed5-baaa-d604f66edcb5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/06721f68-6df5-4ed5-baaa-d604f66edcb5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r50pjbt1wt</Fingerprint>
      <Integrity>D9zEY864E68vOpG4strTGEcI88FCfUUEUIpWuR60L6M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\06721f68-6df5-4ed5-baaa-d604f66edcb5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\0e135733-0e22-4a6a-9b43-8ebed6ee0759.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/0e135733-0e22-4a6a-9b43-8ebed6ee0759.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qnm2sdhxlw</Fingerprint>
      <Integrity>Nh8VmTOKvjBLfiQMV8dj8fcsV4P7dkbegPBRCBv2H/o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\0e135733-0e22-4a6a-9b43-8ebed6ee0759.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\13a6cb07-d72e-4070-9d53-a880bd5f102b.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/13a6cb07-d72e-4070-9d53-a880bd5f102b.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>3517qrawef</Fingerprint>
      <Integrity>h0fXlbQdu/l1Hd//CBo25CU3ycgNsaLk2IRqIHtxKL0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\13a6cb07-d72e-4070-9d53-a880bd5f102b.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\18c6d5b0-c941-4678-bda5-df4bb1483d0c.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/18c6d5b0-c941-4678-bda5-df4bb1483d0c.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>v79tpejokb</Fingerprint>
      <Integrity>FUsBpailVUiFcTcorxfUkr+rqn/hlqxvMFrDL66Ti2o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\18c6d5b0-c941-4678-bda5-df4bb1483d0c.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\195cb5e4-1395-40af-9f46-5a0a67a2c428.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/195cb5e4-1395-40af-9f46-5a0a67a2c428.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qnm2sdhxlw</Fingerprint>
      <Integrity>Nh8VmTOKvjBLfiQMV8dj8fcsV4P7dkbegPBRCBv2H/o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\195cb5e4-1395-40af-9f46-5a0a67a2c428.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\196ec4f9-84a8-421c-97aa-8e806f53e0f8.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/196ec4f9-84a8-421c-97aa-8e806f53e0f8.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rt6dutv1i2</Fingerprint>
      <Integrity>zwUqqSqhV1Aj5S14HWlByNrP9834i+lQ8Eweh2w/6EQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\196ec4f9-84a8-421c-97aa-8e806f53e0f8.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\1b7c5726-0410-4815-8246-f473e12b367e.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/1b7c5726-0410-4815-8246-f473e12b367e.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>chwdejbf1h</Fingerprint>
      <Integrity>sE9bnzsNTTE4w1jX2POfkHkP2Pqd5P2xGFcermH06Jw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\1b7c5726-0410-4815-8246-f473e12b367e.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\1fdc2c63-c21c-448e-874d-a366ff09a4a4.png'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/1fdc2c63-c21c-448e-874d-a366ff09a4a4.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>m33tmdwdmt</Fingerprint>
      <Integrity>8h44xSz+nJAwMQf7dQ5wCIfsANcVYO1unMbanRoPPfg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\1fdc2c63-c21c-448e-874d-a366ff09a4a4.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\2186cdac-2a4d-4d47-b471-9f4e05ec67f6.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/2186cdac-2a4d-4d47-b471-9f4e05ec67f6.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47trw8ea01</Fingerprint>
      <Integrity>cJONZQ05rSqrxYqziVqKlbIl8eYKpRcTfayTlCWW6i0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\2186cdac-2a4d-4d47-b471-9f4e05ec67f6.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\27b74322-2bca-412c-96de-b81906b19b91.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/27b74322-2bca-412c-96de-b81906b19b91.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47trw8ea01</Fingerprint>
      <Integrity>cJONZQ05rSqrxYqziVqKlbIl8eYKpRcTfayTlCWW6i0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\27b74322-2bca-412c-96de-b81906b19b91.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\3090bf8e-ec6d-46e8-9e0d-bb546b383255.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/3090bf8e-ec6d-46e8-9e0d-bb546b383255.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>eo3mxj5mxn</Fingerprint>
      <Integrity>HnnJxEAnl0I0MUYdcfQyWLfUH9kgWPLdCotDkJvC9Ps=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\3090bf8e-ec6d-46e8-9e0d-bb546b383255.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\326fdabd-657b-4d51-820b-3191a4ea0dce.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/326fdabd-657b-4d51-820b-3191a4ea0dce.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>x4yrcyph8t</Fingerprint>
      <Integrity>b/bnQn2cmEOPnRLZo0uLwe9r31csSTCziACGoPXsQQY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\326fdabd-657b-4d51-820b-3191a4ea0dce.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\355a5115-cbd1-4467-a432-16fbb83e14b5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/355a5115-cbd1-4467-a432-16fbb83e14b5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>afp1ewpj0n</Fingerprint>
      <Integrity>9tRf5bV98h9NkqHP2j2EB1qdOXH1EkDg5CswiaeuMow=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\355a5115-cbd1-4467-a432-16fbb83e14b5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\3eb94508-fb17-4e6f-b304-493f1643ccc4.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/3eb94508-fb17-4e6f-b304-493f1643ccc4.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4d7105xi7w</Fingerprint>
      <Integrity>eGBxAn1BxKCJ5IvYPhE8/m65l2kvXGz4sEenQXjC0J8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\3eb94508-fb17-4e6f-b304-493f1643ccc4.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\42432a2a-1ad9-4e9a-9810-97ef4b9164ba.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/42432a2a-1ad9-4e9a-9810-97ef4b9164ba.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>8fxkx8b49a</Fingerprint>
      <Integrity>zIjuv0OtSie5rlOdLBjWOvTm0hfT8kiNTaDEo0mjtMk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\42432a2a-1ad9-4e9a-9810-97ef4b9164ba.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\46ced7a6-91b2-4154-a398-63f0a718511a.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/46ced7a6-91b2-4154-a398-63f0a718511a.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>vn89gpy398</Fingerprint>
      <Integrity>5Xchp6prIieq7m1+FRcNu7HlVHJg0pCi6zZDq4l32XU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\46ced7a6-91b2-4154-a398-63f0a718511a.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\5ed9384a-3eb2-4458-899c-f972d67842e2.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/5ed9384a-3eb2-4458-899c-f972d67842e2.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>z8u37y215a</Fingerprint>
      <Integrity>40oJRqgwaHBXOiX6htLxfQnX0pyEC8nWOv6ohpGbYZk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\5ed9384a-3eb2-4458-899c-f972d67842e2.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\5fd9386a-1611-44db-91b3-0fb5846a03d7.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/5fd9386a-1611-44db-91b3-0fb5846a03d7.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4winu45883</Fingerprint>
      <Integrity>nIKyHWoJfQTBuZ45C5zmWqpYGmoxtCTVEZJC6BUPD+o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\5fd9386a-1611-44db-91b3-0fb5846a03d7.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\60c139fa-d48c-409a-8fae-ce8ee9cfd78c.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/60c139fa-d48c-409a-8fae-ce8ee9cfd78c.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>chwdejbf1h</Fingerprint>
      <Integrity>sE9bnzsNTTE4w1jX2POfkHkP2Pqd5P2xGFcermH06Jw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\60c139fa-d48c-409a-8fae-ce8ee9cfd78c.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6130cb30-9aa8-4355-803e-a387ab50f561.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/6130cb30-9aa8-4355-803e-a387ab50f561.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>gttyby9orm</Fingerprint>
      <Integrity>jPGCScKlnmjTYwNm7k5DEOs4txnO08tuov9//Vy1dFw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6130cb30-9aa8-4355-803e-a387ab50f561.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6351ecb4-a44e-40e3-8f7b-0d3053c84eeb.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/6351ecb4-a44e-40e3-8f7b-0d3053c84eeb.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>eo3mxj5mxn</Fingerprint>
      <Integrity>HnnJxEAnl0I0MUYdcfQyWLfUH9kgWPLdCotDkJvC9Ps=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6351ecb4-a44e-40e3-8f7b-0d3053c84eeb.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\65011fdc-6230-4810-b357-b9e6603e2c7b.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/65011fdc-6230-4810-b357-b9e6603e2c7b.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rt6dutv1i2</Fingerprint>
      <Integrity>zwUqqSqhV1Aj5S14HWlByNrP9834i+lQ8Eweh2w/6EQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\65011fdc-6230-4810-b357-b9e6603e2c7b.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6d0aa260-7dcc-4919-bd4c-299e42b280fa.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/6d0aa260-7dcc-4919-bd4c-299e42b280fa.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47trw8ea01</Fingerprint>
      <Integrity>cJONZQ05rSqrxYqziVqKlbIl8eYKpRcTfayTlCWW6i0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\6d0aa260-7dcc-4919-bd4c-299e42b280fa.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\7bfffe35-0d14-46d5-b64e-701be59c8080.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/7bfffe35-0d14-46d5-b64e-701be59c8080.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qnm2sdhxlw</Fingerprint>
      <Integrity>Nh8VmTOKvjBLfiQMV8dj8fcsV4P7dkbegPBRCBv2H/o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\7bfffe35-0d14-46d5-b64e-701be59c8080.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\7fad217f-d87d-412b-9e80-cade35d639a8.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/7fad217f-d87d-412b-9e80-cade35d639a8.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>umes14da17</Fingerprint>
      <Integrity>FrqnONj4XnJDTQVui22gDqRGg/2aOjRIoErLVmvoaBM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\7fad217f-d87d-412b-9e80-cade35d639a8.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\8613ffd1-6837-486d-9888-7254e0c670d2.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/8613ffd1-6837-486d-9888-7254e0c670d2.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>bbffdd21bv</Fingerprint>
      <Integrity>R9xlNoYcTlc5015MDt0C/9ppVyOeL05LjonRniy/e5k=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\8613ffd1-6837-486d-9888-7254e0c670d2.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\88283923-7d5b-4e4f-9ae8-caec47aa7c41.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/88283923-7d5b-4e4f-9ae8-caec47aa7c41.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qnm2sdhxlw</Fingerprint>
      <Integrity>Nh8VmTOKvjBLfiQMV8dj8fcsV4P7dkbegPBRCBv2H/o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\88283923-7d5b-4e4f-9ae8-caec47aa7c41.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\89a2f482-191e-48dc-b1d3-9a675392afc4.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/89a2f482-191e-48dc-b1d3-9a675392afc4.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>15g87boz00</Fingerprint>
      <Integrity>tbaKci8j0eFtKkmrAqlSuaLKKvzlbNZKY0bg2EDXVYQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\89a2f482-191e-48dc-b1d3-9a675392afc4.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\98db3f39-db33-4f3b-85b2-2b2ee5385551.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/98db3f39-db33-4f3b-85b2-2b2ee5385551.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>umes14da17</Fingerprint>
      <Integrity>FrqnONj4XnJDTQVui22gDqRGg/2aOjRIoErLVmvoaBM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\98db3f39-db33-4f3b-85b2-2b2ee5385551.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9974a692-e9fa-4b84-8bd7-a8a75ccfff33.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/9974a692-e9fa-4b84-8bd7-a8a75ccfff33.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4d7105xi7w</Fingerprint>
      <Integrity>eGBxAn1BxKCJ5IvYPhE8/m65l2kvXGz4sEenQXjC0J8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9974a692-e9fa-4b84-8bd7-a8a75ccfff33.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9bcd17a4-e657-40b8-a588-2ecc0b8fcf12.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/9bcd17a4-e657-40b8-a588-2ecc0b8fcf12.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>iipnr8wwz7</Fingerprint>
      <Integrity>6vtSRyDCvFs5BGjxCbZA414TvqtCD+hD3AJKxcisegs=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9bcd17a4-e657-40b8-a588-2ecc0b8fcf12.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9daa176f-a15b-4645-bfdb-9161022a0357.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/9daa176f-a15b-4645-bfdb-9161022a0357.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>rt6dutv1i2</Fingerprint>
      <Integrity>zwUqqSqhV1Aj5S14HWlByNrP9834i+lQ8Eweh2w/6EQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9daa176f-a15b-4645-bfdb-9161022a0357.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9e25fa7c-2def-43ef-ae84-f7f436bf96e4.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/9e25fa7c-2def-43ef-ae84-f7f436bf96e4.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>x4yrcyph8t</Fingerprint>
      <Integrity>b/bnQn2cmEOPnRLZo0uLwe9r31csSTCziACGoPXsQQY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\9e25fa7c-2def-43ef-ae84-f7f436bf96e4.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a2c5da39-5f51-436a-b3b4-02ac5e948660.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/a2c5da39-5f51-436a-b3b4-02ac5e948660.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>eo3mxj5mxn</Fingerprint>
      <Integrity>HnnJxEAnl0I0MUYdcfQyWLfUH9kgWPLdCotDkJvC9Ps=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a2c5da39-5f51-436a-b3b4-02ac5e948660.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a43abb09-ce57-4753-af6a-c35200cc9a89.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/a43abb09-ce57-4753-af6a-c35200cc9a89.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r50pjbt1wt</Fingerprint>
      <Integrity>D9zEY864E68vOpG4strTGEcI88FCfUUEUIpWuR60L6M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\a43abb09-ce57-4753-af6a-c35200cc9a89.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\aa498f60-804f-420f-bedd-3ef7d0c88f5c.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/aa498f60-804f-420f-bedd-3ef7d0c88f5c.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47trw8ea01</Fingerprint>
      <Integrity>cJONZQ05rSqrxYqziVqKlbIl8eYKpRcTfayTlCWW6i0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\aa498f60-804f-420f-bedd-3ef7d0c88f5c.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b056a6ad-2e7c-44c2-b755-a2be0f53c6f5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/b056a6ad-2e7c-44c2-b755-a2be0f53c6f5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>8fxkx8b49a</Fingerprint>
      <Integrity>zIjuv0OtSie5rlOdLBjWOvTm0hfT8kiNTaDEo0mjtMk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b056a6ad-2e7c-44c2-b755-a2be0f53c6f5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b34c2d0c-62ec-4346-821d-7b6769833e3e.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/b34c2d0c-62ec-4346-821d-7b6769833e3e.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4d7105xi7w</Fingerprint>
      <Integrity>eGBxAn1BxKCJ5IvYPhE8/m65l2kvXGz4sEenQXjC0J8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b34c2d0c-62ec-4346-821d-7b6769833e3e.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b84ad7fb-9cfa-4f30-a49d-c8e1edde2849.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/b84ad7fb-9cfa-4f30-a49d-c8e1edde2849.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>eo3mxj5mxn</Fingerprint>
      <Integrity>HnnJxEAnl0I0MUYdcfQyWLfUH9kgWPLdCotDkJvC9Ps=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\b84ad7fb-9cfa-4f30-a49d-c8e1edde2849.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\baa10373-2532-48b9-b4aa-31941d477813.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/baa10373-2532-48b9-b4aa-31941d477813.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r50pjbt1wt</Fingerprint>
      <Integrity>D9zEY864E68vOpG4strTGEcI88FCfUUEUIpWuR60L6M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\baa10373-2532-48b9-b4aa-31941d477813.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\ceb7ab8b-6dd3-475c-90bc-f1409b4dcba5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/ceb7ab8b-6dd3-475c-90bc-f1409b4dcba5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>chwdejbf1h</Fingerprint>
      <Integrity>sE9bnzsNTTE4w1jX2POfkHkP2Pqd5P2xGFcermH06Jw=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\ceb7ab8b-6dd3-475c-90bc-f1409b4dcba5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\d512463f-5bf5-4c63-a059-e50be78a053e.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/d512463f-5bf5-4c63-a059-e50be78a053e.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>47trw8ea01</Fingerprint>
      <Integrity>cJONZQ05rSqrxYqziVqKlbIl8eYKpRcTfayTlCWW6i0=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\d512463f-5bf5-4c63-a059-e50be78a053e.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\d81a43d1-52b8-4e7a-b5b5-d79e8ba2eea7.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/d81a43d1-52b8-4e7a-b5b5-d79e8ba2eea7.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>15g87boz00</Fingerprint>
      <Integrity>tbaKci8j0eFtKkmrAqlSuaLKKvzlbNZKY0bg2EDXVYQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\d81a43d1-52b8-4e7a-b5b5-d79e8ba2eea7.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\da31aaa6-2097-4f41-ae69-0e4a459829ac.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/da31aaa6-2097-4f41-ae69-0e4a459829ac.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>4d7105xi7w</Fingerprint>
      <Integrity>eGBxAn1BxKCJ5IvYPhE8/m65l2kvXGz4sEenQXjC0J8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\da31aaa6-2097-4f41-ae69-0e4a459829ac.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\dfd29d80-02cf-4362-b5a4-6d79eef9a1a5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/dfd29d80-02cf-4362-b5a4-6d79eef9a1a5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>qnm2sdhxlw</Fingerprint>
      <Integrity>Nh8VmTOKvjBLfiQMV8dj8fcsV4P7dkbegPBRCBv2H/o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\dfd29d80-02cf-4362-b5a4-6d79eef9a1a5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\dfdf4737-9588-4d0e-a1a2-a7368a602bc4.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/dfdf4737-9588-4d0e-a1a2-a7368a602bc4.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>v79tpejokb</Fingerprint>
      <Integrity>FUsBpailVUiFcTcorxfUkr+rqn/hlqxvMFrDL66Ti2o=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\dfdf4737-9588-4d0e-a1a2-a7368a602bc4.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\ef4ff516-0b6c-4b7d-8e83-531b793b7db5.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/ef4ff516-0b6c-4b7d-8e83-531b793b7db5.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>15g87boz00</Fingerprint>
      <Integrity>tbaKci8j0eFtKkmrAqlSuaLKKvzlbNZKY0bg2EDXVYQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\ef4ff516-0b6c-4b7d-8e83-531b793b7db5.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\f3bca1f9-438c-4e34-8b3c-1f9a1671fd83.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/f3bca1f9-438c-4e34-8b3c-1f9a1671fd83.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r50pjbt1wt</Fingerprint>
      <Integrity>D9zEY864E68vOpG4strTGEcI88FCfUUEUIpWuR60L6M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\f3bca1f9-438c-4e34-8b3c-1f9a1671fd83.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\f624a313-a4c5-4aab-a12e-b7418d1f7909.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>AxiaLivraisonAPI</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/AxiaLivraisonAPI</BasePath>
      <RelativePath>images/f624a313-a4c5-4aab-a12e-b7418d1f7909.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>r50pjbt1wt</Fingerprint>
      <Integrity>D9zEY864E68vOpG4strTGEcI88FCfUUEUIpWuR60L6M=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\f624a313-a4c5-4aab-a12e-b7418d1f7909.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>