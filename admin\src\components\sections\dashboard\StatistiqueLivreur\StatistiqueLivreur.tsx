﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect, useMemo } fromimport { useState, useEffect, useMemo } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Card fromimport Card from '@mui/material/Card';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport FormControl fromimport FormControl from '@mui/material/FormControl';
// Importer des fonctionnalités depuis un autre fichierimport Select, { SelectChangeEvent } fromimport Select, { SelectChangeEvent } from '@mui/material/Select';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from '@mui/material/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport InputLabel fromimport InputLabel from '@mui/material/InputLabel';
// Importer des fonctionnalités depuis un autre fichierimport { useTheme } fromimport { useTheme } from '@mui/material/styles';
// Importer des fonctionnalités depuis un autre fichierimport * as echarts fromimport * as echarts from 'echarts/core';
// Importer des fonctionnalités depuis un autre fichierimport { BarChart } fromimport { BarChart } from 'echarts/charts';
// Importer des fonctionnalités depuis un autre fichierimport ReactEchart fromimport ReactEchart from 'components/base/ReactEchart';
// Importer des fonctionnalités depuis un autre fichierimport { TooltipComponent, GridComponent, LegendComponent } fromimport { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components';
// Importer des fonctionnalités depuis un autre fichierimport { CanvasRenderer } fromimport { CanvasRenderer } from 'echarts/renderers';
// Importer des fonctionnalités depuis un autre fichierimport ButtonBase fromimport ButtonBase from '@mui/material/ButtonBase';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

echarts.use([BarChart, TooltipComponent, GridComponent, LegendComponent, CanvasRenderer]);

// Définir une interface TypeScriptinterface LivreurStat
interface LivreurStat {
  livreurId: number;
  nomLivreur: string;
  nombreCommandesTotal: number;
  nombreCommandesEnTransit: number;
  nombreCommandesLivrees: number;
}

// Interface pour les paramètres du tooltip ECharts
// Définir une interface TypeScriptinterface EChartsTooltipParaminterface EChartsTooltipParam {
  seriesName: string;
  name: string;
  value: number;
  axisValue: string;
  color: string;
  dataIndex: number;
}

// Définir une interface TypeScriptinterface StatistiqueLivreurProps
interface StatistiqueLivreurProps {
  sx?: SxProps;
}

// Créer une constanteconst StatistiqueLivreur =
const StatistiqueLivreur = ({ sx }: StatistiqueLivreurProps) => {
  // Créer une constanteconst theme =  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [livreursStats, setLivreursStats] = useState<LivreurStat[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<string>('');

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  // Générer la liste des 12 derniers mois
  // Créer une constanteconst generateMonthOptions =  const generateMonthOptions = () => {
    // Créer une constanteconst options =    const options = [];
    // Créer une constanteconst currentDate =    const currentDate = new Date();

    for (let i = 0; i < 12; i++) {
      // Créer une constanteconst date =      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      // Créer une constanteconst monthKey =      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      // Créer une constanteconst monthLabel =      const monthLabel = date.toLocaleDateString('fr-FR', {
        month: 'long',
        year: 'numeric',
      });

      options.push({
        value: monthKey,
        label: monthLabel,
        month: date.getMonth() + 1,
        year: date.getFullYear(),
      });
    }

    return options;
  };

  // Créer une constanteconst monthOptions =
  const monthOptions = generateMonthOptions();

  // Initialiser avec le mois actuel
  useEffect(() => {
    if (monthOptions.length > 0 && !selectedMonth) {
      setSelectedMonth(monthOptions[0].value);
    }
  }, [monthOptions, selectedMonth]);

  // Créer une constanteconst handleMonthChange =
  const handleMonthChange = (event: SelectChangeEvent<string>) => {
    setSelectedMonth(event.target.value);
  };

  useEffect(() => {
    // Créer une constanteconst fetchData =    const fetchData = async () => {
      if (!selectedMonth) return;

      try {
        setLoading(true);
        setError(null);

        // Extraire le mois et l'année du selectedMonth
        const [year, month] = selectedMonth.split('-').map(Number);

        // Créer une constanteconst response =
        const response = await axios.get<LivreurStat[]>(
          `${API_URL}/api/statistiques/livreurs-commandes-mois`,
          {
            params: {
              mois: month,
              annee: year,
            },
          },
        );

        // Utiliser directement les données du backend
        setLivreursStats(response.data);
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques des livreurs:', err);
        setError('Impossible de charger les statistiques');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedMonth]);

  // Préparer les données pour le graphique
  // Créer une constanteconst chartOption =  const chartOption = useMemo(() => {
    // Créer une constanteconst livreurIds =    const livreurIds = livreursStats.map((item) => `ID ${item.livreurId}`);

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params: EChartsTooltipParam[]) {
          // Créer une constanteconst livreurId =          const livreurId = params[0].axisValue;
          // Créer une constanteconst livreur =          const livreur = livreursStats.find((l) => `ID ${l.livreurId}` === livreurId);

          if (!livreur) return '';

          return `
            <div style="padding: 10px;">
              <div style="margin-bottom: 5px; font-weight: bold;">${livreurId} - ${livreur.nomLivreur}</div>
              <div>Total: ${livreur.nombreCommandesTotal}</div>
              <div>Livrées: ${livreur.nombreCommandesLivrees} </div>
              <div>En transit: ${livreur.nombreCommandesEnTransit}</div>
            </div>
          `;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: livreurIds,
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: theme.palette.divider,
          },
        },
        axisLabel: {
          color: theme.palette.text.secondary,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false, // Supprime les lignes horizontales
        },
        axisLabel: {
          show: false, // Supprime les nombres sur l'axe Y
        },
      },
      series: [
        {
          name: 'Total',
          type: 'bar',
          stack: 'total',
          barWidth: 20,
          itemStyle: {
            color: theme.palette.primary.main, // Bleu foncé (#4318FF)
            borderRadius: [0, 0, 0, 0],
          },
          emphasis: {
            focus: 'series',
          },
          data: livreursStats.map((item) => {
            // Appliquer les coins arrondis si c'est la seule série visible
            // Créer une constanteconst hasRoundedTop =            const hasRoundedTop =
              item.nombreCommandesEnTransit === 0 && item.nombreCommandesLivrees === 0;
            return {
              value: item.nombreCommandesTotal,
              itemStyle: hasRoundedTop ? { borderRadius: [10, 10, 0, 0] } : {},
            };
          }),
          z: 1,
        },
        {
          name: 'Commandes livrées',
          type: 'bar',
          stack: 'total',
          barWidth: 20,
          itemStyle: {
            color: theme.palette.secondary.light, // Bleu ciel (#6AD2FF)
            borderRadius: [0, 0, 0, 0],
          },
          emphasis: {
            focus: 'series',
          },
          data: livreursStats.map((item) => {
            // Appliquer les coins arrondis si c'est la série du haut (pas de transit)
            // Créer une constanteconst hasRoundedTop =            const hasRoundedTop =
              item.nombreCommandesEnTransit === 0 && item.nombreCommandesLivrees > 0;
            return {
              value: item.nombreCommandesLivrees,
              itemStyle: hasRoundedTop ? { borderRadius: [10, 10, 0, 0] } : {},
            };
          }),
          z: 2,
        },
        {
          name: 'Commandes en transit',
          type: 'bar',
          stack: 'total',
          barWidth: 20,
          itemStyle: {
            color: theme.palette.grey[300], // Gris
            borderRadius: [0, 0, 0, 0], // Par défaut pas de coins arrondis
          },
          emphasis: {
            focus: 'series',
          },
          data: livreursStats.map((item) => {
            // Toujours appliquer les coins arrondis si cette série est présente
            return {
              value: item.nombreCommandesEnTransit,
              itemStyle: item.nombreCommandesEnTransit > 0 ? { borderRadius: [10, 10, 0, 0] } : {},
            };
          }),
          z: 3,
        },
      ],
    };
  }, [livreursStats, theme]);

  // Obtenir le label du mois sélectionné
  // Créer une constanteconst selectedMonthLabel =  const selectedMonthLabel =
    monthOptions.find((option) => option.value === selectedMonth)?.label || '';

  return (
    <Box component={Card} height={350} sx={{ ...sx }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" p={2.5} pb={0}>
        <Box>
          <Typography variant="h4">Statistiques Livreurs</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            {selectedMonthLabel.charAt(0).toUpperCase() + selectedMonthLabel.slice(1)}
          </Typography>
        </Box>
        <Stack direction="row" spacing={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 160 }}>
            <InputLabel id="month-select-label-livreur">Mois</InputLabel>
            <Select
              labelId="month-select-label-livreur"
              value={selectedMonth}
              label="Mois"
              onChange={handleMonthChange}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                },
              }}
            >
              {monthOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Stack
            component={ButtonBase}
            alignItems="center"
            justifyContent="center"
            height={36}
            width={36}
            bgcolor="info.main"
            borderRadius={2.5}
          >
            <IconifyIcon icon="ic:round-bar-chart" color="primary.main" fontSize="h4.fontSize" />
          </Stack>
        </Stack>
      </Stack>

      {loading ? (
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80%' }}
        >
          <Typography>Chargement des données...</Typography>
        </Box>
      ) : error ? (
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80%' }}
        >
          <Typography color="error">{error}</Typography>
        </Box>
      ) : livreursStats.length === 0 ? (
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80%' }}
        >
          <Typography>Aucune donnée disponible</Typography>
        </Box>
      ) : (
        <ReactEchart
          echarts={echarts}
          option={chartOption}
          sx={{ height: '265px !important', pt: 1 }}
        />
      )}
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default StatistiqueLivreur;
