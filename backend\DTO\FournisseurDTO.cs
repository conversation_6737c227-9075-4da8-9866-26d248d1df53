// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Classe publique qui définit un objetpublic class FournisseurDTO
public class FournisseurDTO
{
    // Cette propriété est obligatoire[Required]    [Required]
    public string Nom { get; set; }

    // Cette propriété est obligatoire[Required]
    [Required]
    public string Adresse { get; set; }

    // Cette propriété est obligatoire[Required]
    [Required]
    [Phone]
    public string Telephone { get; set; }

    // Cette propriété est obligatoire[Required]
    [Required]
    [MaxLength(50)]
    public string Identifiant { get; set; } // Ensure this property is included
}