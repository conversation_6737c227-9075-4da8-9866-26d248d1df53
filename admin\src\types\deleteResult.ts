﻿// Types pour les résultats de suppression
export interface DeleteDetails {
  permissionsCount: number;
  commandesCount: number;
  rapportsCount: number;
}

export interface DeleteResult {
  success: boolean;
  message: string;
  details?: DeleteDetails;
}

// Type pour les résultats de suppression de fournisseur (avec commandes)
export interface FournisseurDeleteDetails {
  commandesCount: number;
}

export interface FournisseurDeleteResult {
  success: boolean;
  message: string;
  details?: FournisseurDeleteDetails;
  count?: number; // Pour compatibilité avec l'existant
}
