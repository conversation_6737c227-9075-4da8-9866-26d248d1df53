// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const Select: Components<Omit<Theme, 'components'>>['MuiSelect'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      padding: theme.spacing(0, 1.25),
      borderRadius: theme.shape.borderRadius * 4.5,
      '&.MuiInputBase-root': {
        border: 'none',
        '& .MuiBox-root': {
          minWidth: 20,
        },
      },
    }),
    select: ({ theme }) => ({
      padding: theme.spacing(1),
      paddingRight: '0 !important',
      backgroundColor: 'transparent !important',
      fontSize: theme.typography.body2.fontSize,
      color: theme.palette.text.disabled,
      fontWeight: 600,
      border: 'none',
    }),
    icon: ({ theme }) => ({
      color: theme.palette.text.disabled,
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default Select;
