{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNCGeolocationSpec_autolinked_build", "jsonFile": "directory-RNCGeolocationSpec_autolinked_build-Debug-e2d3f905778c8dfe943b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rncamerakit_specs_autolinked_build", "jsonFile": "directory-rncamerakit_specs_autolinked_build-Debug-0e8297fd9ad17bdb3478.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-ff13b57360b651348372.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-a90f6f4ea5e84d86e885.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-53fddccd63d37156cd70.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-81da8ee7c818ddae1b21.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba", "jsonFile": "target-react_codegen_RNCGeolocationSpec-Debug-f7eabf64b347b941ba00.json", "name": "react_codegen_RNCGeolocationSpec", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-53b448acf427f7815ae5.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rncamerakit_specs::@d945e98661337998d651", "jsonFile": "target-react_codegen_rncamerakit_specs-Debug-4a0119fbaf7e9fbb9353.json", "name": "react_codegen_rncamerakit_specs", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-91ce46be82701bc8cc61.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ad13f1467e75016080f6.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86", "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}