﻿// Importer des fonctionnalités depuis un autre fichierimport Grid fromimport Grid from '@mui/material/Grid';
// Importer des fonctionnalités depuis un autre fichierimport LocationMap fromimport LocationMap from 'components/sections/dashboard/location-map';
// Importer des fonctionnalités depuis un autre fichierimport CommandeCalendar fromimport CommandeCalendar from 'components/sections/dashboard/CommandeCalendar';
// Importer des fonctionnalités depuis un autre fichierimport Analytics fromimport Analytics from 'components/sections/dashboard/analytics';
// Importer des fonctionnalités depuis un autre fichierimport ProfitChartSection fromimport ProfitChartSection from 'components/sections/dashboard/profit-chart';
// Importer des fonctionnalités depuis un autre fichierimport Commandes fromimport Commandes from 'components/sections/dashboard/Commandes';
// Importer des fonctionnalités depuis un autre fichierimport RepartitionFournisseur fromimport RepartitionFournisseur from 'components/sections/dashboard/RepartitionFournisseur';
// Importer des fonctionnalités depuis un autre fichierimport CommandesFournisseurs fromimport CommandesFournisseurs from 'components/sections/dashboard/commandes-fournisseurs';
// Importer des fonctionnalités depuis un autre fichierimport SuiviCommandes fromimport SuiviCommandes from 'components/sections/dashboard/suivi-commandes';
// Importer des fonctionnalités depuis un autre fichierimport StatistiqueLivreur fromimport StatistiqueLivreur from 'components/sections/dashboard/StatistiqueLivreur';
// Importer des fonctionnalités depuis un autre fichierimport Livreur fromimport Livreur from 'components/sections/dashboard/livreur';
// Importer des fonctionnalités depuis un autre fichierimport TempsTraitement fromimport TempsTraitement from 'components/sections/dashboard/temps-traitement';
// Importer des fonctionnalités depuis un autre fichierimport AssistantsAdmin fromimport AssistantsAdmin from 'components/sections/dashboard/AssistantsAdmin';

// Créer une constanteconst CARD_HEIGHT =
const CARD_HEIGHT = 390;

// Créer une constanteconst Dashboard =
const Dashboard = () => {
  return (
    <Grid container spacing={2.5}>
      <Grid item xs={12}>
        <Analytics />
      </Grid>
      <Grid item xs={12} md={6}>
        <CommandesFournisseurs sx={{ height: CARD_HEIGHT }} />
      </Grid>
      <Grid item xs={12} md={6}>
        <StatistiqueLivreur sx={{ height: CARD_HEIGHT }} />
      </Grid>
      <Grid item xs={12} md={6}>
        <ProfitChartSection sx={{ height: CARD_HEIGHT }} />
      </Grid>
      {(JSON.parse(localStorage.getItem('user') || '{}').EstAdmin ||
        JSON.parse(localStorage.getItem('user') || '{}').Permissions?.some(
          (p: { permissionName: string }) => p.permissionName === 'Livreur',
        )) && (
        <Grid item xs={12} md={6}>
          <Livreur sx={{ height: CARD_HEIGHT }} />
        </Grid>
      )}

      <Grid item xs={12} md={6}>
        <SuiviCommandes sx={{ height: CARD_HEIGHT }} />
      </Grid>
      <Grid item xs={12} md={6}>
        <RepartitionFournisseur sx={{ height: CARD_HEIGHT }} />
      </Grid>
      <Grid
        item
        xs={12}
        md={
          !JSON.parse(localStorage.getItem('user') || '{}').EstAdmin &&
          JSON.parse(localStorage.getItem('user') || '{}').Permissions?.some(
            (p: { permissionName: string }) => p.permissionName === 'Livreur',
          )
            ? 12
            : 6
        }
      >
        <CommandeCalendar sx={{ height: CARD_HEIGHT }} />
      </Grid>
      <Grid item xs={12} md={6}>
        <LocationMap sx={{ height: CARD_HEIGHT }} />
      </Grid>

      <Grid item xs={12} md={6}>
        <TempsTraitement sx={{ height: CARD_HEIGHT }} />
      </Grid>
      {JSON.parse(localStorage.getItem('user') || '{}').EstAdmin && (
        <Grid item xs={12} md={6}>
          <AssistantsAdmin sx={{ height: CARD_HEIGHT }} />
        </Grid>
      )}
      <Grid item xs={12} xl={6}>
        <Commandes />
      </Grid>
    </Grid>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Dashboard;
