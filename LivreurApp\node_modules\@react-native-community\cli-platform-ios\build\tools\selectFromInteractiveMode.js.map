{"version": 3, "names": ["selectFromInteractiveMode", "scheme", "mode", "newScheme", "newMode", "project", "getProjectInfo", "schemes", "length", "promptForSchemeSelection", "logger", "info", "chalk", "bold", "configurations", "promptForConfigurationSelection"], "sources": ["../../src/tools/selectFromInteractiveMode.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport {getProjectInfo} from './getProjectInfo';\nimport {\n  promptForConfigurationSelection,\n  promptForSchemeSelection,\n} from './prompts';\n\ninterface Args {\n  scheme: string;\n  mode: string;\n}\n\nexport async function selectFromInteractiveMode({\n  scheme,\n  mode,\n}: Args): Promise<Args> {\n  let newScheme = scheme;\n  let newMode = mode;\n\n  const project = getProjectInfo();\n\n  if (project.schemes.length > 1) {\n    newScheme = await promptForSchemeSelection(project);\n  } else {\n    logger.info(`Automatically selected ${chalk.bold(scheme)} scheme.`);\n  }\n\n  if (project.configurations.length > 1) {\n    newMode = await promptForConfigurationSelection(project);\n  } else {\n    logger.info(`Automatically selected ${chalk.bold(mode)} configuration.`);\n  }\n\n  return {\n    scheme: newScheme,\n    mode: newMode,\n  };\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAGmB;AAOZ,eAAeA,yBAAyB,CAAC;EAC9CC,MAAM;EACNC;AACI,CAAC,EAAiB;EACtB,IAAIC,SAAS,GAAGF,MAAM;EACtB,IAAIG,OAAO,GAAGF,IAAI;EAElB,MAAMG,OAAO,GAAG,IAAAC,8BAAc,GAAE;EAEhC,IAAID,OAAO,CAACE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IAC9BL,SAAS,GAAG,MAAM,IAAAM,iCAAwB,EAACJ,OAAO,CAAC;EACrD,CAAC,MAAM;IACLK,kBAAM,CAACC,IAAI,CAAE,0BAAyBC,gBAAK,CAACC,IAAI,CAACZ,MAAM,CAAE,UAAS,CAAC;EACrE;EAEA,IAAII,OAAO,CAACS,cAAc,CAACN,MAAM,GAAG,CAAC,EAAE;IACrCJ,OAAO,GAAG,MAAM,IAAAW,wCAA+B,EAACV,OAAO,CAAC;EAC1D,CAAC,MAAM;IACLK,kBAAM,CAACC,IAAI,CAAE,0BAAyBC,gBAAK,CAACC,IAAI,CAACX,IAAI,CAAE,iBAAgB,CAAC;EAC1E;EAEA,OAAO;IACLD,MAAM,EAAEE,SAAS;IACjBD,IAAI,EAAEE;EACR,CAAC;AACH"}