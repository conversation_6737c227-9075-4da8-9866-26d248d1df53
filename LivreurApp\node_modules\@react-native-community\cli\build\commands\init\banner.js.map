{"version": 3, "names": ["reactLogoArray", "welcomeMessage", "learnOnceMessage", "chalk", "cyan", "join", "cyan<PERSON><PERSON>", "bold", "dim"], "sources": ["../../../src/commands/init/banner.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nconst reactLogoArray = [\n  '                                                          ',\n  '               ######                ######               ',\n  '             ###     ####        ####     ###             ',\n  '            ##          ###    ###          ##            ',\n  '            ##             ####             ##            ',\n  '            ##             ####             ##            ',\n  '            ##           ##    ##           ##            ',\n  '            ##         ###      ###         ##            ',\n  '             ##  ########################  ##             ',\n  '          ######    ###            ###    ######          ',\n  '      ###     ##    ##              ##    ##     ###      ',\n  '   ###         ## ###      ####      ### ##         ###   ',\n  '  ##           ####      ########      ####           ##  ',\n  ' ##             ###     ##########     ###             ## ',\n  '  ##           ####      ########      ####           ##  ',\n  '   ###         ## ###      ####      ### ##         ###   ',\n  '      ###     ##    ##              ##    ##     ###      ',\n  '          ######    ###            ###    ######          ',\n  '             ##  ########################  ##             ',\n  '            ##         ###      ###         ##            ',\n  '            ##           ##    ##           ##            ',\n  '            ##             ####             ##            ',\n  '            ##             ####             ##            ',\n  '            ##          ###    ###          ##            ',\n  '             ###     ####        ####     ###             ',\n  '               ######                ######               ',\n  '                                                          ',\n];\n\nconst welcomeMessage =\n  '                  Welcome to React Native!                ';\nconst learnOnceMessage =\n  '                 Learn once, write anywhere               ';\n\nexport default `${chalk.cyan(reactLogoArray.join('\\n'))}\n\n${chalk.cyanBright.bold(welcomeMessage)}\n${chalk.dim(learnOnceMessage)}\n`;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,MAAMA,cAAc,GAAG,CACrB,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,EAC5D,4DAA4D,CAC7D;AAED,MAAMC,cAAc,GAClB,4DAA4D;AAC9D,MAAMC,gBAAgB,GACpB,4DAA4D;AAAC,eAE/C,GAAEC,gBAAK,CAACC,IAAI,CAACJ,cAAc,CAACK,IAAI,CAAC,IAAI,CAAC,CAAE;AACxD;AACA,EAAEF,gBAAK,CAACG,UAAU,CAACC,IAAI,CAACN,cAAc,CAAE;AACxC,EAAEE,gBAAK,CAACK,GAAG,CAACN,gBAAgB,CAAE;AAC9B,CAAC;AAAA"}