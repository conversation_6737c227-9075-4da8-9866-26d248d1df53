// Importer des fonctionnalités depuis un autre fichierimport React fromimport React from 'react';
// Importer des fonctionnalités depuis un autre fichierimport ReactDOM fromimport ReactDOM from 'react-dom/client';
// Importer des fonctionnalités depuis un autre fichierimport { RouterProvider } fromimport { RouterProvider } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport { CssBaseline, ThemeProvider } fromimport { CssBaseline, ThemeProvider } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport BreakpointsProvider fromimport BreakpointsProvider from 'providers/BreakpointsProvider';
// Importer des fonctionnalités depuis un autre fichierimport router fromimport router from 'routes/router';
// Importer des fonctionnalités depuis un autre fichierimport { theme } fromimport { theme } from 'theme/theme';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <BreakpointsProvider>
        <CssBaseline />
        <RouterProvider router={router} /> {/* Le routeur gère le rendu */}
      </BreakpointsProvider>
    </ThemeProvider>
  </React.StrictMode>,
);
