{"version": 3, "names": ["toPascalCase", "value", "toUpperCase", "slice"], "sources": ["../../../src/commands/runAndroid/toPascalCase.ts"], "sourcesContent": ["export function toPascalCase(value: string) {\n  return value !== '' ? value[0].toUpperCase() + value.slice(1) : value;\n}\n"], "mappings": ";;;;;;AAAO,SAASA,YAAY,CAACC,KAAa,EAAE;EAC1C,OAAOA,KAAK,KAAK,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGF,KAAK;AACvE"}