// Importer des fonctionnalités depuis un autre fichierimport { forwardRef } fromimport { forwardRef } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { Box, BoxProps } fromimport { Box, BoxProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { EChartsReactProps } fromimport { EChartsReactProps } from 'echarts-for-react';
// Importer des fonctionnalités depuis un autre fichierimport EChartsReactCore fromimport EChartsReactCore from 'echarts-for-react/lib/core';
// Importer des fonctionnalités depuis un autre fichierimport ReactEChartsCore fromimport ReactEChartsCore from 'echarts-for-react/lib/core';

export interface ReactEchartProps extends BoxProps {
  echarts: EChartsReactProps['echarts'];
  option: EChartsReactProps['option'];
}

// Créer une constanteconst ReactEchart =
const ReactEchart = forwardRef<null | EChartsReactCore, ReactEchartProps>(
  ({ option, ...rest }, ref) => {
    return (
      <Box
        component={ReactEChartsCore}
        ref={ref}
        option={{
          ...option,
          tooltip: {
            ...option.tooltip,
            confine: true,
          },
        }}
        {...rest}
      />
    );
  },
);

// Exporter comme élément principal de ce fichierexport default
export default ReactEchart;
