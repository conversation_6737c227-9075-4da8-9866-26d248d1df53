// Importer des fonctionnalités depuis un autre fichierimport React, { useState } fromimport React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
} from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Permission } fromimport { Permission } from './types';

// Définir une interface TypeScriptinterface PermissionTableProps
interface PermissionTableProps {
  permissions: Permission[];
  onEditPermission: (permission: Permission) => void;
  onDeletePermission: (id: number) => void;
}

const PermissionTable: React.FC<PermissionTableProps> = ({
  permissions,
  onEditPermission,
  onDeletePermission,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [permissionToDelete, setPermissionToDelete] = useState<Permission | null>(null);

  // Créer une constanteconst handleDeleteClick =
  const handleDeleteClick = (permission: Permission) => {
    setPermissionToDelete(permission);
    setDeleteDialogOpen(true);
  };

  // Créer une constanteconst confirmDelete =
  const confirmDelete = () => {
    if (permissionToDelete) {
      onDeletePermission(permissionToDelete.id);
    }
    setDeleteDialogOpen(false);
  };

  return (
    <>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>Nom</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {permissions.map((permission) => (
              <TableRow
                key={permission.id}
                hover
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell>{permission.permissionName}</TableCell>
                <TableCell>{permission.description}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={() => onEditPermission(permission)}
                    >
                      Modifier
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      onClick={() => handleDeleteClick(permission)}
                    >
                      Supprimer
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer la permission "{permissionToDelete?.permissionName}"?
          </Typography>
          <Typography sx={{ mt: 2, color: 'warning.main' }}>
            Attention: Cette permission sera également retirée de tous les utilisateurs qui la
            possèdent.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Annuler</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default PermissionTable;
