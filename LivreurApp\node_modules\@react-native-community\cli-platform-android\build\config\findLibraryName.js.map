{"version": 3, "names": ["findLibraryName", "root", "sourceDir", "packageJsonPath", "path", "join", "buildGradlePath", "buildGradleKtsPath", "fs", "existsSync", "packageJson", "JSON", "parse", "readFileSync", "codegenConfig", "name", "buildGradleContents", "undefined", "match"], "sources": ["../../src/config/findLibraryName.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nexport function findLibraryName(root: string, sourceDir: string) {\n  const packageJsonPath = path.join(root, 'package.json');\n  const buildGradlePath = path.join(sourceDir, 'build.gradle');\n  const buildGradleKtsPath = path.join(sourceDir, 'build.gradle.kts');\n\n  // We first check if there is a codegenConfig.name inside the package.json file.\n  if (fs.existsSync(packageJsonPath)) {\n    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n    if (packageJson.codegenConfig?.name) {\n      return packageJson.codegenConfig.name;\n    }\n  }\n\n  // If not, we check if the library specified it in the build.gradle file.\n  let buildGradleContents = '';\n  if (fs.existsSync(buildGradlePath)) {\n    buildGradleContents = fs.readFileSync(buildGradlePath, 'utf-8');\n  } else if (fs.existsSync(buildGradleKtsPath)) {\n    buildGradleContents = fs.readFileSync(buildGradleKtsPath, 'utf-8');\n  } else {\n    return undefined;\n  }\n\n  const match = buildGradleContents.match(/libraryName = [\"'](.+)[\"']/);\n\n  if (match) {\n    return match[1];\n  } else {\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAEjB,SAASA,eAAe,CAACC,IAAY,EAAEC,SAAiB,EAAE;EAC/D,MAAMC,eAAe,GAAGC,eAAI,CAACC,IAAI,CAACJ,IAAI,EAAE,cAAc,CAAC;EACvD,MAAMK,eAAe,GAAGF,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,cAAc,CAAC;EAC5D,MAAMK,kBAAkB,GAAGH,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,kBAAkB,CAAC;;EAEnE;EACA,IAAIM,aAAE,CAACC,UAAU,CAACN,eAAe,CAAC,EAAE;IAAA;IAClC,MAAMO,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,aAAE,CAACK,YAAY,CAACV,eAAe,EAAE,MAAM,CAAC,CAAC;IACxE,6BAAIO,WAAW,CAACI,aAAa,0DAAzB,sBAA2BC,IAAI,EAAE;MACnC,OAAOL,WAAW,CAACI,aAAa,CAACC,IAAI;IACvC;EACF;;EAEA;EACA,IAAIC,mBAAmB,GAAG,EAAE;EAC5B,IAAIR,aAAE,CAACC,UAAU,CAACH,eAAe,CAAC,EAAE;IAClCU,mBAAmB,GAAGR,aAAE,CAACK,YAAY,CAACP,eAAe,EAAE,OAAO,CAAC;EACjE,CAAC,MAAM,IAAIE,aAAE,CAACC,UAAU,CAACF,kBAAkB,CAAC,EAAE;IAC5CS,mBAAmB,GAAGR,aAAE,CAACK,YAAY,CAACN,kBAAkB,EAAE,OAAO,CAAC;EACpE,CAAC,MAAM;IACL,OAAOU,SAAS;EAClB;EAEA,MAAMC,KAAK,GAAGF,mBAAmB,CAACE,KAAK,CAAC,4BAA4B,CAAC;EAErE,IAAIA,KAAK,EAAE;IACT,OAAOA,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACL,OAAOD,SAAS;EAClB;AACF"}