// Importer des fonctionnalités depuis un autre fichierimport { useEffect } fromimport { useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport LinearProgress fromimport LinearProgress from '@mui/material/LinearProgress';
// Importer des fonctionnalités depuis un autre fichierimport { DataGrid, GridColDef, useGridApiRef, GridApi } fromimport { DataGrid, GridColDef, useGridApiRef, GridApi } from '@mui/x-data-grid';
// Importer des fonctionnalités depuis un autre fichierimport DataGridFooter fromimport DataGridFooter from 'components/common/DataGridFooter';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport { rows } fromimport { rows } from 'data/complexTableData';
// Importer des fonctionnalités depuis un autre fichierimport ActionMenu fromimport ActionMenu from './CommandesMenu';

const columns: GridColDef<(typeof rows)[number]>[] = [
  {
    field: '__check__',
    headerName: '',
    width: 52,
    sortable: false,
    disableColumnMenu: true,
  },
  {
    field: 'id',
    headerName: 'ID',
    editable: false,
    align: 'left',
    flex: 2,
    minWidth: 130,
    renderHeader: () => (
      <Typography variant="body2" color="text.disabled" fontWeight={500} ml={1}>
        ID
      </Typography>
    ),
    renderCell: (params) => (
      <Stack ml={1} height={1} direction="column" alignSelf="center" justifyContent="center">
        <Typography variant="body2" fontWeight={600}>
          {params.value}
        </Typography>
      </Stack>
    ),
  },
  {
    field: 'name',
    headerName: 'NAME',
    editable: false,
    align: 'left',
    flex: 2,
    minWidth: 190,
  },
  {
    field: 'status',
    headerName: 'STATUS',
    headerAlign: 'left',
    editable: false,
    flex: 1,
    minWidth: 160,
    renderCell: (params) => {
      // Créer une constanteconst status =      const status = params.value.toLowerCase();
      let color = '';
      let icon = '';

      if (status === 'approved') {
        color = 'success.main';
        icon = 'ic:baseline-check-circle';
      } else if (status === 'error') {
        color = 'warning.main';
        icon = 'ic:baseline-error';
      } else if (status === 'disable') {
        color = 'error.main';
        icon = 'ic:baseline-cancel';
      }

      return (
        <Stack alignItems="center" spacing={0.8} height={1}>
          <IconifyIcon icon={icon} color={color} fontSize="h5.fontSize" />
          <Typography variant="body2" fontWeight={600}>
            {params.value}
          </Typography>
        </Stack>
      );
    },
  },
  {
    field: 'date',
    headerName: 'DATE',
    editable: false,
    align: 'left',
    flex: 2,
    minWidth: 150,
  },
  {
    field: 'progress',
    headerName: 'PROGRESS',
    editable: false,
    align: 'left',
    flex: 2,
    minWidth: 220,
    renderCell: (params) => {
      return (
        <Stack alignItems="center" pr={5} height={1} width={1}>
          <Typography variant="body2" fontWeight={600} minWidth={40}>
            {params.value}%
          </Typography>
          <LinearProgress
            variant="determinate"
            value={params.value}
            sx={{
              width: 1,
              height: 6,
              borderRadius: 10,
              bgcolor: 'info.dark',
              '& .MuiLinearProgress-bar': {
                borderRadius: 10,
              },
            }}
          />
        </Stack>
      );
    },
  },
  {
    field: 'quantity',
    headerName: 'QUANTITY',
    editable: false,
    align: 'left',
    flex: 2,
    minWidth: 100,
  },
  {
    field: 'balance',
    headerName: 'BALANCE',
    headerAlign: 'right',
    align: 'right',
    editable: false,
    flex: 1,
    minWidth: 100,
  },
  {
    field: 'action',
    headerAlign: 'right',
    align: 'right',
    editable: false,
    sortable: false,
    flex: 1,
    minWidth: 100,
    renderHeader: () => <ActionMenu />,
    renderCell: () => <ActionMenu />,
  },
];

// Définir une interface TypeScriptinterface TaskOverviewTableProps
interface TaskOverviewTableProps {
  searchText: string;
}

// Créer une constanteconst DataTable =
const DataTable = ({ searchText }: TaskOverviewTableProps) => {
  // Créer une constanteconst apiRef =  const apiRef = useGridApiRef<GridApi>();

  useEffect(() => {
    apiRef.current.setQuickFilterValues(searchText.split(/\b\W+\b/).filter((word) => word !== ''));
  }, [searchText]);

  return (
    <DataGrid
      apiRef={apiRef}
      density="standard"
      columns={columns}
      rows={rows}
      rowHeight={52}
      disableColumnResize
      disableColumnMenu
      disableColumnSelector
      disableRowSelectionOnClick
      initialState={{
        pagination: { paginationModel: { pageSize: 4 } },
      }}
      autosizeOptions={{
        includeOutliers: true,
        includeHeaders: false,
        outliersFactor: 1,
        expand: true,
      }}
      slots={{
        pagination: DataGridFooter,
      }}
      checkboxSelection
      pageSizeOptions={[4]}
    />
  );
};

// Exporter comme élément principal de ce fichierexport default
export default DataTable;
