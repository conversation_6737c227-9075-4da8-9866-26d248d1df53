{"version": 3, "sources": ["../../@mui/material/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n    className,\n    elementType: initialElementType,\n    ownerState,\n    externalForwardedProps,\n    internalForwardedProps,\n    shouldForwardComponentProp = false,\n    ...useSlotPropsParams\n  } = parameters;\n  const {\n    component: rootComponent,\n    slots = {\n      [name]: undefined\n    },\n    slotProps = {\n      [name]: undefined\n    },\n    ...other\n  } = externalForwardedProps;\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const {\n    props: {\n      component: slotComponent,\n      ...mergedProps\n    },\n    internalRef\n  } = mergeSlotProps({\n    className,\n    ...useSlotPropsParams,\n    externalForwardedProps: name === 'root' ? other : undefined,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, {\n    ...(name === 'root' && !rootComponent && !slots[name] && internalForwardedProps),\n    ...(name !== 'root' && !slots[name] && internalForwardedProps),\n    ...mergedProps,\n    ...(LeafComponent && !shouldForwardComponentProp && {\n      as: LeafComponent\n    }),\n    ...(LeafComponent && shouldForwardComponentProp && {\n      component: LeafComponent\n    }),\n    ref\n  }, ownerState);\n  return [elementType, props];\n}"], "mappings": ";;;;;;;;AAoBe,SAAR,QAOP,MAAM,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,6BAA6B;AAAA,IAC7B,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,cAAc,MAAM,IAAI,KAAK;AAInC,QAAM,0BAA0B,8BAAsB,UAAU,IAAI,GAAG,UAAU;AACjF,QAAM;AAAA,IACJ,OAAO;AAAA,MACL,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,IACA;AAAA,EACF,IAAI,uBAAe;AAAA,IACjB;AAAA,IACA,GAAG;AAAA,IACH,wBAAwB,SAAS,SAAS,QAAQ;AAAA,IAClD,mBAAmB;AAAA,EACrB,CAAC;AACD,QAAM,MAAM,WAAW,aAAa,mEAAyB,KAAK,WAAW,GAAG;AAChF,QAAM,gBAAgB,SAAS,SAAS,iBAAiB,gBAAgB;AACzE,QAAM,QAAQ,yBAAiB,aAAa;AAAA,IAC1C,GAAI,SAAS,UAAU,CAAC,iBAAiB,CAAC,MAAM,IAAI,KAAK;AAAA,IACzD,GAAI,SAAS,UAAU,CAAC,MAAM,IAAI,KAAK;AAAA,IACvC,GAAG;AAAA,IACH,GAAI,iBAAiB,CAAC,8BAA8B;AAAA,MAClD,IAAI;AAAA,IACN;AAAA,IACA,GAAI,iBAAiB,8BAA8B;AAAA,MACjD,WAAW;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG,UAAU;AACb,SAAO,CAAC,aAAa,KAAK;AAC5B;", "names": []}