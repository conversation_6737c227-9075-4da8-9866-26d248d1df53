// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const InputLabel: Components<Omit<Theme, 'components'>>['MuiInputLabel'] = {
  defaultProps: {
    shrink: true,
  },
  styleOverrides: {
    root: ({ theme }) => ({
      left: 0,
      top: theme.spacing(-3.75),
      fontSize: theme.typography.body2.fontSize,
      color: theme.palette.text.primary,
      transform: 'none',
      fontWeight: 600,
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default InputLabel;
