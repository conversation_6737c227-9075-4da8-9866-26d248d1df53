{"version": 3, "names": ["TEST_PROJECTS", "IOS_BASE", "BUNDLE_VENDORED_PODFILE", "findPodfilePath", "cwd", "podfiles", "findAllPodfilePaths", "filter", "project", "path", "dirname", "test", "indexOf", "sort", "length", "logger", "warn", "inlineString", "join"], "sources": ["../../src/config/findPodfilePath.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {inlineString, logger} from '@react-native-community/cli-tools';\nimport path from 'path';\nimport findAllPodfilePaths from './findAllPodfilePaths';\n\n// Regexp matching all test projects\nconst TEST_PROJECTS = /test|example|sample/i;\n\n// Base iOS folder\nconst IOS_BASE = 'ios';\n\n// Podfile in the bundle package\nconst BUNDLE_VENDORED_PODFILE = 'vendor/bundle/ruby';\n\nexport default function findPodfilePath(cwd: string) {\n  const podfiles = findAllPodfilePaths(cwd)\n    /**\n     * Then, we will run a simple test to rule out most example projects,\n     * unless they are located in a `ios` folder\n     */\n    .filter((project) => {\n      if (path.dirname(project) === IOS_BASE) {\n        // Pick the Podfile in the default project (in the iOS folder)\n        return true;\n      }\n\n      if (TEST_PROJECTS.test(project)) {\n        // Ignore the Podfile in test and example projects\n        return false;\n      }\n\n      if (project.indexOf(BUNDLE_VENDORED_PODFILE) > -1) {\n        // Ignore the podfile shipped with Cocoapods in bundle\n        return false;\n      }\n\n      // Accept all the others\n      return true;\n    })\n    /**\n     * Podfile from `ios` folder will be picked up as a first one.\n     */\n    .sort((project) => (path.dirname(project) === IOS_BASE ? -1 : 1));\n\n  if (podfiles.length > 0) {\n    if (podfiles.length > 1) {\n      logger.warn(\n        inlineString(`\n          Multiple Podfiles were found: ${podfiles}. Choosing ${podfiles[0]} automatically.\n          If you would like to select a different one, you can configure it via \"project.ios.sourceDir\".\n          You can learn more about it here: https://github.com/react-native-community/cli/blob/master/docs/configuration.md\n        `),\n      );\n    }\n    return path.join(cwd, podfiles[0]);\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAwD;AAVxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA,MAAMA,aAAa,GAAG,sBAAsB;;AAE5C;AACA,MAAMC,QAAQ,GAAG,KAAK;;AAEtB;AACA,MAAMC,uBAAuB,GAAG,oBAAoB;AAErC,SAASC,eAAe,CAACC,GAAW,EAAE;EACnD,MAAMC,QAAQ,GAAG,IAAAC,4BAAmB,EAACF,GAAG;EACtC;AACJ;AACA;AACA,KAHI,CAICG,MAAM,CAAEC,OAAO,IAAK;IACnB,IAAIC,eAAI,CAACC,OAAO,CAACF,OAAO,CAAC,KAAKP,QAAQ,EAAE;MACtC;MACA,OAAO,IAAI;IACb;IAEA,IAAID,aAAa,CAACW,IAAI,CAACH,OAAO,CAAC,EAAE;MAC/B;MACA,OAAO,KAAK;IACd;IAEA,IAAIA,OAAO,CAACI,OAAO,CAACV,uBAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MACjD;MACA,OAAO,KAAK;IACd;;IAEA;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACJ;AACA,KAFI,CAGCW,IAAI,CAAEL,OAAO,IAAMC,eAAI,CAACC,OAAO,CAACF,OAAO,CAAC,KAAKP,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC;EAEnE,IAAII,QAAQ,CAACS,MAAM,GAAG,CAAC,EAAE;IACvB,IAAIT,QAAQ,CAACS,MAAM,GAAG,CAAC,EAAE;MACvBC,kBAAM,CAACC,IAAI,CACT,IAAAC,wBAAY,EAAE;AACtB,0CAA0CZ,QAAS,cAAaA,QAAQ,CAAC,CAAC,CAAE;AAC5E;AACA;AACA,SAAS,CAAC,CACH;IACH;IACA,OAAOI,eAAI,CAACS,IAAI,CAACd,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC;EAEA,OAAO,IAAI;AACb"}