﻿// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';

// Créer une constanteconst scrollbar =
const scrollbar = (theme: Theme) => ({
  '@supports (-moz-appearance:none)': {
    scrollbarColor: `${theme.palette.grey[300]} transparent`,
  },
  '*::-webkit-scrollbar': {
    width: 5,
    height: 5,
    WebkitAppearance: 'none',
    backgroundColor: 'transparent',
    visibility: 'hidden',
  },
  '*::-webkit-scrollbar-track': {
    margin: 0,
  },
  '*::-webkit-scrollbar-thumb': {
    borderRadius: 3,
    backgroundColor: theme.palette.neutral.light,
    visibility: 'hidden',
  },
});

// Exporter comme élément principal de ce fichierexport default
export default scrollbar;
