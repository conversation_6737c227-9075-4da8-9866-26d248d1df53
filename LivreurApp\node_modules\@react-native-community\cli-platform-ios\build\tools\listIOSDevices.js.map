{"version": 3, "names": ["parseXcdeviceList", "text", "rawOutput", "JSON", "parse", "devices", "filter", "device", "platform", "includes", "sort", "simulator", "map", "isAvailable", "available", "name", "udid", "identifier", "version", "operatingSystemVersion", "availabilityError", "error", "description", "type", "listIOSDevices", "out", "execa", "sync", "stdout"], "sources": ["../../src/tools/listIOSDevices.ts"], "sourcesContent": ["import {Device} from '../types';\nimport execa from 'execa';\n\ntype DeviceOutput = {\n  modelCode: string;\n  simulator: boolean;\n  modelName: string;\n  error: {\n    code: number;\n    failureReason: string;\n    underlyingErrors: [\n      {\n        code: number;\n        failureReason: string;\n        description: string;\n        recoverySuggestion: string;\n        domain: string;\n      },\n    ];\n    description: string;\n    recoverySuggestion: string;\n    domain: string;\n  };\n  operatingSystemVersion: string;\n  identifier: string;\n  platform: string;\n  architecture: string;\n  interface: string;\n  available: boolean;\n  name: string;\n  modelUTI: string;\n};\n\nconst parseXcdeviceList = (text: string): Device[] => {\n  const rawOutput = JSON.parse(text) as DeviceOutput[];\n\n  const devices: Device[] = rawOutput\n    .filter(\n      (device) =>\n        !device.platform.includes('appletv') &&\n        !device.platform.includes('macos'),\n    )\n    .sort((device) => (device.simulator ? 1 : -1))\n    .map((device) => ({\n      isAvailable: device.available,\n      name: device.name,\n      udid: device.identifier,\n      version: device.operatingSystemVersion,\n      availabilityError: device.error?.description,\n      type: device.simulator ? 'simulator' : 'device',\n    }));\n  return devices;\n};\n\nasync function listIOSDevices(): Promise<Device[]> {\n  const out = execa.sync('xcrun', ['xcdevice', 'list']).stdout;\n  return parseXcdeviceList(out);\n}\n\nexport default listIOSDevices;\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAgC1B,MAAMA,iBAAiB,GAAIC,IAAY,IAAe;EACpD,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAmB;EAEpD,MAAMI,OAAiB,GAAGH,SAAS,CAChCI,MAAM,CACJC,MAAM,IACL,CAACA,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpC,CAACF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,CACrC,CACAC,IAAI,CAAEH,MAAM,IAAMA,MAAM,CAACI,SAAS,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC,CAC7CC,GAAG,CAAEL,MAAM;IAAA;IAAA,OAAM;MAChBM,WAAW,EAAEN,MAAM,CAACO,SAAS;MAC7BC,IAAI,EAAER,MAAM,CAACQ,IAAI;MACjBC,IAAI,EAAET,MAAM,CAACU,UAAU;MACvBC,OAAO,EAAEX,MAAM,CAACY,sBAAsB;MACtCC,iBAAiB,mBAAEb,MAAM,CAACc,KAAK,kDAAZ,cAAcC,WAAW;MAC5CC,IAAI,EAAEhB,MAAM,CAACI,SAAS,GAAG,WAAW,GAAG;IACzC,CAAC;EAAA,CAAC,CAAC;EACL,OAAON,OAAO;AAChB,CAAC;AAED,eAAemB,cAAc,GAAsB;EACjD,MAAMC,GAAG,GAAGC,gBAAK,CAACC,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAACC,MAAM;EAC5D,OAAO5B,iBAAiB,CAACyB,GAAG,CAAC;AAC/B;AAAC,eAEcD,cAAc;AAAA"}