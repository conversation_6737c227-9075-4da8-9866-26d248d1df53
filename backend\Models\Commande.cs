// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Models
namespace AxiaLivraisonAPI.Models
{
    // Classe publique qui définit un objetpublic class Commande    public class Commande
    {
        // Marquer comme clé primaire de la base de données[Key]        [Key]
        public int Id { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(50)]
        public string CodeSuivi { get; set; }

        public DateTime DateCreation { get; set; } = DateTime.UtcNow;

        // Add this new property
        public DateTime? DateReception { get; set; }  // Nullable to allow for cases where reception hasn't happened yet

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(100)]
        public string Statut { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public int UtilisateurId { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public int FournisseurId { get; set; }

        public string Description { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public decimal PrixUnitaire { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public int Quantite { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public decimal MontantHorsTax { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public decimal Tva { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public decimal MontantTotale { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public string AdressClient { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(255)]
        public string NomClient { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(50)]
        public string TelephoneClient { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [EmailAddress]
        public string EmailClient { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public double Latitude { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public double Longitude { get; set; }

        public ICollection<Rapport> Rapports { get; set; }
    }
}