{"version": 3, "sources": ["../../@mui/material/ToggleButton/toggleButtonClasses.js", "../../@mui/material/ToggleButtonGroup/ToggleButtonGroupContext.js", "../../@mui/material/ToggleButtonGroup/ToggleButtonGroupButtonContext.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getToggleButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButton', slot);\n}\nconst toggleButtonClasses = generateUtilityClasses('MuiToggleButton', ['root', 'disabled', 'selected', 'standard', 'primary', 'secondary', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'fullWidth']);\nexport default toggleButtonClasses;", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ToggleButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ToggleButtonGroupContext.displayName = 'ToggleButtonGroupContext';\n}\nexport default ToggleButtonGroupContext;", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ToggleButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ToggleButtonGroupButtonContext.displayName = 'ToggleButtonGroupButtonContext';\n}\nexport default ToggleButtonGroupButtonContext;"], "mappings": ";;;;;;;;;;;;AAEO,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACA,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,QAAQ,YAAY,YAAY,YAAY,WAAW,aAAa,aAAa,cAAc,aAAa,WAAW,CAAC;AAC/L,IAAO,8BAAQ;;;ACJf,YAAuB;AAIvB,IAAM,2BAA8C,oBAAc,CAAC,CAAC;AACpE,IAAI,MAAuC;AACzC,2BAAyB,cAAc;AACzC;AACA,IAAO,mCAAQ;;;ACRf,IAAAA,SAAuB;AAIvB,IAAM,iCAAoD,qBAAc,MAAS;AACjF,IAAI,MAAuC;AACzC,iCAA+B,cAAc;AAC/C;AACA,IAAO,yCAAQ;", "names": ["React"]}