// Importer des fonctionnalités depuis un autre fichierimport React, { useEffect, useState } fromimport React, { useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport UtilisateurList fromimport UtilisateurList from './UtilisateurList';
// Importer des fonctionnalités depuis un autre fichierimport UtilisateurForm fromimport UtilisateurForm from './UtilisateurForm';
// Importer des fonctionnalités depuis un autre fichierimport { Utilisateur } fromimport { Utilisateur } from './types';
// Importer des fonctionnalités depuis un autre fichierimport { DeleteResult } fromimport { DeleteResult } from '../../types/deleteResult';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';

const GestionUtilisateur: React.FC = () => {
  const [utilisateurs, setUtilisateurs] = useState<Utilisateur[]>([]);
  const [openUserDialog, setOpenUserDialog] = useState<boolean>(false);
  const [currentUtilisateur, setCurrentUtilisateur] = useState<Utilisateur | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<boolean>(false);
  const [deleteResultOpen, setDeleteResultOpen] = useState<boolean>(false);
  const [utilisateurToDelete, setUtilisateurToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);
  // Créer une constanteconst API_URL =  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    fetchUtilisateurs();
  }, []);

  // Créer une constanteconst fetchUtilisateurs =
  const fetchUtilisateurs = async () => {
    try {
      // Créer une constanteconst response =      const response = await axios.get(`${API_URL}/api/utilisateurs/liste`);
      setUtilisateurs(response.data);
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs :', error);
    }
  };

  // Créer une constanteconst handleSaveUser =
  const handleSaveUser = async (utilisateurData: {
    nom: string;
    email: string;
    telephone: string;
    identifiant: string;
    motDePasse: string;
    imageFile?: File;
  }) => {
    // Créer une constanteconst formData =    const formData = new FormData();
    formData.append('Nom', utilisateurData.nom);
    formData.append('Email', utilisateurData.email);
    formData.append('Telephone', utilisateurData.telephone);
    formData.append('Identifiant', utilisateurData.identifiant);
    formData.append('MotDePasse', utilisateurData.motDePasse);
    if (utilisateurData.imageFile) {
      formData.append('ImageFile', utilisateurData.imageFile);
    }

    try {
      if (currentUtilisateur) {
        await axios.put(`${API_URL}/api/utilisateurs/modifier/${currentUtilisateur.id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
      } else {
        await axios.post(`${API_URL}/api/utilisateurs/ajouter`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
      }
      fetchUtilisateurs();
      setOpenUserDialog(false);
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de l'utilisateur :", error);
    }
  };

  // Créer une constanteconst handleDeleteUser =
  const handleDeleteUser = (id: number) => {
    setUtilisateurToDelete(id);
    setDeleteConfirmOpen(true);
  };

  // Créer une constanteconst handleDeleteConfirmed =
  const handleDeleteConfirmed = async () => {
    if (!utilisateurToDelete) return;

    try {
      setIsDeleting(true);
      // Créer une constanteconst response =      const response = await axios.delete(
        `${API_URL}/api/utilisateurs/supprimer/${utilisateurToDelete}`,
      );

      // Fermer le dialogue de confirmation
      setDeleteConfirmOpen(false);

      // Afficher le résultat
      setDeleteResult({
        success: true,
        message: response.data.message,
        details: response.data.details,
      });

      // Rafraîchir la liste des utilisateurs
      fetchUtilisateurs();
    } catch (error) {
      console.error("Erreur lors de la suppression de l'utilisateur:", error);

      let errorMessage = 'Une erreur est survenue lors de la suppression.';
      if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setDeleteResult({
        success: false,
        message: errorMessage,
      });
    } finally {
      setIsDeleting(false);
      setDeleteResultOpen(true);
      setUtilisateurToDelete(null);
    }
  };

  return (
    <>
      <UtilisateurList
        utilisateurs={utilisateurs}
        onAddUtilisateur={() => {
          setCurrentUtilisateur(null);
          setOpenUserDialog(true);
        }}
        onEditUtilisateur={(utilisateur) => {
          setCurrentUtilisateur(utilisateur);
          setOpenUserDialog(true);
        }}
        onDeleteUtilisateur={handleDeleteUser}
      />

      <UtilisateurForm
        open={openUserDialog}
        onClose={() => setOpenUserDialog(false)}
        onSubmit={handleSaveUser}
        initialData={currentUtilisateur || undefined}
      />

      {/* Dialogue de confirmation de suppression */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>Êtes-vous sûr de vouloir supprimer cet assistant-admin ?</Typography>
          <Typography sx={{ mt: 2, color: 'warning.main' }}>
            Cette action est irréversible et entraînera la suppression de toutes les données
            associées à cet assistant-admin (commandes, permissions, rapports, etc.).
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Annuler</Button>
          <Button onClick={handleDeleteConfirmed} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de résultat de suppression */}
      <Dialog open={deleteResultOpen} onClose={() => setDeleteResultOpen(false)}>
        <DialogTitle sx={{ color: deleteResult?.success ? 'success.main' : 'error.main' }}>
          {deleteResult?.success ? 'Suppression réussie' : 'Erreur de suppression'}
        </DialogTitle>
        <DialogContent>
          {isDeleting ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
              <CircularProgress />
            </div>
          ) : (
            <>
              <Typography>{deleteResult?.message}</Typography>
              {deleteResult?.success && deleteResult?.details && (
                <Typography sx={{ mt: 2, fontSize: '0.9em', color: 'text.secondary' }}>
                  Éléments supprimés :
                  <br />• {deleteResult.details.commandesCount} commande(s)
                  <br />• {deleteResult.details.permissionsCount} permission(s)
                  <br />• {deleteResult.details.rapportsCount} rapport(s)
                </Typography>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteResultOpen(false)} color="primary" variant="contained">
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default GestionUtilisateur;
