{"version": 3, "names": ["promptForSchemeSelection", "project", "scheme", "prompts", "name", "type", "message", "choices", "schemes", "map", "value", "title", "promptForConfigurationSelection", "configuration", "configurations", "promptForDeviceSelection", "availableDevices", "device", "filter", "d", "chalk", "bold", "isAvailable", "availabilityError", "red", "disabled", "min"], "sources": ["../../src/tools/prompts.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport prompts from 'prompts';\nimport {Device, IosProjectInfo} from '../types';\n\nexport async function promptForSchemeSelection(\n  project: IosProjectInfo,\n): Promise<string> {\n  const {scheme} = await prompts({\n    name: 'scheme',\n    type: 'select',\n    message: 'Select the scheme you want to use',\n    choices: project.schemes.map((value) => ({\n      title: value,\n      value: value,\n    })),\n  });\n\n  return scheme;\n}\n\nexport async function promptForConfigurationSelection(\n  project: IosProjectInfo,\n): Promise<string> {\n  const {configuration} = await prompts({\n    name: 'configuration',\n    type: 'select',\n    message: 'Select the configuration you want to use',\n    choices: project.configurations.map((value) => ({\n      title: value,\n      value: value,\n    })),\n  });\n\n  return configuration;\n}\n\nexport async function promptForDeviceSelection(\n  availableDevices: Device[],\n): Promise<Device | undefined> {\n  const {device} = await prompts({\n    type: 'select',\n    name: 'device',\n    message: 'Select the device you want to use',\n    choices: availableDevices\n      .filter((d) => d.type === 'device' || d.type === 'simulator')\n      .map((d) => ({\n        title: `${chalk.bold(d.name)} ${\n          !d.isAvailable && !!d.availabilityError\n            ? chalk.red(`(unavailable - ${d.availabilityError})`)\n            : ''\n        }`,\n        value: d,\n        disabled: !d.isAvailable,\n      })),\n    min: 1,\n  });\n  return device;\n}\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA8B;AAGvB,eAAeA,wBAAwB,CAC5CC,OAAuB,EACN;EACjB,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAC,kBAAO,EAAC;IAC7BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAEN,OAAO,CAACO,OAAO,CAACC,GAAG,CAAEC,KAAK,KAAM;MACvCC,KAAK,EAAED,KAAK;MACZA,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf;AAEO,eAAeU,+BAA+B,CACnDX,OAAuB,EACN;EACjB,MAAM;IAACY;EAAa,CAAC,GAAG,MAAM,IAAAV,kBAAO,EAAC;IACpCC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,0CAA0C;IACnDC,OAAO,EAAEN,OAAO,CAACa,cAAc,CAACL,GAAG,CAAEC,KAAK,KAAM;MAC9CC,KAAK,EAAED,KAAK;MACZA,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOG,aAAa;AACtB;AAEO,eAAeE,wBAAwB,CAC5CC,gBAA0B,EACG;EAC7B,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAd,kBAAO,EAAC;IAC7BE,IAAI,EAAE,QAAQ;IACdD,IAAI,EAAE,QAAQ;IACdE,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAES,gBAAgB,CACtBE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACd,IAAI,KAAK,QAAQ,IAAIc,CAAC,CAACd,IAAI,KAAK,WAAW,CAAC,CAC5DI,GAAG,CAAEU,CAAC,KAAM;MACXR,KAAK,EAAG,GAAES,gBAAK,CAACC,IAAI,CAACF,CAAC,CAACf,IAAI,CAAE,IAC3B,CAACe,CAAC,CAACG,WAAW,IAAI,CAAC,CAACH,CAAC,CAACI,iBAAiB,GACnCH,gBAAK,CAACI,GAAG,CAAE,kBAAiBL,CAAC,CAACI,iBAAkB,GAAE,CAAC,GACnD,EACL,EAAC;MACFb,KAAK,EAAES,CAAC;MACRM,QAAQ,EAAE,CAACN,CAAC,CAACG;IACf,CAAC,CAAC,CAAC;IACLI,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOT,MAAM;AACf"}