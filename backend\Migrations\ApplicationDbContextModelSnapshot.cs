﻿// <auto-generated />
using System;
using AxiaLivraisonAPI.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AxiaLivraisonAPI.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Commande", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdressClient")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CodeSuivi")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailClient")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<double>("Latitude")
                        .HasColumnType("float");

                    b.Property<double>("Longitude")
                        .HasColumnType("float");

                    b.Property<decimal>("MontantHorsTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantTotale")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NomClient")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal>("PrixUnitaire")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantite")
                        .HasColumnType("int");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TelephoneClient")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("Tva")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("UtilisateurId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("Commande", (string)null);
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Fournisseur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Identifiant")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("Fournisseur", (string)null);
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PermissionName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Permission", (string)null);
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Rapport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<string>("Contenu")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CommandeId");

                    b.ToTable("Rapports");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Utilisateur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstAdmin")
                        .HasColumnType("bit");

                    b.Property<bool>("EstLivreur")
                        .HasColumnType("bit");

                    b.Property<string>("Identifiant")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MotDePasse")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("Utilisateur", (string)null);
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.UtilisateurPermission", b =>
                {
                    b.Property<int>("UtilisateurId")
                        .HasColumnType("int");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int");

                    b.HasKey("UtilisateurId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("UtilisateurPermission", (string)null);
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Commande", b =>
                {
                    b.HasOne("AxiaLivraisonAPI.Models.Fournisseur", null)
                        .WithMany("Commandes")
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AxiaLivraisonAPI.Models.Utilisateur", null)
                        .WithMany("Commandes")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Rapport", b =>
                {
                    b.HasOne("AxiaLivraisonAPI.Models.Commande", "Commande")
                        .WithMany("Rapports")
                        .HasForeignKey("CommandeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Commande");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.UtilisateurPermission", b =>
                {
                    b.HasOne("AxiaLivraisonAPI.Models.Permission", "Permission")
                        .WithMany("UtilisateurPermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AxiaLivraisonAPI.Models.Utilisateur", "Utilisateur")
                        .WithMany("UtilisateurPermissions")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Commande", b =>
                {
                    b.Navigation("Rapports");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Fournisseur", b =>
                {
                    b.Navigation("Commandes");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Permission", b =>
                {
                    b.Navigation("UtilisateurPermissions");
                });

            modelBuilder.Entity("AxiaLivraisonAPI.Models.Utilisateur", b =>
                {
                    b.Navigation("Commandes");

                    b.Navigation("UtilisateurPermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
