import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';

/**
 * MockCamera component for development and testing
 * This simulates the Camera component from react-native-camera-kit
 * with a fake QR code scanning functionality
 */
const MockCamera = ({
  style,
  cameraType,
  flashMode,
  scanBarcode,
  onReadCode,
  showFrame,
  laserColor,
  frameColor,
}) => {
  const [scanning, setScanning] = useState(true);

  useEffect(() => {
    // Simulate scanning a QR code after 2 seconds
    if (scanBarcode && onReadCode) {
      const timer = setTimeout(() => {
        // Simulate the event structure that the real camera would provide
        onReadCode({
          nativeEvent: {
            codeStringValue: 'CMD-123456',
            type: 'QR_CODE',
          },
        });
        setScanning(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [scanBarcode, onReadCode]);

  // Simulate scanning again
  const rescan = () => {
    setScanning(true);
    setTimeout(() => {
      if (onReadCode) {
        onReadCode({
          nativeEvent: {
            codeStringValue: 'CMD-' + Math.floor(100000 + Math.random() * 900000),
            type: 'QR_CODE',
          },
        });
        setScanning(false);
      }
    }, 1500);
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.scannerOverlay}>
        {showFrame && (
          <View
            style={[
              styles.scannerFrame,
              frameColor ? { borderColor: frameColor } : null,
            ]}
          />
        )}
        
        {scanning ? (
          <>
            <ActivityIndicator
              size="large"
              color={laserColor || '#FFAA80'}
              style={styles.indicator}
            />
            <Text style={styles.scanningText}>Recherche de QR code...</Text>
          </>
        ) : (
          <TouchableOpacity
            style={styles.rescanButton}
            onPress={rescan}
          >
            <Text style={styles.rescanButtonText}>Scanner à nouveau</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// Styles for the mock scanner
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scannerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#FFAA80',
    backgroundColor: 'transparent',
    borderRadius: 12,
  },
  indicator: {
    marginTop: 20,
  },
  scanningText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
  rescanButton: {
    marginTop: 20,
    backgroundColor: '#FFAA80',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  rescanButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

// Add static properties to match the real Camera component
MockCamera.Constants = {
  CameraType: {
    Back: 'back',
    Front: 'front',
  },
  FlashMode: {
    Auto: 'auto',
    On: 'on',
    Off: 'off',
  },
};

export default MockCamera;
