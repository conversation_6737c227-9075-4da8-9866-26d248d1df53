{"version": 3, "names": ["pkgJson", "require", "program", "<PERSON><PERSON><PERSON><PERSON>", "usage", "version", "option", "handleError", "err", "logger", "enable", "opts", "verbose", "error", "message", "replace", "stack", "log", "hasDebugMessages", "info", "chalk", "dim", "reset", "process", "exit", "printExamples", "examples", "output", "length", "formattedUsage", "map", "example", "desc", "cyan", "cmd", "join", "concat", "bold", "isDetachedCommand", "command", "detached", "isAttachedCommand", "attachCommand", "config", "name", "action", "handleAction", "args", "passedOptions", "argv", "Array", "from", "slice", "func", "Error", "description", "addHelpText", "opt", "options", "parse", "val", "default", "run", "setupAndRun", "e", "includes", "disable", "setVerbose", "platform", "scriptName", "absolutePath", "path", "__dirname", "childProcess", "execFileSync", "stdio", "warn", "red", "loadConfig", "projectCommands", "commands", "debug", "CLIError", "detachedCommands", "bin", "resolve"], "sources": ["../src/index.ts"], "sourcesContent": ["import loadConfig from '@react-native-community/cli-config';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\nimport type {\n  Command,\n  Config,\n  DetachedCommand,\n} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport childProcess from 'child_process';\nimport {Command as CommanderCommand} from 'commander';\nimport path from 'path';\nimport {detachedCommands, projectCommands} from './commands';\n\nconst pkgJson = require('../package.json');\n\nconst program = new CommanderCommand()\n  .usage('[command] [options]')\n  .version(pkgJson.version, '-v', 'Output the current version')\n  .option('--verbose', 'Increase logging verbosity');\n\nconst handleError = (err: Error) => {\n  logger.enable();\n  if (program.opts().verbose) {\n    logger.error(err.message);\n  } else {\n    // Some error messages (esp. custom ones) might have `.` at the end already.\n    const message = err.message.replace(/\\.$/, '');\n    logger.error(`${message}.`);\n  }\n  if (err.stack) {\n    logger.log(err.stack);\n  }\n  if (!program.opts().verbose && logger.hasDebugMessages()) {\n    logger.info(\n      chalk.dim(\n        `Run CLI with ${chalk.reset('--verbose')} ${chalk.dim(\n          'flag for more details.',\n        )}`,\n      ),\n    );\n  }\n  process.exit(1);\n};\n\nfunction printExamples(examples: Command['examples']) {\n  let output: string[] = [];\n\n  if (examples && examples.length > 0) {\n    const formattedUsage = examples\n      .map((example) => `  ${example.desc}: \\n  ${chalk.cyan(example.cmd)}`)\n      .join('\\n\\n');\n\n    output = output.concat([chalk.bold('\\nExample usage:'), formattedUsage]);\n  }\n\n  return output.join('\\n').concat('\\n');\n}\n\n/**\n * Custom type assertion needed for the `makeCommand` conditional\n * types to be properly resolved.\n */\nfunction isDetachedCommand(\n  command: Command<boolean>,\n): command is DetachedCommand {\n  return command.detached === true;\n}\n\nfunction isAttachedCommand(\n  command: Command<boolean>,\n): command is Command<false> {\n  return !isDetachedCommand(command);\n}\n\n/**\n * Attaches a new command onto global `commander` instance.\n *\n * Note that this function takes additional argument of `Config` type in case\n * passed `command` needs it for its execution.\n */\nfunction attachCommand<C extends Command<boolean>>(\n  command: C,\n  config: C extends DetachedCommand ? Config | undefined : Config,\n): void {\n  const cmd = program\n    .command(command.name)\n    .action(async function handleAction(\n      this: CommanderCommand,\n      ...args: string[]\n    ) {\n      const passedOptions = this.opts();\n      const argv = Array.from(args).slice(0, -1);\n\n      try {\n        if (isDetachedCommand(command)) {\n          await command.func(argv, passedOptions, config);\n        } else if (isAttachedCommand(command)) {\n          await command.func(argv, config, passedOptions);\n        } else {\n          throw new Error('A command must be either attached or detached');\n        }\n      } catch (error) {\n        handleError(error as Error);\n      }\n    });\n\n  if (command.description) {\n    cmd.description(command.description);\n  }\n\n  cmd.addHelpText('after', printExamples(command.examples));\n\n  for (const opt of command.options || []) {\n    cmd.option(\n      opt.name,\n      opt.description ?? '',\n      opt.parse || ((val: any) => val),\n      typeof opt.default === 'function' ? opt.default(config) : opt.default,\n    );\n  }\n}\n\nasync function run() {\n  try {\n    await setupAndRun();\n  } catch (e) {\n    handleError(e as Error);\n  }\n}\n\nasync function setupAndRun() {\n  // Commander is not available yet\n\n  // when we run `config`, we don't want to output anything to the console. We\n  // expect it to return valid JSON\n  if (process.argv.includes('config')) {\n    logger.disable();\n  }\n\n  logger.setVerbose(process.argv.includes('--verbose'));\n\n  // We only have a setup script for UNIX envs currently\n  if (process.platform !== 'win32') {\n    const scriptName = 'setup_env.sh';\n    const absolutePath = path.join(__dirname, '..', scriptName);\n\n    try {\n      childProcess.execFileSync(absolutePath, {stdio: 'pipe'});\n    } catch (error) {\n      logger.warn(\n        `Failed to run environment setup script \"${scriptName}\"\\n\\n${chalk.red(\n          error,\n        )}`,\n      );\n      logger.info(\n        `React Native CLI will continue to run if your local environment matches what React Native expects. If it does fail, check out \"${absolutePath}\" and adjust your environment to match it.`,\n      );\n    }\n  }\n\n  let config: Config | undefined;\n  try {\n    config = loadConfig();\n\n    logger.enable();\n\n    for (const command of [...projectCommands, ...config.commands]) {\n      attachCommand(command, config);\n    }\n  } catch (error) {\n    /**\n     * When there is no `package.json` found, the CLI will enter `detached` mode and a subset\n     * of commands will be available. That's why we don't throw on such kind of error.\n     */\n    if ((error as Error).message.includes(\"We couldn't find a package.json\")) {\n      logger.debug((error as Error).message);\n      logger.debug(\n        'Failed to load configuration of your project. Only a subset of commands will be available.',\n      );\n    } else {\n      throw new CLIError(\n        'Failed to load configuration of your project.',\n        error as any,\n      );\n    }\n  } finally {\n    for (const command of detachedCommands) {\n      attachCommand(command, config);\n    }\n  }\n\n  program.parse(process.argv);\n}\n\nconst bin = require.resolve('./bin');\n\nexport {run, bin, loadConfig};\n"], "mappings": ";;;;;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA6D;AAE7D,MAAMA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAE1C,MAAMC,OAAO,GAAG,KAAIC,oBAAgB,GAAE,CACnCC,KAAK,CAAC,qBAAqB,CAAC,CAC5BC,OAAO,CAACL,OAAO,CAACK,OAAO,EAAE,IAAI,EAAE,4BAA4B,CAAC,CAC5DC,MAAM,CAAC,WAAW,EAAE,4BAA4B,CAAC;AAEpD,MAAMC,WAAW,GAAIC,GAAU,IAAK;EAClCC,kBAAM,CAACC,MAAM,EAAE;EACf,IAAIR,OAAO,CAACS,IAAI,EAAE,CAACC,OAAO,EAAE;IAC1BH,kBAAM,CAACI,KAAK,CAACL,GAAG,CAACM,OAAO,CAAC;EAC3B,CAAC,MAAM;IACL;IACA,MAAMA,OAAO,GAAGN,GAAG,CAACM,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9CN,kBAAM,CAACI,KAAK,CAAE,GAAEC,OAAQ,GAAE,CAAC;EAC7B;EACA,IAAIN,GAAG,CAACQ,KAAK,EAAE;IACbP,kBAAM,CAACQ,GAAG,CAACT,GAAG,CAACQ,KAAK,CAAC;EACvB;EACA,IAAI,CAACd,OAAO,CAACS,IAAI,EAAE,CAACC,OAAO,IAAIH,kBAAM,CAACS,gBAAgB,EAAE,EAAE;IACxDT,kBAAM,CAACU,IAAI,CACTC,gBAAK,CAACC,GAAG,CACN,gBAAeD,gBAAK,CAACE,KAAK,CAAC,WAAW,CAAE,IAAGF,gBAAK,CAACC,GAAG,CACnD,wBAAwB,CACxB,EAAC,CACJ,CACF;EACH;EACAE,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;AACjB,CAAC;AAED,SAASC,aAAa,CAACC,QAA6B,EAAE;EACpD,IAAIC,MAAgB,GAAG,EAAE;EAEzB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;IACnC,MAAMC,cAAc,GAAGH,QAAQ,CAC5BI,GAAG,CAAEC,OAAO,IAAM,KAAIA,OAAO,CAACC,IAAK,SAAQZ,gBAAK,CAACa,IAAI,CAACF,OAAO,CAACG,GAAG,CAAE,EAAC,CAAC,CACrEC,IAAI,CAAC,MAAM,CAAC;IAEfR,MAAM,GAAGA,MAAM,CAACS,MAAM,CAAC,CAAChB,gBAAK,CAACiB,IAAI,CAAC,kBAAkB,CAAC,EAAER,cAAc,CAAC,CAAC;EAC1E;EAEA,OAAOF,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA,SAASE,iBAAiB,CACxBC,OAAyB,EACG;EAC5B,OAAOA,OAAO,CAACC,QAAQ,KAAK,IAAI;AAClC;AAEA,SAASC,iBAAiB,CACxBF,OAAyB,EACE;EAC3B,OAAO,CAACD,iBAAiB,CAACC,OAAO,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAa,CACpBH,OAAU,EACVI,MAA+D,EACzD;EACN,MAAMT,GAAG,GAAGhC,OAAO,CAChBqC,OAAO,CAACA,OAAO,CAACK,IAAI,CAAC,CACrBC,MAAM,CAAC,eAAeC,YAAY,CAEjC,GAAGC,IAAc,EACjB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACrC,IAAI,EAAE;IACjC,MAAMsC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1C,IAAI;MACF,IAAId,iBAAiB,CAACC,OAAO,CAAC,EAAE;QAC9B,MAAMA,OAAO,CAACc,IAAI,CAACJ,IAAI,EAAED,aAAa,EAAEL,MAAM,CAAC;MACjD,CAAC,MAAM,IAAIF,iBAAiB,CAACF,OAAO,CAAC,EAAE;QACrC,MAAMA,OAAO,CAACc,IAAI,CAACJ,IAAI,EAAEN,MAAM,EAAEK,aAAa,CAAC;MACjD,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAAC,+CAA+C,CAAC;MAClE;IACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdN,WAAW,CAACM,KAAK,CAAU;IAC7B;EACF,CAAC,CAAC;EAEJ,IAAI0B,OAAO,CAACgB,WAAW,EAAE;IACvBrB,GAAG,CAACqB,WAAW,CAAChB,OAAO,CAACgB,WAAW,CAAC;EACtC;EAEArB,GAAG,CAACsB,WAAW,CAAC,OAAO,EAAE/B,aAAa,CAACc,OAAO,CAACb,QAAQ,CAAC,CAAC;EAEzD,KAAK,MAAM+B,GAAG,IAAIlB,OAAO,CAACmB,OAAO,IAAI,EAAE,EAAE;IACvCxB,GAAG,CAAC5B,MAAM,CACRmD,GAAG,CAACb,IAAI,EACRa,GAAG,CAACF,WAAW,IAAI,EAAE,EACrBE,GAAG,CAACE,KAAK,KAAMC,GAAQ,IAAKA,GAAG,CAAC,EAChC,OAAOH,GAAG,CAACI,OAAO,KAAK,UAAU,GAAGJ,GAAG,CAACI,OAAO,CAAClB,MAAM,CAAC,GAAGc,GAAG,CAACI,OAAO,CACtE;EACH;AACF;AAEA,eAAeC,GAAG,GAAG;EACnB,IAAI;IACF,MAAMC,WAAW,EAAE;EACrB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVzD,WAAW,CAACyD,CAAC,CAAU;EACzB;AACF;AAEA,eAAeD,WAAW,GAAG;EAC3B;;EAEA;EACA;EACA,IAAIxC,OAAO,CAAC0B,IAAI,CAACgB,QAAQ,CAAC,QAAQ,CAAC,EAAE;IACnCxD,kBAAM,CAACyD,OAAO,EAAE;EAClB;EAEAzD,kBAAM,CAAC0D,UAAU,CAAC5C,OAAO,CAAC0B,IAAI,CAACgB,QAAQ,CAAC,WAAW,CAAC,CAAC;;EAErD;EACA,IAAI1C,OAAO,CAAC6C,QAAQ,KAAK,OAAO,EAAE;IAChC,MAAMC,UAAU,GAAG,cAAc;IACjC,MAAMC,YAAY,GAAGC,eAAI,CAACpC,IAAI,CAACqC,SAAS,EAAE,IAAI,EAAEH,UAAU,CAAC;IAE3D,IAAI;MACFI,wBAAY,CAACC,YAAY,CAACJ,YAAY,EAAE;QAACK,KAAK,EAAE;MAAM,CAAC,CAAC;IAC1D,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdJ,kBAAM,CAACmE,IAAI,CACR,2CAA0CP,UAAW,QAAOjD,gBAAK,CAACyD,GAAG,CACpEhE,KAAK,CACL,EAAC,CACJ;MACDJ,kBAAM,CAACU,IAAI,CACR,kIAAiImD,YAAa,4CAA2C,CAC3L;IACH;EACF;EAEA,IAAI3B,MAA0B;EAC9B,IAAI;IACFA,MAAM,GAAG,IAAAmC,oBAAU,GAAE;IAErBrE,kBAAM,CAACC,MAAM,EAAE;IAEf,KAAK,MAAM6B,OAAO,IAAI,CAAC,GAAGwC,yBAAe,EAAE,GAAGpC,MAAM,CAACqC,QAAQ,CAAC,EAAE;MAC9DtC,aAAa,CAACH,OAAO,EAAEI,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;IACd;AACJ;AACA;AACA;IACI,IAAKA,KAAK,CAAWC,OAAO,CAACmD,QAAQ,CAAC,iCAAiC,CAAC,EAAE;MACxExD,kBAAM,CAACwE,KAAK,CAAEpE,KAAK,CAAWC,OAAO,CAAC;MACtCL,kBAAM,CAACwE,KAAK,CACV,4FAA4F,CAC7F;IACH,CAAC,MAAM;MACL,MAAM,KAAIC,oBAAQ,EAChB,+CAA+C,EAC/CrE,KAAK,CACN;IACH;EACF,CAAC,SAAS;IACR,KAAK,MAAM0B,OAAO,IAAI4C,0BAAgB,EAAE;MACtCzC,aAAa,CAACH,OAAO,EAAEI,MAAM,CAAC;IAChC;EACF;EAEAzC,OAAO,CAACyD,KAAK,CAACpC,OAAO,CAAC0B,IAAI,CAAC;AAC7B;AAEA,MAAMmC,GAAG,GAAGnF,OAAO,CAACoF,OAAO,CAAC,OAAO,CAAC;AAAC"}