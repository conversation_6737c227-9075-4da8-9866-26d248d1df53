{"version": 3, "names": ["runAndroid", "_argv", "config", "args", "link", "setPlatform", "reactNativeVersion", "setVersion", "binaryPath", "tasks", "CLIError", "path", "isAbsolute", "join", "root", "fs", "existsSync", "androidProject", "getAndroidProject", "runPack<PERSON>", "buildAndRun", "defaultPort", "getAvailableDevicePort", "port", "adbPath", "getAdbPath", "devices", "adb", "getDevices", "some", "d", "includes", "toString", "process", "chdir", "sourceDir", "cmd", "platform", "startsWith", "selectedTask", "interactive", "task", "promptForTaskSelection", "listDevices", "deviceId", "logger", "warn", "device", "listAndroidDevices", "users", "checkUsers", "length", "user", "promptForUser", "id", "connected", "runOnSpecificDevice", "emulator", "info", "result", "tryLaunchEmulator", "readableName", "success", "chalk", "dim", "error", "runOnAllDevices", "buildTask", "replace", "indexOf", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "mode", "variant", "push", "extraParams", "activeArchOnly", "architecture", "getCPU", "build", "installAndLaunchOnDevice", "selected<PERSON><PERSON><PERSON>", "tryRunAdbReverse", "tryInstallAppOnDevice", "tryLaunchAppOnDevice", "packageName", "name", "description", "func", "options", "default", "parse", "Number"], "sources": ["../../../src/commands/runAndroid/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport fs from 'fs';\nimport {Config} from '@react-native-community/cli-types';\nimport adb from './adb';\nimport runOnAllDevices from './runOnAllDevices';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport tryInstallAppOnDevice from './tryInstallAppOnDevice';\nimport getAdbPath from './getAdbPath';\nimport {logger, CLIError, link} from '@react-native-community/cli-tools';\nimport {getAndroidProject} from '../../config/getAndroidProject';\nimport listAndroidDevices from './listAndroidDevices';\nimport tryLaunchEmulator from './tryLaunchEmulator';\nimport chalk from 'chalk';\nimport path from 'path';\nimport {build, runPackager, BuildFlags, options} from '../buildAndroid';\nimport {promptForTaskSelection} from './listAndroidTasks';\nimport {getTaskNames} from './getTaskNames';\nimport {checkUsers, promptForUser} from './listAndroidUsers';\n\nexport interface Flags extends BuildFlags {\n  appId: string;\n  appIdSuffix: string;\n  mainActivity: string;\n  deviceId?: string;\n  listDevices?: boolean;\n  binaryPath?: string;\n  user?: number | string;\n}\n\nexport type AndroidProject = NonNullable<Config['project']['android']>;\n\n/**\n * Starts the app on a connected Android emulator or device.\n */\nasync function runAndroid(_argv: Array<string>, config: Config, args: Flags) {\n  link.setPlatform('android');\n\n  if (config.reactNativeVersion !== 'unknown') {\n    link.setVersion(config.reactNativeVersion);\n  }\n\n  if (args.binaryPath) {\n    if (args.tasks) {\n      throw new CLIError(\n        'binary-path and tasks were specified, but they are not compatible. Specify only one',\n      );\n    }\n\n    args.binaryPath = path.isAbsolute(args.binaryPath)\n      ? args.binaryPath\n      : path.join(config.root, args.binaryPath);\n\n    if (args.binaryPath && !fs.existsSync(args.binaryPath)) {\n      throw new CLIError(\n        'binary-path was specified, but the file was not found.',\n      );\n    }\n  }\n\n  const androidProject = getAndroidProject(config);\n\n  await runPackager(args, config);\n  return buildAndRun(args, androidProject);\n}\n\nconst defaultPort = 5552;\nasync function getAvailableDevicePort(\n  port: number = defaultPort,\n): Promise<number> {\n  /**\n   * The default value is 5554 for the first virtual device instance running on your machine. A virtual device normally occupies a pair of adjacent ports: a console port and an adb port. The console of the first virtual device running on a particular machine uses console port 5554 and adb port 5555. Subsequent instances use port numbers increasing by two. For example, 5556/5557, 5558/5559, and so on. The range is 5554 to 5682, allowing for 64 concurrent virtual devices.\n   */\n  const adbPath = getAdbPath();\n  const devices = adb.getDevices(adbPath);\n  if (port > 5682) {\n    throw new CLIError('Failed to launch emulator...');\n  }\n  if (devices.some((d) => d.includes(port.toString()))) {\n    return await getAvailableDevicePort(port + 2);\n  }\n  return port;\n}\n\n// Builds the app and runs it on a connected emulator / device.\nasync function buildAndRun(args: Flags, androidProject: AndroidProject) {\n  process.chdir(androidProject.sourceDir);\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n\n  const adbPath = getAdbPath();\n\n  let selectedTask;\n\n  if (args.interactive) {\n    const task = await promptForTaskSelection(\n      'install',\n      androidProject.sourceDir,\n    );\n    if (task) {\n      selectedTask = task;\n    }\n  }\n\n  if (args.listDevices || args.interactive) {\n    if (args.deviceId) {\n      logger.warn(\n        'Both \"deviceId\" and \"list-devices\" parameters were passed to \"run\" command. We will list available devices and let you choose from one',\n      );\n    }\n\n    const device = await listAndroidDevices();\n    if (!device) {\n      throw new CLIError(\n        `Failed to select device, please try to run app without ${\n          args.listDevices ? 'list-devices' : 'interactive'\n        } command.`,\n      );\n    }\n\n    if (args.interactive) {\n      const users = checkUsers(device.deviceId as string, adbPath);\n      if (users && users.length > 1) {\n        const user = await promptForUser(users);\n\n        if (user) {\n          args.user = user.id;\n        }\n      }\n    }\n\n    if (device.connected) {\n      return runOnSpecificDevice(\n        {...args, deviceId: device.deviceId},\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    }\n\n    const port = await getAvailableDevicePort();\n    const emulator = `emulator-${port}`;\n    logger.info('Launching emulator...');\n    const result = await tryLaunchEmulator(adbPath, device.readableName, port);\n    if (result.success) {\n      logger.info('Successfully launched emulator.');\n      return runOnSpecificDevice(\n        {...args, deviceId: emulator},\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    }\n    throw new CLIError(\n      `Failed to launch emulator. Reason: ${chalk.dim(result.error || '')}`,\n    );\n  }\n\n  if (args.deviceId) {\n    return runOnSpecificDevice(args, adbPath, androidProject, selectedTask);\n  } else {\n    return runOnAllDevices(args, cmd, adbPath, androidProject);\n  }\n}\n\nfunction runOnSpecificDevice(\n  args: Flags,\n  adbPath: string,\n  androidProject: AndroidProject,\n  selectedTask?: string,\n) {\n  const devices = adb.getDevices(adbPath);\n  const {deviceId} = args;\n\n  // if coming from run-android command and we have selected task\n  // from interactive mode we need to create appropriate build task\n  // eg 'installRelease' -> 'assembleRelease'\n  const buildTask = selectedTask\n    ? [selectedTask.replace('install', 'assemble')]\n    : [];\n\n  if (devices.length > 0 && deviceId) {\n    if (devices.indexOf(deviceId) !== -1) {\n      let gradleArgs = getTaskNames(\n        androidProject.appName,\n        args.mode || args.variant,\n        args.tasks ?? buildTask,\n        'install',\n        androidProject.sourceDir,\n      );\n\n      // using '-x lint' in order to ignore linting errors while building the apk\n      gradleArgs.push('-x', 'lint');\n      if (args.extraParams) {\n        gradleArgs.push(...args.extraParams);\n      }\n\n      if (args.port) {\n        gradleArgs.push(`-PreactNativeDevServerPort=${args.port}`);\n      }\n\n      if (args.activeArchOnly) {\n        const architecture = adb.getCPU(adbPath, deviceId);\n\n        if (architecture !== null) {\n          logger.info(`Detected architecture ${architecture}`);\n          // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n          // Can be removed when 0.67 no longer needs to be supported.\n          gradleArgs.push(`-PreactNativeDebugArchitectures=${architecture}`);\n          gradleArgs.push(`-PreactNativeArchitectures=${architecture}`);\n        }\n      }\n\n      if (!args.binaryPath) {\n        build(gradleArgs, androidProject.sourceDir);\n      }\n\n      installAndLaunchOnDevice(\n        args,\n        deviceId,\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    } else {\n      logger.error(\n        `Could not find device with the id: \"${deviceId}\". Please choose one of the following:`,\n        ...devices,\n      );\n    }\n  } else {\n    logger.error('No Android device or emulator connected.');\n  }\n}\n\nfunction installAndLaunchOnDevice(\n  args: Flags,\n  selectedDevice: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n  selectedTask?: string,\n) {\n  tryRunAdbReverse(args.port, selectedDevice);\n\n  tryInstallAppOnDevice(\n    args,\n    adbPath,\n    selectedDevice,\n    androidProject,\n    selectedTask,\n  );\n  tryLaunchAppOnDevice(\n    selectedDevice,\n    androidProject.packageName,\n    adbPath,\n    args,\n  );\n}\n\nexport default {\n  name: 'run-android',\n  description:\n    'builds your app and starts it on a connected Android emulator or device',\n  func: runAndroid,\n  options: [\n    ...options,\n    {\n      name: '--appId <string>',\n      description:\n        'Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.',\n      default: '',\n    },\n    {\n      name: '--appIdSuffix <string>',\n      description: 'Specify an applicationIdSuffix to launch after build.',\n      default: '',\n    },\n    {\n      name: '--main-activity <string>',\n      description: 'Name of the activity to start',\n      default: 'MainActivity',\n    },\n    {\n      name: '--deviceId <string>',\n      description:\n        'builds your app and starts it on a specific device/simulator with the ' +\n        'given device id (listed by running \"adb devices\" on the command line).',\n    },\n    {\n      name: '--list-devices',\n      description:\n        'Lists all available Android devices and simulators and let you choose one to run the app',\n      default: false,\n    },\n    {\n      name: '--binary-path <string>',\n      description:\n        'Path relative to project root where pre-built .apk binary lives.',\n    },\n    {\n      name: '--user <number>',\n      description: 'Id of the User Profile you want to install the app on.',\n      parse: Number,\n    },\n  ],\n};\n\nexport {adb, getAdbPath, listAndroidDevices, tryRunAdbReverse};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAA6D;AAxB7D;AACA;AACA;AACA;AACA;AACA;AACA;;AAgCA;AACA;AACA;AACA,eAAeA,UAAU,CAACC,KAAoB,EAAEC,MAAc,EAAEC,IAAW,EAAE;EAC3EC,gBAAI,CAACC,WAAW,CAAC,SAAS,CAAC;EAE3B,IAAIH,MAAM,CAACI,kBAAkB,KAAK,SAAS,EAAE;IAC3CF,gBAAI,CAACG,UAAU,CAACL,MAAM,CAACI,kBAAkB,CAAC;EAC5C;EAEA,IAAIH,IAAI,CAACK,UAAU,EAAE;IACnB,IAAIL,IAAI,CAACM,KAAK,EAAE;MACd,MAAM,KAAIC,oBAAQ,EAChB,qFAAqF,CACtF;IACH;IAEAP,IAAI,CAACK,UAAU,GAAGG,eAAI,CAACC,UAAU,CAACT,IAAI,CAACK,UAAU,CAAC,GAC9CL,IAAI,CAACK,UAAU,GACfG,eAAI,CAACE,IAAI,CAACX,MAAM,CAACY,IAAI,EAAEX,IAAI,CAACK,UAAU,CAAC;IAE3C,IAAIL,IAAI,CAACK,UAAU,IAAI,CAACO,aAAE,CAACC,UAAU,CAACb,IAAI,CAACK,UAAU,CAAC,EAAE;MACtD,MAAM,KAAIE,oBAAQ,EAChB,wDAAwD,CACzD;IACH;EACF;EAEA,MAAMO,cAAc,GAAG,IAAAC,oCAAiB,EAAChB,MAAM,CAAC;EAEhD,MAAM,IAAAiB,yBAAW,EAAChB,IAAI,EAAED,MAAM,CAAC;EAC/B,OAAOkB,WAAW,CAACjB,IAAI,EAAEc,cAAc,CAAC;AAC1C;AAEA,MAAMI,WAAW,GAAG,IAAI;AACxB,eAAeC,sBAAsB,CACnCC,IAAY,GAAGF,WAAW,EACT;EACjB;AACF;AACA;EACE,MAAMG,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAC5B,MAAMC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACvC,IAAID,IAAI,GAAG,IAAI,EAAE;IACf,MAAM,KAAIb,oBAAQ,EAAC,8BAA8B,CAAC;EACpD;EACA,IAAIgB,OAAO,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAACR,IAAI,CAACS,QAAQ,EAAE,CAAC,CAAC,EAAE;IACpD,OAAO,MAAMV,sBAAsB,CAACC,IAAI,GAAG,CAAC,CAAC;EAC/C;EACA,OAAOA,IAAI;AACb;;AAEA;AACA,eAAeH,WAAW,CAACjB,IAAW,EAAEc,cAA8B,EAAE;EACtEgB,OAAO,CAACC,KAAK,CAACjB,cAAc,CAACkB,SAAS,CAAC;EACvC,MAAMC,GAAG,GAAGH,OAAO,CAACI,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;EAE5E,MAAMd,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAE5B,IAAIc,YAAY;EAEhB,IAAIpC,IAAI,CAACqC,WAAW,EAAE;IACpB,MAAMC,IAAI,GAAG,MAAM,IAAAC,wCAAsB,EACvC,SAAS,EACTzB,cAAc,CAACkB,SAAS,CACzB;IACD,IAAIM,IAAI,EAAE;MACRF,YAAY,GAAGE,IAAI;IACrB;EACF;EAEA,IAAItC,IAAI,CAACwC,WAAW,IAAIxC,IAAI,CAACqC,WAAW,EAAE;IACxC,IAAIrC,IAAI,CAACyC,QAAQ,EAAE;MACjBC,kBAAM,CAACC,IAAI,CACT,wIAAwI,CACzI;IACH;IAEA,MAAMC,MAAM,GAAG,MAAM,IAAAC,2BAAkB,GAAE;IACzC,IAAI,CAACD,MAAM,EAAE;MACX,MAAM,KAAIrC,oBAAQ,EACf,0DACCP,IAAI,CAACwC,WAAW,GAAG,cAAc,GAAG,aACrC,WAAU,CACZ;IACH;IAEA,IAAIxC,IAAI,CAACqC,WAAW,EAAE;MACpB,MAAMS,KAAK,GAAG,IAAAC,4BAAU,EAACH,MAAM,CAACH,QAAQ,EAAYpB,OAAO,CAAC;MAC5D,IAAIyB,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,IAAI,GAAG,MAAM,IAAAC,+BAAa,EAACJ,KAAK,CAAC;QAEvC,IAAIG,IAAI,EAAE;UACRjD,IAAI,CAACiD,IAAI,GAAGA,IAAI,CAACE,EAAE;QACrB;MACF;IACF;IAEA,IAAIP,MAAM,CAACQ,SAAS,EAAE;MACpB,OAAOC,mBAAmB,CACxB;QAAC,GAAGrD,IAAI;QAAEyC,QAAQ,EAAEG,MAAM,CAACH;MAAQ,CAAC,EACpCpB,OAAO,EACPP,cAAc,EACdsB,YAAY,CACb;IACH;IAEA,MAAMhB,IAAI,GAAG,MAAMD,sBAAsB,EAAE;IAC3C,MAAMmC,QAAQ,GAAI,YAAWlC,IAAK,EAAC;IACnCsB,kBAAM,CAACa,IAAI,CAAC,uBAAuB,CAAC;IACpC,MAAMC,MAAM,GAAG,MAAM,IAAAC,0BAAiB,EAACpC,OAAO,EAAEuB,MAAM,CAACc,YAAY,EAAEtC,IAAI,CAAC;IAC1E,IAAIoC,MAAM,CAACG,OAAO,EAAE;MAClBjB,kBAAM,CAACa,IAAI,CAAC,iCAAiC,CAAC;MAC9C,OAAOF,mBAAmB,CACxB;QAAC,GAAGrD,IAAI;QAAEyC,QAAQ,EAAEa;MAAQ,CAAC,EAC7BjC,OAAO,EACPP,cAAc,EACdsB,YAAY,CACb;IACH;IACA,MAAM,KAAI7B,oBAAQ,EACf,sCAAqCqD,gBAAK,CAACC,GAAG,CAACL,MAAM,CAACM,KAAK,IAAI,EAAE,CAAE,EAAC,CACtE;EACH;EAEA,IAAI9D,IAAI,CAACyC,QAAQ,EAAE;IACjB,OAAOY,mBAAmB,CAACrD,IAAI,EAAEqB,OAAO,EAAEP,cAAc,EAAEsB,YAAY,CAAC;EACzE,CAAC,MAAM;IACL,OAAO,IAAA2B,wBAAe,EAAC/D,IAAI,EAAEiC,GAAG,EAAEZ,OAAO,EAAEP,cAAc,CAAC;EAC5D;AACF;AAEA,SAASuC,mBAAmB,CAC1BrD,IAAW,EACXqB,OAAe,EACfP,cAA8B,EAC9BsB,YAAqB,EACrB;EACA,MAAMb,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACvC,MAAM;IAACoB;EAAQ,CAAC,GAAGzC,IAAI;;EAEvB;EACA;EACA;EACA,MAAMgE,SAAS,GAAG5B,YAAY,GAC1B,CAACA,YAAY,CAAC6B,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,GAC7C,EAAE;EAEN,IAAI1C,OAAO,CAACyB,MAAM,GAAG,CAAC,IAAIP,QAAQ,EAAE;IAClC,IAAIlB,OAAO,CAAC2C,OAAO,CAACzB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACpC,IAAI0B,UAAU,GAAG,IAAAC,0BAAY,EAC3BtD,cAAc,CAACuD,OAAO,EACtBrE,IAAI,CAACsE,IAAI,IAAItE,IAAI,CAACuE,OAAO,EACzBvE,IAAI,CAACM,KAAK,IAAI0D,SAAS,EACvB,SAAS,EACTlD,cAAc,CAACkB,SAAS,CACzB;;MAED;MACAmC,UAAU,CAACK,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;MAC7B,IAAIxE,IAAI,CAACyE,WAAW,EAAE;QACpBN,UAAU,CAACK,IAAI,CAAC,GAAGxE,IAAI,CAACyE,WAAW,CAAC;MACtC;MAEA,IAAIzE,IAAI,CAACoB,IAAI,EAAE;QACb+C,UAAU,CAACK,IAAI,CAAE,8BAA6BxE,IAAI,CAACoB,IAAK,EAAC,CAAC;MAC5D;MAEA,IAAIpB,IAAI,CAAC0E,cAAc,EAAE;QACvB,MAAMC,YAAY,GAAGnD,YAAG,CAACoD,MAAM,CAACvD,OAAO,EAAEoB,QAAQ,CAAC;QAElD,IAAIkC,YAAY,KAAK,IAAI,EAAE;UACzBjC,kBAAM,CAACa,IAAI,CAAE,yBAAwBoB,YAAa,EAAC,CAAC;UACpD;UACA;UACAR,UAAU,CAACK,IAAI,CAAE,mCAAkCG,YAAa,EAAC,CAAC;UAClER,UAAU,CAACK,IAAI,CAAE,8BAA6BG,YAAa,EAAC,CAAC;QAC/D;MACF;MAEA,IAAI,CAAC3E,IAAI,CAACK,UAAU,EAAE;QACpB,IAAAwE,mBAAK,EAACV,UAAU,EAAErD,cAAc,CAACkB,SAAS,CAAC;MAC7C;MAEA8C,wBAAwB,CACtB9E,IAAI,EACJyC,QAAQ,EACRpB,OAAO,EACPP,cAAc,EACdsB,YAAY,CACb;IACH,CAAC,MAAM;MACLM,kBAAM,CAACoB,KAAK,CACT,uCAAsCrB,QAAS,wCAAuC,EACvF,GAAGlB,OAAO,CACX;IACH;EACF,CAAC,MAAM;IACLmB,kBAAM,CAACoB,KAAK,CAAC,0CAA0C,CAAC;EAC1D;AACF;AAEA,SAASgB,wBAAwB,CAC/B9E,IAAW,EACX+E,cAAsB,EACtB1D,OAAe,EACfP,cAA8B,EAC9BsB,YAAqB,EACrB;EACA,IAAA4C,yBAAgB,EAAChF,IAAI,CAACoB,IAAI,EAAE2D,cAAc,CAAC;EAE3C,IAAAE,8BAAqB,EACnBjF,IAAI,EACJqB,OAAO,EACP0D,cAAc,EACdjE,cAAc,EACdsB,YAAY,CACb;EACD,IAAA8C,6BAAoB,EAClBH,cAAc,EACdjE,cAAc,CAACqE,WAAW,EAC1B9D,OAAO,EACPrB,IAAI,CACL;AACH;AAAC,eAEc;EACboF,IAAI,EAAE,aAAa;EACnBC,WAAW,EACT,yEAAyE;EAC3EC,IAAI,EAAEzF,UAAU;EAChB0F,OAAO,EAAE,CACP,GAAGA,qBAAO,EACV;IACEH,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EACT,oHAAoH;IACtHG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,uDAAuD;IACpEG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EAAE,+BAA+B;IAC5CG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT,wEAAwE,GACxE;EACJ,CAAC,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EACT,0FAA0F;IAC5FG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,wDAAwD;IACrEI,KAAK,EAAEC;EACT,CAAC;AAEL,CAAC;AAAA"}