﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport CommandesFournisseursChart fromimport CommandesFournisseursChart from './CommandesFournisseursChart';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

// Définir une interface TypeScriptinterface DonneesMensuelles
interface DonneesMensuelles {
  mois: string;
  nombreCommandes: number;
  nombreFournisseurs: number;
}

// Définir une interface TypeScriptinterface StatistiquesData
interface StatistiquesData {
  donneesMensuelles: DonneesMensuelles[];
  totalCommandes: number;
  totalFournisseurs: number;
  pourcentageChangement: number;
  pourcentageChangementFournisseurs?: number;
}

// Définir une interface TypeScriptinterface CommandesFournisseursProps
interface CommandesFournisseursProps {
  sx?: SxProps;
}

// Créer une constanteconst CommandesFournisseurs =
const CommandesFournisseurs = ({ sx }: CommandesFournisseursProps) => {
  const [data, setData] = useState<StatistiquesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  // Fonction pour normaliser les pourcentages (limiter à ±100%)
  // Créer une constanteconst normaliserPourcentage =  const normaliserPourcentage = (pourcentage: number): number => {
    // Limiter à ±100%
    // Créer une constanteconst valeurLimitee =    const valeurLimitee = Math.max(Math.min(pourcentage, 100), -100);
    // Arrondir à 1 décimale
    return Math.round(valeurLimitee * 10) / 10;
  };

  useEffect(() => {
    // Créer une constanteconst fetchData =    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Récupérer les données de base
        // Créer une constanteconst response =        const response = await axios.get(
          `${API_URL}/api/statistiques/commandes-fournisseurs-mensuels`,
        );

        // Calculer le pourcentage de changement pour les fournisseurs
        let pourcentageChangementFournisseurs = 0;
        // Créer une constanteconst donneesMensuelles =        const donneesMensuelles = response.data.donneesMensuelles;

        if (donneesMensuelles && donneesMensuelles.length >= 2) {
          // Créer une constanteconst moisActuel =          const moisActuel = donneesMensuelles[donneesMensuelles.length - 1];
          // Créer une constanteconst moisPrecedent =          const moisPrecedent = donneesMensuelles[donneesMensuelles.length - 2];

          if (moisPrecedent.nombreFournisseurs > 0) {
            pourcentageChangementFournisseurs =
              ((moisActuel.nombreFournisseurs - moisPrecedent.nombreFournisseurs) /
                moisPrecedent.nombreFournisseurs) *
              100;
          }
        }

        // Normaliser les pourcentages
        // Créer une constanteconst pourcentageCommandesNormalise =        const pourcentageCommandesNormalise = normaliserPourcentage(
          response.data.pourcentageChangementCommandes,
        );
        // Créer une constanteconst pourcentageFournisseursNormalise =        const pourcentageFournisseursNormalise = normaliserPourcentage(
          pourcentageChangementFournisseurs,
        );

        // Ajouter les pourcentages normalisés aux données
        // Créer une constanteconst dataWithNormalizedPercentages =        const dataWithNormalizedPercentages = {
          ...response.data,
          pourcentageChangement: pourcentageCommandesNormalise,
          pourcentageChangementFournisseurs: pourcentageFournisseursNormalise,
        };

        setData(dataWithNormalizedPercentages);
      } catch (err) {
        console.error('Erreur lors de la récupération des statistiques:', err);
        setError('Impossible de charger les statistiques');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Préparer les données pour le graphique
  // Créer une constanteconst chartData =  const chartData = data?.donneesMensuelles
    ? {
        labels: data.donneesMensuelles.map((item) => item.mois),
        commandes: data.donneesMensuelles.map((item) => item.nombreCommandes),
        fournisseurs: data.donneesMensuelles.map((item) => item.nombreFournisseurs),
      }
    : { labels: [], commandes: [], fournisseurs: [] };

  return (
    <Box component={Paper} height={{ xs: 450, sm: 350 }} p={3} sx={{ ...sx }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1rem' }}>
          {new Date().toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' })}
        </Typography>
        <IconifyIcon icon="ic:round-bar-chart" color="primary.main" fontSize={24} />
      </Stack>

      <Stack direction="row" spacing={3} height="calc(100% - 60px)">
        {/* Colonne de gauche - Informations textuelles */}
        <Stack width="30%" justifyContent="space-between">
          <Box>
            <Typography
              variant="h2"
              color="text.primary"
              fontWeight={600}
              sx={{ fontSize: '3rem', mb: 0 }}
            >
              {loading ? '...' : data?.totalCommandes || 0}
            </Typography>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.9rem' }}>
                Commandes
              </Typography>
              {data && (
                <Stack direction="row" alignItems="center" sx={{ mt: -6 }}>
                  <IconifyIcon
                    icon="mdi:triangle"
                    color={data.pourcentageChangement >= 0 ? 'success.main' : 'error.main'}
                    fontSize="small"
                    sx={{
                      transform:
                        data.pourcentageChangement >= 0 ? 'rotate(0deg)' : 'rotate(180deg)',
                      fontSize: '0.8rem',
                      mr: 0.5,
                    }}
                  />
                  <Typography
                    variant="caption"
                    color={data.pourcentageChangement >= 0 ? 'success.main' : 'error.main'}
                    fontWeight={600}
                  >
                    {Math.abs(data.pourcentageChangement)}%
                  </Typography>
                </Stack>
              )}
            </Stack>

            <Box sx={{ mt: 4 }}>
              <Typography
                variant="h3"
                color="text.primary"
                fontWeight={600}
                sx={{ fontSize: '2rem', mb: 0 }}
              >
                {loading ? '...' : data?.totalFournisseurs || 0}
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.9rem' }}>
                  Fournisseurs
                </Typography>
                {data && data.pourcentageChangementFournisseurs !== undefined && (
                  <Stack direction="row" alignItems="center" sx={{ mt: -6 }}>
                    <IconifyIcon
                      icon="mdi:triangle"
                      color={
                        data.pourcentageChangementFournisseurs >= 0 ? 'success.main' : 'error.main'
                      }
                      fontSize="small"
                      sx={{
                        transform:
                          data.pourcentageChangementFournisseurs >= 0
                            ? 'rotate(0deg)'
                            : 'rotate(180deg)',
                        fontSize: '0.8rem',
                        mr: 0.5,
                      }}
                    />
                    <Typography
                      variant="caption"
                      color={
                        data.pourcentageChangementFournisseurs >= 0 ? 'success.main' : 'error.main'
                      }
                      fontWeight={600}
                    >
                      {Math.abs(data.pourcentageChangementFournisseurs)}%
                    </Typography>
                  </Stack>
                )}
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                <IconifyIcon
                  icon="mdi:check-circle"
                  color="success.main"
                  sx={{ fontSize: '1.5rem' }}
                />
                <Typography variant="body1" color="success.main" fontWeight={600}>
                  En bonne voie
                </Typography>
              </Stack>
            </Box>
          </Box>
        </Stack>

        {/* Colonne de droite - Graphique */}
        <Box sx={{ width: '60%', height: '100%' }}>
          {loading ? (
            <Box display="flex" alignItems="center" justifyContent="center" height="100%">
              <Typography>Chargement...</Typography>
            </Box>
          ) : error ? (
            <Box display="flex" alignItems="center" justifyContent="center" height="100%">
              <Typography color="error">{error}</Typography>
            </Box>
          ) : (
            <CommandesFournisseursChart data={chartData} sx={{ width: '100%', height: '100%' }} />
          )}
        </Box>
      </Stack>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default CommandesFournisseurs;
