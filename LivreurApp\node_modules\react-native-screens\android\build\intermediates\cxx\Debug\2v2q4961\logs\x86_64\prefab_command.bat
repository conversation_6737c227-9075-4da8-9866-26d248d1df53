@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging14351346153763993174\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab"
