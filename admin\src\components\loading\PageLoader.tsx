// Importer des fonctionnalités depuis un autre fichierimport { StackOwnProps } fromimport { StackOwnProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport LinearProgress fromimport LinearProgress from '@mui/material/LinearProgress';

// Créer une constanteconst PageLoader =
const PageLoader = (props: StackOwnProps) => {
  return (
    <Stack justifyContent="center" alignItems="center" height="100vh" {...props}>
      <Box width={1 / 2}>
        <LinearProgress />
      </Box>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default PageLoader;
