﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, PropsWithChildren } fromimport { useState, PropsWithChildren } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Sidebar fromimport Sidebar from 'layouts/main-layout/sidebar';
// Importer des fonctionnalités depuis un autre fichierimport Topbar fromimport Topbar from './topbar';
// Importer des fonctionnalités depuis un autre fichierimport Footer fromimport Footer from './footer';

// Créer une constanteconst MainLayout =
const MainLayout = ({ children }: PropsWithChildren) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  return (
    <Stack width={1} minHeight="100vh">
      <Sidebar mobileOpen={mobileOpen} setMobileOpen={setMobileOpen} setIsClosing={setIsClosing} />
      <Stack
        component="main"
        direction="column"
        px={3.5}
        flexGrow={1}
        width={{ xs: 1, lg: 'calc(100% - 290px)' }}
      >
        <Topbar isClosing={isClosing} mobileOpen={mobileOpen} setMobileOpen={setMobileOpen} />
        {children}
        <Footer />
      </Stack>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default MainLayout;
