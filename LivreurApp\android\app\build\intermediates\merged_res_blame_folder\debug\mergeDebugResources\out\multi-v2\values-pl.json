{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4280,4384,4552,4674,4784,4935,5060,5171,5410,5581,5690,5865,5993,6152,6313,6382,6448", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4379,4547,4669,4779,4930,5055,5166,5265,5576,5685,5860,5988,6147,6308,6377,6443,6527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "391,506,608,716,802,909,1028,1107,1183,1274,1367,1462,1556,1657,1750,1845,1940,2031,2122,2204,2313,2413,2512,2621,2733,2844,3007,11189", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "501,603,711,797,904,1023,1102,1178,1269,1362,1457,1551,1652,1745,1840,1935,2026,2117,2199,2308,2408,2507,2616,2728,2839,3002,3098,11267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,205,274,359,435,514,597,692,762,847,933,1008,1090,1173,1251,1323,1393,1479,1557,1633,1707", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "127,200,269,354,430,509,592,687,757,842,928,1003,1085,1168,1246,1318,1388,1474,1552,1628,1702,1782"}, "to": {"startLines": "46,67,69,70,84,85,120,121,122,123,125,126,127,128,129,130,131,132,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,6677,6814,6883,7936,8012,10856,10939,11034,11104,11272,11358,11433,11515,11598,11676,11748,11818,12005,12083,12159,12233", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "4275,6745,6878,6963,8007,8086,10934,11029,11099,11184,11353,11428,11510,11593,11671,11743,11813,11899,12078,12154,12228,12308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,341,420,505,622,704,768,849,913,974,1085,1153,1207,1276,1338,1392,1503,1564,1626,1680,1752,1881,1970,2052,2171,2253,2336,2423,2490,2556,2627,2703,2792,2869,2947,3025,3101,3191,3264,3359,3456,3528,3602,3702,3754,3820,3908,3998,4060,4124,4187,4294,4383,4482,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "336,415,500,617,699,763,844,908,969,1080,1148,1202,1271,1333,1387,1498,1559,1621,1675,1747,1876,1965,2047,2166,2248,2331,2418,2485,2551,2622,2698,2787,2864,2942,3020,3096,3186,3259,3354,3451,3523,3597,3697,3749,3815,3903,3993,4055,4119,4182,4289,4378,4477,4565,4641"}, "to": {"startLines": "2,35,43,44,45,65,66,68,71,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3103,3919,4004,4121,6532,6596,6750,6968,7029,7140,7208,7262,7331,7393,7447,7558,7619,7681,7735,7807,8091,8180,8262,8381,8463,8546,8633,8700,8766,8837,8913,9002,9079,9157,9235,9311,9401,9474,9569,9666,9738,9812,9912,9964,10030,10118,10208,10270,10334,10397,10504,10593,10692,10780", "endLines": "7,35,43,44,45,65,66,68,71,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "386,3177,3999,4116,4198,6591,6672,6809,7024,7135,7203,7257,7326,7388,7442,7553,7614,7676,7730,7802,7931,8175,8257,8376,8458,8541,8628,8695,8761,8832,8908,8997,9074,9152,9230,9306,9396,9469,9564,9661,9733,9807,9907,9959,10025,10113,10203,10265,10329,10392,10499,10588,10687,10775,10851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5270", "endColumns": "139", "endOffsets": "5405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "36,37,38,39,40,41,42,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3182,3279,3381,3479,3578,3692,3797,11904", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3274,3376,3474,3573,3687,3792,3914,12000"}}]}]}