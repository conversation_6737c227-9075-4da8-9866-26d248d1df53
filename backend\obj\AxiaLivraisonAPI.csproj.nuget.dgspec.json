{"format": 1, "restore": {"E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj": {}}, "projects": {"E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj", "projectName": "AxiaLivraisonAPI", "projectPath": "E:\\PFE\\Test\\AxiaLivraison\\backend\\AxiaLivraisonAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\PFE\\Test\\AxiaLivraison\\backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "MailKit": {"target": "Package", "version": "[4.12.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.StaticFiles": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.FileProviders.Physical": {"target": "Package", "version": "[9.0.3, )"}, "MimeKit": {"target": "Package", "version": "[4.12.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.4, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}