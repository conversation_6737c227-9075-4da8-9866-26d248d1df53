import React, {useState, useRef, useEffect} from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  View,
  Dimensions,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StatusBar,
  Animated,
  Easing,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Fontisto from 'react-native-vector-icons/Fontisto';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

const {width, height} = Dimensions.get('window');

const API_BASE_URL = Platform.select({
  android: 'http://*************:5283', // My computer's IP address
  ios: 'http://localhost:5283',
  default: 'http://localhost:5283',
});

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

const LoginScreen = ({navigation}) => {
  // Form state
  const [identifiant, setIdentifiant] = useState('');
  const [motDePasse, setMotDePasse] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState({
    username: false,
    password: false,
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const formOpacity = useRef(new Animated.Value(0)).current;
  const formSlide = useRef(new Animated.Value(50)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const inputUsernameAnim = useRef(new Animated.Value(0)).current;
  const inputPasswordAnim = useRef(new Animated.Value(0)).current;

  // Animation functions
  useEffect(() => {
    // Simplified initial animations
    Animated.parallel([
      // Fade in header
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Slide in header
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in form with delay
      Animated.timing(formOpacity, {
        toValue: 1,
        duration: 600,
        delay: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Slide in form with delay
      Animated.timing(formSlide, {
        toValue: 0,
        duration: 600,
        delay: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
    ]).start();

    // Set up keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // Animation when keyboard shows
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // Animation when keyboard hides
      },
    );

    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [fadeAnim, slideAnim, formOpacity, formSlide]);

  // Input focus handlers - simplified
  const handleFocus = (field) => {
    setIsFocused(prev => ({...prev, [field]: true}));

    if (field === 'username') {
      Animated.timing(inputUsernameAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }).start();
    } else if (field === 'password') {
      Animated.timing(inputPasswordAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }).start();
    }
  };

  const handleBlur = (field) => {
    setIsFocused(prev => ({...prev, [field]: false}));

    if (field === 'username') {
      Animated.timing(inputUsernameAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: false,
      }).start();
    } else if (field === 'password') {
      Animated.timing(inputPasswordAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: false,
      }).start();
    }
  };

  // Button animation
  const handlePressIn = () => {
    Animated.spring(buttonScale, {
      toValue: 0.95,
      useNativeDriver: true,
      friction: 5,
      tension: 40,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(buttonScale, {
      toValue: 1,
      useNativeDriver: true,
      friction: 3,
      tension: 40,
    }).start();
  };

  const handleLogin = async () => {
    if (!identifiant.trim() || !motDePasse.trim()) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    setLoading(true);

    try {
      // Authentification avec axios
      const response = await axiosInstance.post('/api/authentification/connexion-livreur', {
        Identifiant: identifiant,
        MotDePasse: motDePasse,
      });

      const authResponse = response.data;

      console.log('Réponse du serveur:', authResponse);

      if (authResponse?.nom) {
        // Sauvegarder les données utilisateur avec AsyncStorage
        const userData = {
          id: authResponse.userId,
          nom: authResponse.nom,
          email: authResponse.email,
          telephone: authResponse.telephone,
          image: authResponse.imagePath ? `${API_BASE_URL}${authResponse.imagePath}` : null,
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
        };

        await AsyncStorage.setItem('user', JSON.stringify(userData));
        await AsyncStorage.setItem('accessToken', authResponse.accessToken);
        if (authResponse.refreshToken) {
          await AsyncStorage.setItem('refreshToken', authResponse.refreshToken);
        }

        navigation.navigate('Accueil', { user: userData });
      } else {
        throw new Error(authResponse?.message || 'Données utilisateur manquantes dans la réponse');
      }
    } catch (error) {
      let errorTitle = '🔐 Connexion échouée';
      let errorMessage = '';

      if (error.response) {
        const status = error.response.status;
        const serverMessage = error.response.data?.message || '';

        if (status === 401 || status === 403) {
          errorTitle = '🔒 Accès refusé';
          errorMessage = 'L\'identifiant ou le mot de passe est incorrect.';
        } else if (status === 404) {
          errorTitle = '👤 Utilisateur introuvable';
          errorMessage = 'Aucun compte livreur trouvé avec cet identifiant.\n\nContactez votre administrateur si vous pensez que c\'est une erreur.';
        } else if (status === 500) {
          errorTitle = '⚠️ Erreur serveur';
          errorMessage = 'Le serveur rencontre un problème temporaire.\n\nVeuillez réessayer dans quelques instants.';
        } else {
          errorTitle = '🚫 Erreur de connexion';
          errorMessage = serverMessage || 'Une erreur inattendue s\'est produite.\n\nVeuillez réessayer.';
        }
      } else if (error.request) {
        errorTitle = '🌐 Problème de réseau';
        errorMessage = 'Impossible de contacter le serveur.\n\nVérifiez :\n• Votre connexion internet\n• Que le serveur est accessible\n• Que vous êtes sur le bon réseau';
      } else {
        errorTitle = '⚠️ Erreur technique';
        errorMessage = 'Une erreur technique s\'est produite.\n\nVeuillez réessayer ou contacter le support.';
      }

      Alert.alert(errorTitle, errorMessage, [
        {
          text: '🔄 Réessayer',
          style: 'default',
          onPress: () => {
            // Relancer la tentative de connexion
            setIdentifiant('');
            setMotDePasse('');
          }
        },
        {
          text: '✅ OK',
          style: 'cancel'
        }
      ]);

      // Log silencieux pour le développement (ne s'affiche pas à l'utilisateur)
      if (__DEV__) {
        console.log('Erreur de connexion (dev only):', error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <StatusBar backgroundColor="#FFAA80" barStyle="light-content" />
        <LinearGradient
          colors={['#FFAA80', '#FFD6C2', '#FFF5F0']}
          style={styles.gradientBackground}
          start={{x: 0, y: 0}}
          end={{x: 0, y: 0.5}}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            style={styles.keyboardContainer}>
            <View style={styles.contentContainer}>
            {/* Animated Header */}
            <Animated.View
              style={[
                styles.headerContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}>
              <View style={styles.iconBackground}>
                <MaterialIcons name="delivery-dining" size={30} color="#FFFFFF" />
              </View>
              <Text style={styles.headerTitle}>Axia Livraison</Text>
            </Animated.View>

            {/* Animated Form Container */}
            <Animated.View
              style={[
                styles.formContainer,
                {
                  opacity: formOpacity,
                  transform: [{ translateY: formSlide }]
                }
              ]}>
              <View style={styles.logoContainer}>
                <Image
                  source={require('../assets/images/logo.png')}
                  style={styles.logo}
                />
              </View>

              <Text style={styles.welcomeText}>Connectez-vous à votre compte</Text>

              {/* Username Input with Animation */}
              <Animated.View
                style={[
                  styles.inputContainer,
                  {
                    borderColor: inputUsernameAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['#E0E0E0', '#FFAA80']
                    }),
                    transform: [
                      {
                        scale: inputUsernameAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [1, 1.02]
                        })
                      }
                    ]
                  }
                ]}>
                <FontAwesome
                  name="user"
                  size={18}
                  color="#FFAA80"
                  style={styles.inputIcon}
                />
                <TextInput
                  placeholder="Identifiant"
                  style={styles.textInput}
                  placeholderTextColor="#9A9A9A"
                  value={identifiant}
                  onChangeText={setIdentifiant}
                  autoCapitalize="none"
                  onFocus={() => handleFocus('username')}
                  onBlur={() => handleBlur('username')}
                />
              </Animated.View>

              {/* Password Input with Animation */}
              <Animated.View
                style={[
                  styles.inputContainer,
                  {
                    borderColor: inputPasswordAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['#E0E0E0', '#FFAA80']
                    }),
                    transform: [
                      {
                        scale: inputPasswordAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [1, 1.02]
                        })
                      }
                    ]
                  }
                ]}>
                <Fontisto
                  name="locked"
                  size={18}
                  color="#FFAA80"
                  style={styles.inputIcon}
                />
                <TextInput
                  placeholder="Mot de passe"
                  style={styles.textInput}
                  secureTextEntry={!showPassword}
                  placeholderTextColor="#9A9A9A"
                  value={motDePasse}
                  onChangeText={setMotDePasse}
                  onFocus={() => handleFocus('password')}
                  onBlur={() => handleBlur('password')}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeButton}>
                  <MaterialIcons
                    name={showPassword ? "visibility-off" : "visibility"}
                    size={20}
                    color="#FFAA80"
                  />
                </TouchableOpacity>
              </Animated.View>

              {/* Animated Login Button */}
              <Animated.View style={{
                transform: [{ scale: buttonScale }],
                width: '100%'
              }}>
                <TouchableOpacity
                  style={styles.loginButton}
                  onPress={handleLogin}
                  onPressIn={handlePressIn}
                  onPressOut={handlePressOut}
                  disabled={loading}>
                  {loading ? (
                    <ActivityIndicator color="#FFFFFF" size="small" />
                  ) : (
                    <Text style={styles.loginButtonText}>Se connecter</Text>
                  )}
                </TouchableOpacity>
              </Animated.View>
            </Animated.View>

            {/* Footer */}
            <Animated.Text
              style={[
                styles.footerText,
                { opacity: fadeAnim }
              ]}>
              © 2025 Axia Livraison. Tous droits réservés.
            </Animated.Text>
            </View>
          </KeyboardAvoidingView>
        </LinearGradient>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradientBackground: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  keyboardContainer: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    minHeight: '100%',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    paddingVertical: 10,
  },
  iconBackground: {
    width: 54,
    height: 54,
    borderRadius: 27,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#FFAA80',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#995623',
    fontFamily: Platform.OS === 'android' ? 'Roboto' : 'Avenir',
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: {width: 1, height: 1},
    textShadowRadius: 2,
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: '#FFD6C2',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 130,
    height: 85,
    resizeMode: 'contain',
  },
  welcomeText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    color: '#666',
    marginBottom: 24,
    fontFamily: Platform.OS === 'android' ? 'Roboto' : 'Avenir',
    letterSpacing: 0.3,
  },
  inputContainer: {
    flexDirection: 'row',
    borderRadius: 14,
    borderWidth: 1.5,
    borderColor: '#E0E0E0',
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    height: 54,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    fontFamily: Platform.OS === 'android' ? 'Roboto' : 'Avenir',
    paddingVertical: 10,
  },
  eyeButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginButton: {
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    backgroundColor: '#FFAA80',
    borderRadius: 16,
    marginTop: 20,
    shadowColor: '#FFAA80',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 4,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: Platform.OS === 'android' ? 'Roboto' : 'Avenir',
    letterSpacing: 0.5,
  },
  footerText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    fontFamily: Platform.OS === 'android' ? 'Roboto' : 'Avenir',
    marginBottom: 12,
    letterSpacing: 0.2,
  },
});

export default LoginScreen;
