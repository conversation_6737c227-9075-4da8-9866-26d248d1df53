// Importer des fonctionnalités depuis un autre fichierimport React, { useEffect, useState } fromimport React, { useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport PermissionList fromimport PermissionList from './PermissionList';
// Importer des fonctionnalités depuis un autre fichierimport PermissionForm fromimport PermissionForm from './PermissionForm';
// Importer des fonctionnalités depuis un autre fichierimport { Permission } fromimport { Permission } from './types';

const GestionPermission: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [openPermissionDialog, setOpenPermissionDialog] = useState<boolean>(false);
  const [currentPermission, setCurrentPermission] = useState<Permission | null>(null);
  // Créer une constanteconst API_URL =  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    fetchPermissions();
  }, []);

  // Créer une constanteconst fetchPermissions =
  const fetchPermissions = async () => {
    try {
      // Créer une constanteconst response =      const response = await axios.get(`${API_URL}/api/permissions/liste`);
      setPermissions(response.data);
    } catch (error) {
      console.error('Erreur lors de la récupération des permissions :', error);
    }
  };

  // Créer une constanteconst handleSavePermission =
  const handleSavePermission = async (permissionData: {
    permissionName: string;
    description: string;
  }) => {
    try {
      if (currentPermission) {
        await axios.put(
          `${API_URL}/api/permissions/modifier/${currentPermission.id}`,
          permissionData,
        );
      } else {
        await axios.post(`${API_URL}/api/permissions/ajouter`, permissionData);
      }
      fetchPermissions();
      setOpenPermissionDialog(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la permission :', error);
    }
  };

  // Créer une constanteconst handleDeletePermission =
  const handleDeletePermission = async (id: number) => {
    try {
      console.log('Deleting permission with ID:', id);
      await axios.delete(`${API_URL}/api/permissions/supprimer/${id}`);
      console.log('Permission deleted successfully');
      fetchPermissions();
    } catch (error) {
      console.error('Erreur lors de la suppression de la permission :', error);
      if (axios.isAxiosError(error)) {
        console.error('Response data:', error.response?.data);
        console.error('Status:', error.response?.status);
      }
    }
  };

  return (
    <>
      <PermissionList
        permissions={permissions}
        onAddPermission={() => {
          setCurrentPermission(null);
          setOpenPermissionDialog(true);
        }}
        onEditPermission={(permission) => {
          setCurrentPermission(permission);
          setOpenPermissionDialog(true);
        }}
        onDeletePermission={handleDeletePermission}
      />

      <PermissionForm
        open={openPermissionDialog}
        onClose={() => setOpenPermissionDialog(false)}
        onSubmit={handleSavePermission}
        initialData={currentPermission || undefined}
      />
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default GestionPermission;
