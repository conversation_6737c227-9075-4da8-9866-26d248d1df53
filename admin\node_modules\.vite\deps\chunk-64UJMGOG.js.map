{"version": 3, "sources": ["../../@mui/material/DialogTitle/dialogTitleClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogTitleUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogTitle', slot);\n}\nconst dialogTitleClasses = generateUtilityClasses('MuiDialogTitle', ['root']);\nexport default dialogTitleClasses;"], "mappings": ";;;;;;AAEO,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACA,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,MAAM,CAAC;AAC5E,IAAO,6BAAQ;", "names": []}