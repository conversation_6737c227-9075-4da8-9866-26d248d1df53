// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';

// Définir une interface TypeScriptinterface TopFournisseur
interface TopFournisseur {
  fournisseurId: number;
  nomFournisseur: string;
  nombreCommandes: number;
  montantTotal: number;
}

// Créer une constanteconst TopFournisseur =
const TopFournisseur = () => {
  const [topFournisseur, setTopFournisseur] = useState<TopFournisseur | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchTopFournisseur =    const fetchTopFournisseur = async () => {
      try {
        setLoading(true);
        setError(null);

        // Créer une constanteconst response =
        const response = await axios.get<TopFournisseur>(
          `${API_URL}/api/statistiques/top-fournisseur`,
        );

        setTopFournisseur(response.data);
      } catch (err) {
        console.error('Erreur lors de la récupération du top fournisseur:', err);
        setError('Erreur de chargement');
      } finally {
        setLoading(false);
      }
    };

    fetchTopFournisseur();
  }, []);

  // Formater le montant en devise
  // Créer une constanteconst formatMontant =  const formatMontant = (montant: number) => {
    // Utiliser le formateur de base
    // Créer une constanteconst formatted =    const formatted = new Intl.NumberFormat('fr-TN', {
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(montant);

    // Ajouter manuellement le symbole DT
    return `${formatted} DT`;
  };

  return (
    <Stack component={Paper} p={1.5} spacing={1.5} height={100}>
      <div style={{ width: '100%' }}>
        <Typography variant="body2" color="text.disabled" noWrap>
          Top Fournisseur
        </Typography>
        {loading ? (
          <Typography mt={0.25} variant="h3">
            ...
          </Typography>
        ) : error ? (
          <Typography mt={0.25} variant="body2" color="error.main">
            {error}
          </Typography>
        ) : (
          <>
            <Typography mt={0.25} variant="h3" noWrap sx={{ maxWidth: '100%' }}>
              {topFournisseur?.nomFournisseur || 'Aucun'}
            </Typography>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mt={0.5}>
              <Typography variant="caption" fontWeight="bold" color="success.main">
                {topFournisseur ? formatMontant(topFournisseur.montantTotal) : '0 DT'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {topFournisseur?.nombreCommandes || 0} commandes
              </Typography>
            </Stack>
          </>
        )}
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default TopFournisseur;
