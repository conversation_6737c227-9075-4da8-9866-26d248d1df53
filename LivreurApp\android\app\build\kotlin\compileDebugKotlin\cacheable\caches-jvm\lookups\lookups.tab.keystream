  Application android.app  DefaultReactActivityDelegate android.app.Activity  
fabricEnabled android.app.Activity  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  apply android.app.Application  getDefaultReactHost android.app.Application  load android.app.Application  onCreate android.app.Application  Boolean android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  String android.content.Context  apply android.content.Context  
fabricEnabled android.content.Context  getDefaultReactHost android.content.Context  load android.content.Context  Boolean android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  getDefaultReactHost android.content.ContextWrapper  load android.content.ContextWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  apply "com.facebook.react.ReactNativeHost  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  getDefaultReactHost ,com.facebook.react.defaults.DefaultReactHost  OpenSourceMergedSoMapping com.facebook.react.soloader  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  Application com.livreurapp  Boolean com.livreurapp  BuildConfig com.livreurapp  DefaultReactActivityDelegate com.livreurapp  DefaultReactNativeHost com.livreurapp  List com.livreurapp  MainActivity com.livreurapp  MainApplication com.livreurapp  OpenSourceMergedSoMapping com.livreurapp  PackageList com.livreurapp  
ReactActivity com.livreurapp  ReactActivityDelegate com.livreurapp  ReactApplication com.livreurapp  	ReactHost com.livreurapp  ReactNativeHost com.livreurapp  ReactPackage com.livreurapp  SoLoader com.livreurapp  String com.livreurapp  apply com.livreurapp  
fabricEnabled com.livreurapp  getDefaultReactHost com.livreurapp  load com.livreurapp  DEBUG com.livreurapp.BuildConfig  IS_HERMES_ENABLED com.livreurapp.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED com.livreurapp.BuildConfig  DefaultReactActivityDelegate com.livreurapp.MainActivity  
fabricEnabled com.livreurapp.MainActivity  mainComponentName com.livreurapp.MainActivity  BuildConfig com.livreurapp.MainApplication  OpenSourceMergedSoMapping com.livreurapp.MainApplication  PackageList com.livreurapp.MainApplication  SoLoader com.livreurapp.MainApplication  applicationContext com.livreurapp.MainApplication  apply com.livreurapp.MainApplication  getDefaultReactHost com.livreurapp.MainApplication  load com.livreurapp.MainApplication  reactNativeHost com.livreurapp.MainApplication  	Function1 kotlin  apply kotlin  List kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       