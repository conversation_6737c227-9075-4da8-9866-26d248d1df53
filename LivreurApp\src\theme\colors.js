// Centralized color theme for LivreurApp
// Based on the login page design with orange primary colors

export const colors = {
  // Primary brand colors (from login page)
  primary: {
    main: '#FFAA80',        // Main orange from login
    light: '#FFD6C2',       // Light orange from gradient
    lighter: '#FFF5F0',     // Lightest orange from gradient
    dark: '#FF8A50',        // Darker orange for hover states
    darker: '#E6956B',      // Even darker for pressed states
  },

  // Secondary colors that complement the orange theme
  secondary: {
    main: '#995623',        // Brown from login header text
    light: '#B8713D',       // Lighter brown
    dark: '#7A4419',        // Darker brown
  },

  // Neutral colors
  neutral: {
    white: '#FFFFFF',
    background: '#F8F9FA',   // Light gray background
    surface: '#FFFFFF',      // Card/surface background
    border: '#E0E0E0',       // Light border
    borderLight: '#F0F0F0',  // Very light border
    divider: '#E2E8F0',      // Divider lines
  },

  // Text colors
  text: {
    primary: '#1E293B',      // Dark text
    secondary: '#64748B',    // Medium gray text
    tertiary: '#9A9A9A',     // Light gray text (placeholders)
    inverse: '#FFFFFF',      // White text on dark backgrounds
    brown: '#995623',        // Brown text (from login)
    dark: '#262626',         // Very dark text
    medium: '#334155',       // Medium dark text
    light: '#666666',        // Light text
  },

  // Status colors
  status: {
    success: '#10B981',      // Green for "Livré"
    warning: '#F59E0B',      // Amber for "En préparation"
    info: '#3B82F6',         // Blue for "En transit"
    error: '#EF4444',        // Red for errors
    neutral: '#64748B',      // Gray for unknown status
  },

  // Status background colors (lighter versions)
  statusBackground: {
    success: '#E6F4EA',      // Light green background
    warning: '#FEF3C7',      // Light amber background
    info: '#DBEAFE',         // Light blue background
    error: '#FEE2E2',        // Light red background
    neutral: '#F1F5F9',      // Light gray background
  },

  // Shadow colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.05)',
    medium: 'rgba(0, 0, 0, 0.1)',
    dark: 'rgba(0, 0, 0, 0.15)',
    primary: 'rgba(255, 170, 128, 0.25)', // Orange shadow
  },

  // Gradient definitions
  gradients: {
    primary: ['#FFAA80', '#FFD6C2', '#FFF5F0'], // Login page gradient
    primaryReverse: ['#FFF5F0', '#FFD6C2', '#FFAA80'],
    subtle: ['#FFFFFF', '#F8F9FA'],
  },
};

// Helper function to get status color
export const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'livré':
      return colors.status.success;
    case 'en transit':
      return colors.status.info;
    case 'en préparation':
      return colors.status.warning;
    default:
      return colors.status.neutral;
  }
};

// Helper function to get status background color
export const getStatusBackgroundColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'livré':
      return colors.statusBackground.success;
    case 'en transit':
      return colors.statusBackground.info;
    case 'en préparation':
      return colors.statusBackground.warning;
    default:
      return colors.statusBackground.neutral;
  }
};

// Exporter comme élément principal de ce fichierexport default
export default colors;
