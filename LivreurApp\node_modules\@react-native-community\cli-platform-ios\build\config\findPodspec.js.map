{"version": 3, "names": ["findPodspec", "folder", "podspecs", "glob", "sync", "cwd", "length", "packagePodspec", "path", "basename", "podspecFile", "includes", "join"], "sources": ["../../src/config/findPodspec.ts"], "sourcesContent": ["import glob from 'glob';\nimport path from 'path';\n\nexport default function findPodspec(folder: string): string | null {\n  const podspecs = glob.sync('*.podspec', {cwd: folder});\n\n  if (podspecs.length === 0) {\n    return null;\n  }\n\n  const packagePodspec = path.basename(folder) + '.podspec';\n  const podspecFile = podspecs.includes(packagePodspec)\n    ? packagePodspec\n    : podspecs[0];\n\n  return path.join(folder, podspecFile);\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAET,SAASA,WAAW,CAACC,MAAc,EAAiB;EACjE,MAAMC,QAAQ,GAAGC,eAAI,CAACC,IAAI,CAAC,WAAW,EAAE;IAACC,GAAG,EAAEJ;EAAM,CAAC,CAAC;EAEtD,IAAIC,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EAEA,MAAMC,cAAc,GAAGC,eAAI,CAACC,QAAQ,CAACR,MAAM,CAAC,GAAG,UAAU;EACzD,MAAMS,WAAW,GAAGR,QAAQ,CAACS,QAAQ,CAACJ,cAAc,CAAC,GACjDA,cAAc,GACdL,QAAQ,CAAC,CAAC,CAAC;EAEf,OAAOM,eAAI,CAACI,IAAI,CAACX,MAAM,EAAES,WAAW,CAAC;AACvC"}