ninja: Entering directory `E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\.cxx\Debug\5u705b2z\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring incomplete, errors occurred!
See also "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a/CMakeFiles/CMakeOutput.log".
FAILED: build.ninja 
C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SE:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BE:\PFE\Test\AxiaLivraison\LivreurApp\android\app\.cxx\Debug\5u705b2z\armeabi-v7a
