// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Interface pour les livreurs disponibles
// Définir une interface TypeScriptinterface LivreurDisponibleinterface LivreurDisponible {
  nombreLivreursDisponibles: number;
}

// Créer une constanteconst LivreurDisponible =
const LivreurDisponible = () => {
  const [livreursDisponibles, setLivreursDisponibles] = useState(0);
  const [loading, setLoading] = useState(true);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchLivreursDisponibles =    const fetchLivreursDisponibles = async () => {
      try {
        setLoading(true);
        // Utiliser l'endpoint qui compte les livreurs sans commandes
        // Créer une constanteconst response =        const response = await axios.get<LivreurDisponible>(
          `${API_URL}/api/statistiques/livreurs-disponibles`,
        );

        // Définir le nombre de livreurs disponibles
        setLivreursDisponibles(response.data.nombreLivreursDisponibles);
      } catch (err) {
        console.error('Erreur lors de la récupération des livreurs disponibles:', err);
        setLivreursDisponibles(0);
      } finally {
        setLoading(false);
      }
    };

    fetchLivreursDisponibles();
  }, []);

  return (
    <Stack component={Paper} p={2.5} alignItems="center" spacing={2.25} height={100}>
      <Stack
        alignItems="center"
        justifyContent="center"
        height={56}
        width={56}
        bgcolor="info.lighter"
        borderRadius="50%"
      >
        <IconifyIcon icon="mdi:account-group" fontSize="h2.fontSize" color="success.main" />
      </Stack>
      <div>
        <Typography variant="body2" color="text.disabled">
          Livreurs Disponibles
        </Typography>
        <Typography mt={0.25} variant="h3">
          {loading ? '...' : livreursDisponibles}
        </Typography>
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default LivreurDisponible;
