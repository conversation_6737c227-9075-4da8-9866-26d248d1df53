import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Platform } from 'react-native';

const API_BASE_URL = Platform.select({
  android: 'http://*************:5283',
  ios: 'http://localhost:5283',
  default: 'http://localhost:5283',
});

class AuthService {
  constructor() {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.initializeTokens();
  }

  async initializeTokens() {
    try {
      this.accessToken = await AsyncStorage.getItem('accessToken');
      this.refreshToken = await AsyncStorage.getItem('refreshToken');
      const expiry = await AsyncStorage.getItem('tokenExpiry');
      this.tokenExpiry = expiry ? new Date(expiry) : null;
    } catch (error) {
      console.error('Error loading tokens from storage:', error);
    }
  }

  async saveTokensToStorage(accessToken, refreshToken, expiry) {
    try {
      await AsyncStorage.setItem('accessToken', accessToken);
      await AsyncStorage.setItem('refreshToken', refreshToken);
      await AsyncStorage.setItem('tokenExpiry', expiry.toISOString());
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      this.tokenExpiry = expiry;
    } catch (error) {
      console.error('Error saving tokens to storage:', error);
    }
  }

  async clearTokensFromStorage() {
    try {
      await AsyncStorage.multiRemove([
        'accessToken',
        'refreshToken',
        'tokenExpiry',
        'user'
      ]);
      this.accessToken = null;
      this.refreshToken = null;
      this.tokenExpiry = null;
    } catch (error) {
      console.error('Error clearing tokens from storage:', error);
    }
  }

  async login(credentials) {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/authentification/connexion-livreur`,
        credentials,
        {
          timeout: 15000,
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        }
      );

      const authData = response.data;
      const expiry = new Date(authData.tokenExpiry);

      // Save tokens
      await this.saveTokensToStorage(authData.accessToken, authData.refreshToken, expiry);

      // Save user data
      const userData = {
        id: authData.userId,
        identifiant: authData.identifiant || credentials.Identifiant,
        nom: authData.nom,
        email: authData.email,
        telephone: authData.telephone,
        image: `${API_BASE_URL}${authData.imagePath}`,
      };
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      return authData;
    } catch (error) {
      throw error;
    }
  }

  async logout() {
    try {
      if (this.accessToken) {
        await axios.post(
          `${API_BASE_URL}/api/authentification/deconnexion`,
          {},
          {
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
            },
          }
        );
      }
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      await this.clearTokensFromStorage();
    }
  }

  async refreshAccessToken() {
    if (!this.refreshToken) {
      return null;
    }

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/authentification/refresh-token`,
        { refreshToken: this.refreshToken }
      );

      const authData = response.data;
      const expiry = new Date(authData.tokenExpiry);

      await this.saveTokensToStorage(authData.accessToken, authData.refreshToken, expiry);

      // Update user data
      const userData = {
        id: authData.userId,
        nom: authData.nom,
        email: authData.email,
        telephone: authData.telephone,
        image: `${API_BASE_URL}${authData.imagePath}`,
      };
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      return authData.accessToken;
    } catch (error) {
      console.error('Error refreshing token:', error);
      await this.clearTokensFromStorage();
      return null;
    }
  }

  getAccessToken() {
    return this.accessToken;
  }

  isTokenExpired() {
    if (!this.tokenExpiry) {
      return true;
    }
    return new Date() >= this.tokenExpiry;
  }

  isAuthenticated() {
    return this.accessToken !== null && !this.isTokenExpired();
  }

  async getValidToken() {
    if (!this.accessToken) {
      return null;
    }

    if (this.isTokenExpired()) {
      return await this.refreshAccessToken();
    }

    return this.accessToken;
  }

  // Create axios instance with automatic token handling
  createAuthenticatedAxios() {
    const axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Request interceptor to add auth token
    axiosInstance.interceptors.request.use(
      async (config) => {
        const token = await this.getValidToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token expiration
    axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await this.refreshAccessToken();
            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return axiosInstance(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, clear tokens
            await this.clearTokensFromStorage();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );

    return axiosInstance;
  }
}

export const authService = new AuthService();
