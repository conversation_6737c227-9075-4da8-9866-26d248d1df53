[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a\\android_gradle_build.json due to:", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a file changed", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (DELETED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a\\build.ninja (LAST_MODIFIED_CHANGED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a\\compile_commands.json (LAST_MODIFIED_CHANGED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt (LAST_MODIFIED_CHANGED)", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Java\\\\jdk-17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging8054638078013382102\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\072209afb1c52d43fe8bcfe0160879d7\\\\transformed\\\\react-android-0.79.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\b034aef65a970793e0e053d494c15a32\\\\transformed\\\\hermes-android-0.79.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\72cde7dc85b5006383f56c98fcfedfa5\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "keeping json folder 'E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a' but regenerating project", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HE:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5u705b2z\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5u705b2z\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5u705b2z\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BE:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5u705b2z\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HE:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5u705b2z\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5u705b2z\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5u705b2z\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BE:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5u705b2z\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=E:\\\\PFE\\\\Test\\\\AxiaLivraison\\\\LivreurApp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a\\compile_commands.json.bin normally", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\arm64-v8a\\compile_commands.json to E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]