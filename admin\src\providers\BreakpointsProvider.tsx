﻿// Importer des fonctionnalités depuis un autre fichierimport { createContext, PropsWithChildren, useContext, useEffect, useState } fromimport { createContext, PropsWithChildren, useContext, useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { Breakpoint, Theme } fromimport { Breakpoint, Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { useMediaQuery } fromimport { useMediaQuery } from '@mui/material';

// Définir une interface TypeScriptinterface BreakpointContextInterface
interface BreakpointContextInterface {
  currentBreakpoint: Breakpoint;
  up: (key: Breakpoint | number) => boolean;
  down: (key: Breakpoint | number) => boolean;
  only: (key: Breakpoint | number) => boolean;
  between: (start: Breakpoint | number, end: Breakpoint | number) => boolean;
}

export const BreakpointContext = createContext({} as BreakpointContextInterface);

// Créer une constanteconst BreakpointsProvider =
const BreakpointsProvider = ({ children }: PropsWithChildren) => {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('xs');
  // Créer une constanteconst up =  const up = (key: Breakpoint | number) =>
    useMediaQuery<Theme>((theme) => theme.breakpoints.up(key));

  // Créer une constanteconst down =
  const down = (key: Breakpoint | number) =>
    useMediaQuery<Theme>((theme) => theme.breakpoints.down(key));

  // Créer une constanteconst only =
  const only = (key: Breakpoint | number) =>
    useMediaQuery<Theme>((theme) => theme.breakpoints.only(key as Breakpoint));

  // Créer une constanteconst between =
  const between = (start: Breakpoint | number, end: Breakpoint | number) =>
    useMediaQuery<Theme>((theme) => theme.breakpoints.between(start, end));

  // Créer une constanteconst isXs =
  const isXs = between('xs', 'sm');
  // Créer une constanteconst isSm =  const isSm = between('sm', 'md');
  // Créer une constanteconst isMd =  const isMd = between('md', 'lg');
  // Créer une constanteconst isLg =  const isLg = between('lg', 'xl');
  // Créer une constanteconst isXl =  const isXl = up('xl');

  useEffect(() => {
    if (isXs) {
      setCurrentBreakpoint('xs');
    }
    if (isSm) {
      setCurrentBreakpoint('sm');
    }
    if (isMd) {
      setCurrentBreakpoint('md');
    }
    if (isLg) {
      setCurrentBreakpoint('lg');
    }
    if (isXl) {
      setCurrentBreakpoint('xl');
    }
  }, [isXs, isSm, isMd, isLg, isXl]);

  return (
    <BreakpointContext.Provider value={{ currentBreakpoint, up, down, only, between }}>
      {children}
    </BreakpointContext.Provider>
  );
};

export const useBreakpoints = () => useContext(BreakpointContext);

// Exporter comme élément principal de ce fichierexport default
export default BreakpointsProvider;
