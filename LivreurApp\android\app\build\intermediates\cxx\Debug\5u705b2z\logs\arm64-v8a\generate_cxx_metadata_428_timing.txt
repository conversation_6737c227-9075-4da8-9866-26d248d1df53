# C/C++ build system timings
generate_cxx_metadata
  [gap of 87ms]
  create-invalidation-state 311ms
  generate-prefab-packages
    [gap of 18ms]
    exec-prefab 4383ms
    [gap of 51ms]
  generate-prefab-packages completed in 4452ms
  execute-generate-process
    exec-configure 2456ms
    [gap of 1855ms]
  execute-generate-process completed in 4312ms
  [gap of 627ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 9857ms

