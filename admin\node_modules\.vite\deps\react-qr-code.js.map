{"version": 3, "sources": ["../../qr.js/lib/ErrorCorrectLevel.js", "../../qr.js/lib/mode.js", "../../qr.js/lib/8BitByte.js", "../../qr.js/lib/RSBlock.js", "../../qr.js/lib/BitBuffer.js", "../../qr.js/lib/math.js", "../../qr.js/lib/Polynomial.js", "../../qr.js/lib/util.js", "../../qr.js/lib/QRCode.js", "../../react-qr-code/lib/QRCodeSvg/index.js", "../../react-qr-code/lib/index.js"], "sourcesContent": ["module.exports = {\n\tL : 1,\n\tM : 0,\n\tQ : 3,\n\tH : 2\n};\n\n", "module.exports = {\n\tMODE_NUMBER :\t\t1 << 0,\n\tMODE_ALPHA_NUM : \t1 << 1,\n\tMODE_8BIT_BYTE : \t1 << 2,\n\tMODE_KANJI :\t\t1 << 3\n};\n", "var mode = require('./mode');\n\nfunction QR8bitByte(data) {\n\tthis.mode = mode.MODE_8BIT_BYTE;\n\tthis.data = data;\n}\n\nQR8bitByte.prototype = {\n\n\tgetLength : function(buffer) {\n\t\treturn this.data.length;\n\t},\n\t\n\twrite : function(buffer) {\n\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t// not JIS ...\n\t\t\tbuffer.put(this.data.charCodeAt(i), 8);\n\t\t}\n\t}\n};\n\nmodule.exports = QR8bitByte;\n\n", "// ErrorCorrectLevel\nvar ECL = require('./ErrorCorrectLevel');\n\nfunction QRRSBlock(totalCount, dataCount) {\n\tthis.totalCount = totalCount;\n\tthis.dataCount  = dataCount;\n}\n\nQRRSBlock.RS_BLOCK_TABLE = [\n\n\t// L\n\t// M\n\t// Q\n\t// H\n\n\t// 1\n\t[1, 26, 19],\n\t[1, 26, 16],\n\t[1, 26, 13],\n\t[1, 26, 9],\n\t\n\t// 2\n\t[1, 44, 34],\n\t[1, 44, 28],\n\t[1, 44, 22],\n\t[1, 44, 16],\n\n\t// 3\n\t[1, 70, 55],\n\t[1, 70, 44],\n\t[2, 35, 17],\n\t[2, 35, 13],\n\n\t// 4\t\t\n\t[1, 100, 80],\n\t[2, 50, 32],\n\t[2, 50, 24],\n\t[4, 25, 9],\n\t\n\t// 5\n\t[1, 134, 108],\n\t[2, 67, 43],\n\t[2, 33, 15, 2, 34, 16],\n\t[2, 33, 11, 2, 34, 12],\n\t\n\t// 6\n\t[2, 86, 68],\n\t[4, 43, 27],\n\t[4, 43, 19],\n\t[4, 43, 15],\n\t\n\t// 7\t\t\n\t[2, 98, 78],\n\t[4, 49, 31],\n\t[2, 32, 14, 4, 33, 15],\n\t[4, 39, 13, 1, 40, 14],\n\t\n\t// 8\n\t[2, 121, 97],\n\t[2, 60, 38, 2, 61, 39],\n\t[4, 40, 18, 2, 41, 19],\n\t[4, 40, 14, 2, 41, 15],\n\t\n\t// 9\n\t[2, 146, 116],\n\t[3, 58, 36, 2, 59, 37],\n\t[4, 36, 16, 4, 37, 17],\n\t[4, 36, 12, 4, 37, 13],\n\t\n\t// 10\t\t\n\t[2, 86, 68, 2, 87, 69],\n\t[4, 69, 43, 1, 70, 44],\n\t[6, 43, 19, 2, 44, 20],\n\t[6, 43, 15, 2, 44, 16],\n\n\t// 11\n\t[4, 101, 81],\n\t[1, 80, 50, 4, 81, 51],\n\t[4, 50, 22, 4, 51, 23],\n\t[3, 36, 12, 8, 37, 13],\n\n\t// 12\n\t[2, 116, 92, 2, 117, 93],\n\t[6, 58, 36, 2, 59, 37],\n\t[4, 46, 20, 6, 47, 21],\n\t[7, 42, 14, 4, 43, 15],\n\n\t// 13\n\t[4, 133, 107],\n\t[8, 59, 37, 1, 60, 38],\n\t[8, 44, 20, 4, 45, 21],\n\t[12, 33, 11, 4, 34, 12],\n\n\t// 14\n\t[3, 145, 115, 1, 146, 116],\n\t[4, 64, 40, 5, 65, 41],\n\t[11, 36, 16, 5, 37, 17],\n\t[11, 36, 12, 5, 37, 13],\n\n\t// 15\n\t[5, 109, 87, 1, 110, 88],\n\t[5, 65, 41, 5, 66, 42],\n\t[5, 54, 24, 7, 55, 25],\n\t[11, 36, 12],\n\n\t// 16\n\t[5, 122, 98, 1, 123, 99],\n\t[7, 73, 45, 3, 74, 46],\n\t[15, 43, 19, 2, 44, 20],\n\t[3, 45, 15, 13, 46, 16],\n\n\t// 17\n\t[1, 135, 107, 5, 136, 108],\n\t[10, 74, 46, 1, 75, 47],\n\t[1, 50, 22, 15, 51, 23],\n\t[2, 42, 14, 17, 43, 15],\n\n\t// 18\n\t[5, 150, 120, 1, 151, 121],\n\t[9, 69, 43, 4, 70, 44],\n\t[17, 50, 22, 1, 51, 23],\n\t[2, 42, 14, 19, 43, 15],\n\n\t// 19\n\t[3, 141, 113, 4, 142, 114],\n\t[3, 70, 44, 11, 71, 45],\n\t[17, 47, 21, 4, 48, 22],\n\t[9, 39, 13, 16, 40, 14],\n\n\t// 20\n\t[3, 135, 107, 5, 136, 108],\n\t[3, 67, 41, 13, 68, 42],\n\t[15, 54, 24, 5, 55, 25],\n\t[15, 43, 15, 10, 44, 16],\n\n\t// 21\n\t[4, 144, 116, 4, 145, 117],\n\t[17, 68, 42],\n\t[17, 50, 22, 6, 51, 23],\n\t[19, 46, 16, 6, 47, 17],\n\n\t// 22\n\t[2, 139, 111, 7, 140, 112],\n\t[17, 74, 46],\n\t[7, 54, 24, 16, 55, 25],\n\t[34, 37, 13],\n\n\t// 23\n\t[4, 151, 121, 5, 152, 122],\n\t[4, 75, 47, 14, 76, 48],\n\t[11, 54, 24, 14, 55, 25],\n\t[16, 45, 15, 14, 46, 16],\n\n\t// 24\n\t[6, 147, 117, 4, 148, 118],\n\t[6, 73, 45, 14, 74, 46],\n\t[11, 54, 24, 16, 55, 25],\n\t[30, 46, 16, 2, 47, 17],\n\n\t// 25\n\t[8, 132, 106, 4, 133, 107],\n\t[8, 75, 47, 13, 76, 48],\n\t[7, 54, 24, 22, 55, 25],\n\t[22, 45, 15, 13, 46, 16],\n\n\t// 26\n\t[10, 142, 114, 2, 143, 115],\n\t[19, 74, 46, 4, 75, 47],\n\t[28, 50, 22, 6, 51, 23],\n\t[33, 46, 16, 4, 47, 17],\n\n\t// 27\n\t[8, 152, 122, 4, 153, 123],\n\t[22, 73, 45, 3, 74, 46],\n\t[8, 53, 23, 26, 54, 24],\n\t[12, 45, 15, 28, 46, 16],\n\n\t// 28\n\t[3, 147, 117, 10, 148, 118],\n\t[3, 73, 45, 23, 74, 46],\n\t[4, 54, 24, 31, 55, 25],\n\t[11, 45, 15, 31, 46, 16],\n\n\t// 29\n\t[7, 146, 116, 7, 147, 117],\n\t[21, 73, 45, 7, 74, 46],\n\t[1, 53, 23, 37, 54, 24],\n\t[19, 45, 15, 26, 46, 16],\n\n\t// 30\n\t[5, 145, 115, 10, 146, 116],\n\t[19, 75, 47, 10, 76, 48],\n\t[15, 54, 24, 25, 55, 25],\n\t[23, 45, 15, 25, 46, 16],\n\n\t// 31\n\t[13, 145, 115, 3, 146, 116],\n\t[2, 74, 46, 29, 75, 47],\n\t[42, 54, 24, 1, 55, 25],\n\t[23, 45, 15, 28, 46, 16],\n\n\t// 32\n\t[17, 145, 115],\n\t[10, 74, 46, 23, 75, 47],\n\t[10, 54, 24, 35, 55, 25],\n\t[19, 45, 15, 35, 46, 16],\n\n\t// 33\n\t[17, 145, 115, 1, 146, 116],\n\t[14, 74, 46, 21, 75, 47],\n\t[29, 54, 24, 19, 55, 25],\n\t[11, 45, 15, 46, 46, 16],\n\n\t// 34\n\t[13, 145, 115, 6, 146, 116],\n\t[14, 74, 46, 23, 75, 47],\n\t[44, 54, 24, 7, 55, 25],\n\t[59, 46, 16, 1, 47, 17],\n\n\t// 35\n\t[12, 151, 121, 7, 152, 122],\n\t[12, 75, 47, 26, 76, 48],\n\t[39, 54, 24, 14, 55, 25],\n\t[22, 45, 15, 41, 46, 16],\n\n\t// 36\n\t[6, 151, 121, 14, 152, 122],\n\t[6, 75, 47, 34, 76, 48],\n\t[46, 54, 24, 10, 55, 25],\n\t[2, 45, 15, 64, 46, 16],\n\n\t// 37\n\t[17, 152, 122, 4, 153, 123],\n\t[29, 74, 46, 14, 75, 47],\n\t[49, 54, 24, 10, 55, 25],\n\t[24, 45, 15, 46, 46, 16],\n\n\t// 38\n\t[4, 152, 122, 18, 153, 123],\n\t[13, 74, 46, 32, 75, 47],\n\t[48, 54, 24, 14, 55, 25],\n\t[42, 45, 15, 32, 46, 16],\n\n\t// 39\n\t[20, 147, 117, 4, 148, 118],\n\t[40, 75, 47, 7, 76, 48],\n\t[43, 54, 24, 22, 55, 25],\n\t[10, 45, 15, 67, 46, 16],\n\n\t// 40\n\t[19, 148, 118, 6, 149, 119],\n\t[18, 75, 47, 31, 76, 48],\n\t[34, 54, 24, 34, 55, 25],\n\t[20, 45, 15, 61, 46, 16]\n];\n\nQRRSBlock.getRSBlocks = function(typeNumber, errorCorrectLevel) {\n\t\n\tvar rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);\n\t\n\tif (rsBlock == undefined) {\n\t\tthrow new Error(\"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectLevel:\" + errorCorrectLevel);\n\t}\n\n\tvar length = rsBlock.length / 3;\n\t\n\tvar list = new Array();\n\t\n\tfor (var i = 0; i < length; i++) {\n\n\t\tvar count = rsBlock[i * 3 + 0];\n\t\tvar totalCount = rsBlock[i * 3 + 1];\n\t\tvar dataCount  = rsBlock[i * 3 + 2];\n\n\t\tfor (var j = 0; j < count; j++) {\n\t\t\tlist.push(new QRRSBlock(totalCount, dataCount) );\t\n\t\t}\n\t}\n\t\n\treturn list;\n}\n\nQRRSBlock.getRsBlockTable = function(typeNumber, errorCorrectLevel) {\n\n\tswitch(errorCorrectLevel) {\n\tcase ECL.L :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n\tcase ECL.M :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n\tcase ECL.Q :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n\tcase ECL.H :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n\tdefault :\n\t\treturn undefined;\n\t}\n}\n\nmodule.exports = QRRSBlock;\n", "function QRBitBuffer() {\n\tthis.buffer = new Array();\n\tthis.length = 0;\n}\n\nQRBitBuffer.prototype = {\n\n\tget : function(index) {\n\t\tvar bufIndex = Math.floor(index / 8);\n\t\treturn ( (this.buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1;\n\t},\n\t\n\tput : function(num, length) {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tthis.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);\n\t\t}\n\t},\n\t\n\tgetLengthInBits : function() {\n\t\treturn this.length;\n\t},\n\t\n\tputBit : function(bit) {\n\t\n\t\tvar bufIndex = Math.floor(this.length / 8);\n\t\tif (this.buffer.length <= bufIndex) {\n\t\t\tthis.buffer.push(0);\n\t\t}\n\t\n\t\tif (bit) {\n\t\t\tthis.buffer[bufIndex] |= (0x80 >>> (this.length % 8) );\n\t\t}\n\t\n\t\tthis.length++;\n\t}\n};\n\nmodule.exports = QRBitBuffer;\n", "var QRMath = {\n\n\tglog : function(n) {\n\t\n\t\tif (n < 1) {\n\t\t\tthrow new Error(\"glog(\" + n + \")\");\n\t\t}\n\t\t\n\t\treturn QRMath.LOG_TABLE[n];\n\t},\n\t\n\tgexp : function(n) {\n\t\n\t\twhile (n < 0) {\n\t\t\tn += 255;\n\t\t}\n\t\n\t\twhile (n >= 256) {\n\t\t\tn -= 255;\n\t\t}\n\t\n\t\treturn QRMath.EXP_TABLE[n];\n\t},\n\t\n\tEXP_TABLE : new Array(256),\n\t\n\tLOG_TABLE : new Array(256)\n\n};\n\t\nfor (var i = 0; i < 8; i++) {\n\tQRMath.EXP_TABLE[i] = 1 << i;\n}\nfor (var i = 8; i < 256; i++) {\n\tQRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4]\n\t\t^ QRMath.EXP_TABLE[i - 5]\n\t\t^ QRMath.EXP_TABLE[i - 6]\n\t\t^ QRMath.EXP_TABLE[i - 8];\n}\nfor (var i = 0; i < 255; i++) {\n\tQRMath.LOG_TABLE[QRMath.EXP_TABLE[i] ] = i;\n}\n\nmodule.exports = QRMath;\n", "var math = require('./math');\n\nfunction QRPolynomial(num, shift) {\n\n\tif (num.length == undefined) {\n\t\tthrow new Error(num.length + \"/\" + shift);\n\t}\n\n\tvar offset = 0;\n\n\twhile (offset < num.length && num[offset] == 0) {\n\t\toffset++;\n\t}\n\n\tthis.num = new Array(num.length - offset + shift);\n\tfor (var i = 0; i < num.length - offset; i++) {\n\t\tthis.num[i] = num[i + offset];\n\t}\n}\n\nQRPolynomial.prototype = {\n\n\tget : function(index) {\n\t\treturn this.num[index];\n\t},\n\t\n\tgetLength : function() {\n\t\treturn this.num.length;\n\t},\n\t\n\tmultiply : function(e) {\n\t\n\t\tvar num = new Array(this.getLength() + e.getLength() - 1);\n\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tfor (var j = 0; j < e.getLength(); j++) {\n\t\t\t\tnum[i + j] ^= math.gexp(math.glog(this.get(i) ) + math.glog(e.get(j) ) );\n\t\t\t}\n\t\t}\n\t\n\t\treturn new QRPolynomial(num, 0);\n\t},\n\t\n\tmod : function(e) {\n\t\n\t\tif (this.getLength() - e.getLength() < 0) {\n\t\t\treturn this;\n\t\t}\n\t\n\t\tvar ratio = math.glog(this.get(0) ) - math.glog(e.get(0) );\n\t\n\t\tvar num = new Array(this.getLength() );\n\t\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tnum[i] = this.get(i);\n\t\t}\n\t\t\n\t\tfor (var i = 0; i < e.getLength(); i++) {\n\t\t\tnum[i] ^= math.gexp(math.glog(e.get(i) ) + ratio);\n\t\t}\n\t\n\t\t// recursive call\n\t\treturn new QRPolynomial(num, 0).mod(e);\n\t}\n};\n\nmodule.exports = QRPolynomial;\n", "var Mode = require('./mode');\nvar Polynomial = require('./Polynomial');\nvar math = require('./math');\n\nvar QRMaskPattern = {\n\tPATTERN000 : 0,\n\tPATTERN001 : 1,\n\tPATTERN010 : 2,\n\tPATTERN011 : 3,\n\tPATTERN100 : 4,\n\tPATTERN101 : 5,\n\tPATTERN110 : 6,\n\tPATTERN111 : 7\n};\n\nvar QRUtil = {\n\n    PATTERN_POSITION_TABLE : [\n\t    [],\n\t    [6, 18],\n\t    [6, 22],\n\t    [6, 26],\n\t    [6, 30],\n\t    [6, 34],\n\t    [6, 22, 38],\n\t    [6, 24, 42],\n\t    [6, 26, 46],\n\t    [6, 28, 50],\n\t    [6, 30, 54],\t\t\n\t    [6, 32, 58],\n\t    [6, 34, 62],\n\t    [6, 26, 46, 66],\n\t    [6, 26, 48, 70],\n\t    [6, 26, 50, 74],\n\t    [6, 30, 54, 78],\n\t    [6, 30, 56, 82],\n\t    [6, 30, 58, 86],\n\t    [6, 34, 62, 90],\n\t    [6, 28, 50, 72, 94],\n\t    [6, 26, 50, 74, 98],\n\t    [6, 30, 54, 78, 102],\n\t    [6, 28, 54, 80, 106],\n\t    [6, 32, 58, 84, 110],\n\t    [6, 30, 58, 86, 114],\n\t    [6, 34, 62, 90, 118],\n\t    [6, 26, 50, 74, 98, 122],\n\t    [6, 30, 54, 78, 102, 126],\n\t    [6, 26, 52, 78, 104, 130],\n\t    [6, 30, 56, 82, 108, 134],\n\t    [6, 34, 60, 86, 112, 138],\n\t    [6, 30, 58, 86, 114, 142],\n\t    [6, 34, 62, 90, 118, 146],\n\t    [6, 30, 54, 78, 102, 126, 150],\n\t    [6, 24, 50, 76, 102, 128, 154],\n\t    [6, 28, 54, 80, 106, 132, 158],\n\t    [6, 32, 58, 84, 110, 136, 162],\n\t    [6, 26, 54, 82, 110, 138, 166],\n\t    [6, 30, 58, 86, 114, 142, 170]\n    ],\n\n    G15 : (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),\n    G18 : (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),\n    G15_MASK : (1 << 14) | (1 << 12) | (1 << 10)\t| (1 << 4) | (1 << 1),\n\n    getBCHTypeInfo : function(data) {\n\t    var d = data << 10;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\n\t\t    d ^= (QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) ) ); \t\n\t    }\n\t    return ( (data << 10) | d) ^ QRUtil.G15_MASK;\n    },\n\n    getBCHTypeNumber : function(data) {\n\t    var d = data << 12;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\n\t\t    d ^= (QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) ) ); \t\n\t    }\n\t    return (data << 12) | d;\n    },\n\n    getBCHDigit : function(data) {\n\n\t    var digit = 0;\n\n\t    while (data != 0) {\n\t\t    digit++;\n\t\t    data >>>= 1;\n\t    }\n\n\t    return digit;\n    },\n\n    getPatternPosition : function(typeNumber) {\n\t    return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];\n    },\n\n    getMask : function(maskPattern, i, j) {\n\t    \n\t    switch (maskPattern) {\n\t\t    \n\t    case QRMaskPattern.PATTERN000 : return (i + j) % 2 == 0;\n\t    case QRMaskPattern.PATTERN001 : return i % 2 == 0;\n\t    case QRMaskPattern.PATTERN010 : return j % 3 == 0;\n\t    case QRMaskPattern.PATTERN011 : return (i + j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN100 : return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0;\n\t    case QRMaskPattern.PATTERN101 : return (i * j) % 2 + (i * j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN110 : return ( (i * j) % 2 + (i * j) % 3) % 2 == 0;\n\t    case QRMaskPattern.PATTERN111 : return ( (i * j) % 3 + (i + j) % 2) % 2 == 0;\n\n\t    default :\n\t\t    throw new Error(\"bad maskPattern:\" + maskPattern);\n\t    }\n    },\n\n    getErrorCorrectPolynomial : function(errorCorrectLength) {\n\n\t    var a = new Polynomial([1], 0);\n\n\t    for (var i = 0; i < errorCorrectLength; i++) {\n\t\t    a = a.multiply(new Polynomial([1, math.gexp(i)], 0) );\n\t    }\n\n\t    return a;\n    },\n\n    getLengthInBits : function(mode, type) {\n\n\t    if (1 <= type && type < 10) {\n\n\t\t    // 1 - 9\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 10;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 9;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 8;\n\t\t    case Mode.MODE_KANJI  \t: return 8;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 27) {\n\n\t\t    // 10 - 26\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 12;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 11;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 10;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 41) {\n\n\t\t    // 27 - 40\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 14;\n\t\t    case Mode.MODE_ALPHA_NUM\t: return 13;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 12;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else {\n\t\t    throw new Error(\"type:\" + type);\n\t    }\n    },\n\n    getLostPoint : function(qrCode) {\n\t    \n\t    var moduleCount = qrCode.getModuleCount();\n\t    \n\t    var lostPoint = 0;\n\t    \n\t    // LEVEL1\n\t    \n\t    for (var row = 0; row < moduleCount; row++) {\n\n\t\t    for (var col = 0; col < moduleCount; col++) {\n\n\t\t\t    var sameCount = 0;\n\t\t\t    var dark = qrCode.isDark(row, col);\n\n\t\t\t\tfor (var r = -1; r <= 1; r++) {\n\n\t\t\t\t    if (row + r < 0 || moduleCount <= row + r) {\n\t\t\t\t\t    continue;\n\t\t\t\t    }\n\n\t\t\t\t    for (var c = -1; c <= 1; c++) {\n\n\t\t\t\t\t    if (col + c < 0 || moduleCount <= col + c) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (r == 0 && c == 0) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (dark == qrCode.isDark(row + r, col + c) ) {\n\t\t\t\t\t\t    sameCount++;\n\t\t\t\t\t    }\n\t\t\t\t    }\n\t\t\t    }\n\n\t\t\t    if (sameCount > 5) {\n\t\t\t\t    lostPoint += (3 + sameCount - 5);\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL2\n\n\t    for (var row = 0; row < moduleCount - 1; row++) {\n\t\t    for (var col = 0; col < moduleCount - 1; col++) {\n\t\t\t    var count = 0;\n\t\t\t    if (qrCode.isDark(row,     col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row,     col + 1) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col + 1) ) count++;\n\t\t\t    if (count == 0 || count == 4) {\n\t\t\t\t    lostPoint += 3;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL3\n\n\t    for (var row = 0; row < moduleCount; row++) {\n\t\t    for (var col = 0; col < moduleCount - 6; col++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 1)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 2)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 3)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 4)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 5)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 6) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount - 6; row++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 1, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 2, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 3, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 4, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 5, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 6, col) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL4\n\t    \n\t    var darkCount = 0;\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount; row++) {\n\t\t\t    if (qrCode.isDark(row, col) ) {\n\t\t\t\t    darkCount++;\n\t\t\t    }\n\t\t    }\n\t    }\n\t    \n\t    var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n\t    lostPoint += ratio * 10;\n\n\t    return lostPoint;\t\t\n    }\n};\n\nmodule.exports = QRUtil;\n", "var BitByte = require('./8BitByte');\nvar RSBlock = require('./RSBlock');\nvar BitBuffer = require('./BitBuffer');\nvar util = require('./util');\nvar Polynomial = require('./Polynomial');\n\nfunction QRCode(typeNumber, errorCorrectLevel) {\n\tthis.typeNumber = typeNumber;\n\tthis.errorCorrectLevel = errorCorrectLevel;\n\tthis.modules = null;\n\tthis.moduleCount = 0;\n\tthis.dataCache = null;\n\tthis.dataList = [];\n}\n\n// for client side minification\nvar proto = QRCode.prototype;\n\nproto.addData = function(data) {\n\tvar newData = new BitByte(data);\n\tthis.dataList.push(newData);\n\tthis.dataCache = null;\n};\n\nproto.isDark = function(row, col) {\n\tif (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\n\t\tthrow new Error(row + \",\" + col);\n\t}\n\treturn this.modules[row][col];\n};\n\nproto.getModuleCount = function() {\n\treturn this.moduleCount;\n};\n\nproto.make = function() {\n\t// Calculate automatically typeNumber if provided is < 1\n\tif (this.typeNumber < 1 ){\n\t\tvar typeNumber = 1;\n\t\tfor (typeNumber = 1; typeNumber < 40; typeNumber++) {\n\t\t\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, this.errorCorrectLevel);\n\n\t\t\tvar buffer = new BitBuffer();\n\t\t\tvar totalDataCount = 0;\n\t\t\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\t\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < this.dataList.length; i++) {\n\t\t\t\tvar data = this.dataList[i];\n\t\t\t\tbuffer.put(data.mode, 4);\n\t\t\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\t\t\tdata.write(buffer);\n\t\t\t}\n\t\t\tif (buffer.getLengthInBits() <= totalDataCount * 8)\n\t\t\t\tbreak;\n\t\t}\n\t\tthis.typeNumber = typeNumber;\n\t}\n\tthis.makeImpl(false, this.getBestMaskPattern() );\n};\n\nproto.makeImpl = function(test, maskPattern) {\n\t\n\tthis.moduleCount = this.typeNumber * 4 + 17;\n\tthis.modules = new Array(this.moduleCount);\n\t\n\tfor (var row = 0; row < this.moduleCount; row++) {\n\t\t\n\t\tthis.modules[row] = new Array(this.moduleCount);\n\t\t\n\t\tfor (var col = 0; col < this.moduleCount; col++) {\n\t\t\tthis.modules[row][col] = null;//(col + row) % 3;\n\t\t}\n\t}\n\n\tthis.setupPositionProbePattern(0, 0);\n\tthis.setupPositionProbePattern(this.moduleCount - 7, 0);\n\tthis.setupPositionProbePattern(0, this.moduleCount - 7);\n\tthis.setupPositionAdjustPattern();\n\tthis.setupTimingPattern();\n\tthis.setupTypeInfo(test, maskPattern);\n\t\n\tif (this.typeNumber >= 7) {\n\t\tthis.setupTypeNumber(test);\n\t}\n\n\tif (this.dataCache == null) {\n\t\tthis.dataCache = QRCode.createData(this.typeNumber, this.errorCorrectLevel, this.dataList);\n\t}\n\n\tthis.mapData(this.dataCache, maskPattern);\n};\n\nproto.setupPositionProbePattern = function(row, col)  {\n\t\n\tfor (var r = -1; r <= 7; r++) {\n\t\t\n\t\tif (row + r <= -1 || this.moduleCount <= row + r) continue;\n\t\t\n\t\tfor (var c = -1; c <= 7; c++) {\n\t\t\t\n\t\t\tif (col + c <= -1 || this.moduleCount <= col + c) continue;\n\t\t\t\n\t\t\tif ( (0 <= r && r <= 6 && (c == 0 || c == 6) )\n\t\t\t\t\t|| (0 <= c && c <= 6 && (r == 0 || r == 6) )\n\t\t\t\t\t|| (2 <= r && r <= 4 && 2 <= c && c <= 4) ) {\n\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t} else {\n\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t}\n\t\t}\t\t\n\t}\t\t\n};\n\nproto.getBestMaskPattern = function() {\n\n\tvar minLostPoint = 0;\n\tvar pattern = 0;\n\n\tfor (var i = 0; i < 8; i++) {\n\t\t\n\t\tthis.makeImpl(true, i);\n\n\t\tvar lostPoint = util.getLostPoint(this);\n\n\t\tif (i == 0 || minLostPoint >  lostPoint) {\n\t\t\tminLostPoint = lostPoint;\n\t\t\tpattern = i;\n\t\t}\n\t}\n\n\treturn pattern;\n};\n\nproto.createMovieClip = function(target_mc, instance_name, depth) {\n\n\tvar qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);\n\tvar cs = 1;\n\n\tthis.make();\n\n\tfor (var row = 0; row < this.modules.length; row++) {\n\t\t\n\t\tvar y = row * cs;\n\t\t\n\t\tfor (var col = 0; col < this.modules[row].length; col++) {\n\n\t\t\tvar x = col * cs;\n\t\t\tvar dark = this.modules[row][col];\n\t\t\n\t\t\tif (dark) {\n\t\t\t\tqr_mc.beginFill(0, 100);\n\t\t\t\tqr_mc.moveTo(x, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y + cs);\n\t\t\t\tqr_mc.lineTo(x, y + cs);\n\t\t\t\tqr_mc.endFill();\n\t\t\t}\n\t\t}\n\t}\n\t\n\treturn qr_mc;\n};\n\nproto.setupTimingPattern = function() {\n\t\n\tfor (var r = 8; r < this.moduleCount - 8; r++) {\n\t\tif (this.modules[r][6] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[r][6] = (r % 2 == 0);\n\t}\n\n\tfor (var c = 8; c < this.moduleCount - 8; c++) {\n\t\tif (this.modules[6][c] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[6][c] = (c % 2 == 0);\n\t}\n};\n\nproto.setupPositionAdjustPattern = function() {\n\n\tvar pos = util.getPatternPosition(this.typeNumber);\n\t\n\tfor (var i = 0; i < pos.length; i++) {\n\t\n\t\tfor (var j = 0; j < pos.length; j++) {\n\t\t\n\t\t\tvar row = pos[i];\n\t\t\tvar col = pos[j];\n\t\t\t\n\t\t\tif (this.modules[row][col] != null) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t\n\t\t\tfor (var r = -2; r <= 2; r++) {\n\t\t\t\n\t\t\t\tfor (var c = -2; c <= 2; c++) {\n\t\t\t\t\n\t\t\t\t\tif (r == -2 || r == 2 || c == -2 || c == 2\n\t\t\t\t\t\t\t|| (r == 0 && c == 0) ) {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\nproto.setupTypeNumber = function(test) {\n\n\tvar bits = util.getBCHTypeNumber(this.typeNumber);\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;\n\t}\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n\t}\n};\n\nproto.setupTypeInfo = function(test, maskPattern) {\n\n\tvar data = (this.errorCorrectLevel << 3) | maskPattern;\n\tvar bits = util.getBCHTypeInfo(data);\n\n\t// vertical\t\t\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\n\t\tif (i < 6) {\n\t\t\tthis.modules[i][8] = mod;\n\t\t} else if (i < 8) {\n\t\t\tthis.modules[i + 1][8] = mod;\n\t\t} else {\n\t\t\tthis.modules[this.moduleCount - 15 + i][8] = mod;\n\t\t}\n\t}\n\n\t// horizontal\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\t\n\t\tif (i < 8) {\n\t\t\tthis.modules[8][this.moduleCount - i - 1] = mod;\n\t\t} else if (i < 9) {\n\t\t\tthis.modules[8][15 - i - 1 + 1] = mod;\n\t\t} else {\n\t\t\tthis.modules[8][15 - i - 1] = mod;\n\t\t}\n\t}\n\n\t// fixed module\n\tthis.modules[this.moduleCount - 8][8] = (!test);\n};\n\nproto.mapData = function(data, maskPattern) {\n\t\n\tvar inc = -1;\n\tvar row = this.moduleCount - 1;\n\tvar bitIndex = 7;\n\tvar byteIndex = 0;\n\t\n\tfor (var col = this.moduleCount - 1; col > 0; col -= 2) {\n\n\t\tif (col == 6) col--;\n\n\t\twhile (true) {\n\n\t\t\tfor (var c = 0; c < 2; c++) {\n\t\t\t\t\n\t\t\t\tif (this.modules[row][col - c] == null) {\n\t\t\t\t\t\n\t\t\t\t\tvar dark = false;\n\n\t\t\t\t\tif (byteIndex < data.length) {\n\t\t\t\t\t\tdark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);\n\t\t\t\t\t}\n\n\t\t\t\t\tvar mask = util.getMask(maskPattern, row, col - c);\n\n\t\t\t\t\tif (mask) {\n\t\t\t\t\t\tdark = !dark;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.modules[row][col - c] = dark;\n\t\t\t\t\tbitIndex--;\n\n\t\t\t\t\tif (bitIndex == -1) {\n\t\t\t\t\t\tbyteIndex++;\n\t\t\t\t\t\tbitIndex = 7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\trow += inc;\n\n\t\t\tif (row < 0 || this.moduleCount <= row) {\n\t\t\t\trow -= inc;\n\t\t\t\tinc = -inc;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n};\n\nQRCode.PAD0 = 0xEC;\nQRCode.PAD1 = 0x11;\n\nQRCode.createData = function(typeNumber, errorCorrectLevel, dataList) {\n\t\n\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, errorCorrectLevel);\n\t\n\tvar buffer = new BitBuffer();\n\t\n\tfor (var i = 0; i < dataList.length; i++) {\n\t\tvar data = dataList[i];\n\t\tbuffer.put(data.mode, 4);\n\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\tdata.write(buffer);\n\t}\n\n\t// calc num max data.\n\tvar totalDataCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t}\n\n\tif (buffer.getLengthInBits() > totalDataCount * 8) {\n\t\tthrow new Error(\"code length overflow. (\"\n\t\t\t+ buffer.getLengthInBits()\n\t\t\t+ \">\"\n\t\t\t+  totalDataCount * 8\n\t\t\t+ \")\");\n\t}\n\n\t// end code\n\tif (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n\t\tbuffer.put(0, 4);\n\t}\n\n\t// padding\n\twhile (buffer.getLengthInBits() % 8 != 0) {\n\t\tbuffer.putBit(false);\n\t}\n\n\t// padding\n\twhile (true) {\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD0, 8);\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD1, 8);\n\t}\n\n\treturn QRCode.createBytes(buffer, rsBlocks);\n};\n\nQRCode.createBytes = function(buffer, rsBlocks) {\n\n\tvar offset = 0;\n\t\n\tvar maxDcCount = 0;\n\tvar maxEcCount = 0;\n\t\n\tvar dcdata = new Array(rsBlocks.length);\n\tvar ecdata = new Array(rsBlocks.length);\n\t\n\tfor (var r = 0; r < rsBlocks.length; r++) {\n\n\t\tvar dcCount = rsBlocks[r].dataCount;\n\t\tvar ecCount = rsBlocks[r].totalCount - dcCount;\n\n\t\tmaxDcCount = Math.max(maxDcCount, dcCount);\n\t\tmaxEcCount = Math.max(maxEcCount, ecCount);\n\t\t\n\t\tdcdata[r] = new Array(dcCount);\n\t\t\n\t\tfor (var i = 0; i < dcdata[r].length; i++) {\n\t\t\tdcdata[r][i] = 0xff & buffer.buffer[i + offset];\n\t\t}\n\t\toffset += dcCount;\n\t\t\n\t\tvar rsPoly = util.getErrorCorrectPolynomial(ecCount);\n\t\tvar rawPoly = new Polynomial(dcdata[r], rsPoly.getLength() - 1);\n\n\t\tvar modPoly = rawPoly.mod(rsPoly);\n\t\tecdata[r] = new Array(rsPoly.getLength() - 1);\n\t\tfor (var i = 0; i < ecdata[r].length; i++) {\n            var modIndex = i + modPoly.getLength() - ecdata[r].length;\n\t\t\tecdata[r][i] = (modIndex >= 0)? modPoly.get(modIndex) : 0;\n\t\t}\n\n\t}\n\t\n\tvar totalCodeCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalCodeCount += rsBlocks[i].totalCount;\n\t}\n\n\tvar data = new Array(totalCodeCount);\n\tvar index = 0;\n\n\tfor (var i = 0; i < maxDcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < dcdata[r].length) {\n\t\t\t\tdata[index++] = dcdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (var i = 0; i < maxEcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < ecdata[r].length) {\n\t\t\t\tdata[index++] = ecdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn data;\n};\n\nmodule.exports = QRCode;\n\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = require(\"prop-types\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _react = require(\"react\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  bgD: _propTypes2.default.string.isRequired,\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  fgD: _propTypes2.default.string.isRequired,\n  size: _propTypes2.default.number.isRequired,\n  title: _propTypes2.default.string,\n  viewBoxSize: _propTypes2.default.number.isRequired,\n  xmlns: _propTypes2.default.string\n};\n\nvar QRCodeSvg = (0, _react.forwardRef)(function (_ref, ref) {\n  var bgColor = _ref.bgColor,\n      bgD = _ref.bgD,\n      fgD = _ref.fgD,\n      fgColor = _ref.fgColor,\n      size = _ref.size,\n      title = _ref.title,\n      viewBoxSize = _ref.viewBoxSize,\n      _ref$xmlns = _ref.xmlns,\n      xmlns = _ref$xmlns === undefined ? \"http://www.w3.org/2000/svg\" : _ref$xmlns,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"bgD\", \"fgD\", \"fgColor\", \"size\", \"title\", \"viewBoxSize\", \"xmlns\"]);\n\n  return _react2.default.createElement(\n    \"svg\",\n    _extends({}, props, { height: size, ref: ref, viewBox: \"0 0 \" + viewBoxSize + \" \" + viewBoxSize, width: size, xmlns: xmlns }),\n    title ? _react2.default.createElement(\n      \"title\",\n      null,\n      title\n    ) : null,\n    _react2.default.createElement(\"path\", { d: bgD, fill: bgColor }),\n    _react2.default.createElement(\"path\", { d: fgD, fill: fgColor })\n  );\n});\n\nQRCodeSvg.displayName = \"QRCodeSvg\";\nQRCodeSvg.propTypes = propTypes;\n\nexports.default = QRCodeSvg;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.QRCode = undefined;\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = require(\"prop-types\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _ErrorCorrectLevel = require(\"qr.js/lib/ErrorCorrectLevel\");\n\nvar _ErrorCorrectLevel2 = _interopRequireDefault(_ErrorCorrectLevel);\n\nvar _QRCode = require(\"qr.js/lib/QRCode\");\n\nvar _QRCode2 = _interopRequireDefault(_QRCode);\n\nvar _react = require(\"react\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _QRCodeSvg = require(\"./QRCodeSvg\");\n\nvar _QRCodeSvg2 = _interopRequireDefault(_QRCodeSvg);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n// A `qr.js` doesn't handle error level of zero (M) so we need to do it right, thus the deep require.\n\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  level: _propTypes2.default.string,\n  size: _propTypes2.default.number,\n  value: _propTypes2.default.string.isRequired\n};\n\nvar QRCode = (0, _react.forwardRef)(function (_ref, ref) {\n  var _ref$bgColor = _ref.bgColor,\n      bgColor = _ref$bgColor === undefined ? \"#FFFFFF\" : _ref$bgColor,\n      _ref$fgColor = _ref.fgColor,\n      fgColor = _ref$fgColor === undefined ? \"#000000\" : _ref$fgColor,\n      _ref$level = _ref.level,\n      level = _ref$level === undefined ? \"L\" : _ref$level,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 256 : _ref$size,\n      value = _ref.value,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"fgColor\", \"level\", \"size\", \"value\"]);\n\n  // Use type === -1 to automatically pick the best type.\n  var qrcode = new _QRCode2.default(-1, _ErrorCorrectLevel2.default[level]);\n  qrcode.addData(value);\n  qrcode.make();\n  var cells = qrcode.modules;\n  return _react2.default.createElement(_QRCodeSvg2.default, _extends({}, props, {\n    bgColor: bgColor,\n    bgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return !cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    fgColor: fgColor,\n    fgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    ref: ref,\n    size: size,\n    viewBoxSize: cells.length\n  }));\n});\n\nexports.QRCode = QRCode;\nQRCode.displayName = \"QRCode\";\nQRCode.propTypes = propTypes;\n\nexports.default = QRCode;"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAAA,MAChB,GAAI;AAAA,MACJ,GAAI;AAAA,MACJ,GAAI;AAAA,MACJ,GAAI;AAAA,IACL;AAAA;AAAA;;;ACLA;AAAA;AAAA,WAAO,UAAU;AAAA,MAChB,aAAe,KAAK;AAAA,MACpB,gBAAkB,KAAK;AAAA,MACvB,gBAAkB,KAAK;AAAA,MACvB,YAAc,KAAK;AAAA,IACpB;AAAA;AAAA;;;ACLA;AAAA;AAAA,QAAI,OAAO;AAEX,aAAS,WAAW,MAAM;AACzB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACb;AAEA,eAAW,YAAY;AAAA,MAEtB,WAAY,SAAS,QAAQ;AAC5B,eAAO,KAAK,KAAK;AAAA,MAClB;AAAA,MAEA,OAAQ,SAAS,QAAQ;AACxB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAE1C,iBAAO,IAAI,KAAK,KAAK,WAAW,CAAC,GAAG,CAAC;AAAA,QACtC;AAAA,MACD;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AACA,QAAI,MAAM;AAEV,aAAS,UAAU,YAAY,WAAW;AACzC,WAAK,aAAa;AAClB,WAAK,YAAa;AAAA,IACnB;AAEA,cAAU,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQ1B,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,MAGT,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,MAGT,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,MAGX,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,EAAE;AAAA,MACX,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,MAGX,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,GAAG;AAAA,MACb,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACxB;AAEA,cAAU,cAAc,SAAS,YAAY,mBAAmB;AAE/D,UAAI,UAAU,UAAU,gBAAgB,YAAY,iBAAiB;AAErE,UAAI,WAAW,QAAW;AACzB,cAAM,IAAI,MAAM,+BAA+B,aAAa,wBAAwB,iBAAiB;AAAA,MACtG;AAEA,UAAI,SAAS,QAAQ,SAAS;AAE9B,UAAI,OAAO,IAAI,MAAM;AAErB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAEhC,YAAI,QAAQ,QAAQ,IAAI,IAAI,CAAC;AAC7B,YAAI,aAAa,QAAQ,IAAI,IAAI,CAAC;AAClC,YAAI,YAAa,QAAQ,IAAI,IAAI,CAAC;AAElC,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC/B,eAAK,KAAK,IAAI,UAAU,YAAY,SAAS,CAAE;AAAA,QAChD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,cAAU,kBAAkB,SAAS,YAAY,mBAAmB;AAEnE,cAAO,mBAAmB;AAAA,QAC1B,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD;AACC,iBAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1SjB;AAAA;AAAA,aAAS,cAAc;AACtB,WAAK,SAAS,IAAI,MAAM;AACxB,WAAK,SAAS;AAAA,IACf;AAEA,gBAAY,YAAY;AAAA,MAEvB,KAAM,SAAS,OAAO;AACrB,YAAI,WAAW,KAAK,MAAM,QAAQ,CAAC;AACnC,gBAAU,KAAK,OAAO,QAAQ,MAAO,IAAI,QAAQ,IAAO,MAAM;AAAA,MAC/D;AAAA,MAEA,KAAM,SAAS,KAAK,QAAQ;AAC3B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,eAAK,QAAW,QAAS,SAAS,IAAI,IAAO,MAAM,CAAC;AAAA,QACrD;AAAA,MACD;AAAA,MAEA,iBAAkB,WAAW;AAC5B,eAAO,KAAK;AAAA,MACb;AAAA,MAEA,QAAS,SAAS,KAAK;AAEtB,YAAI,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AACzC,YAAI,KAAK,OAAO,UAAU,UAAU;AACnC,eAAK,OAAO,KAAK,CAAC;AAAA,QACnB;AAEA,YAAI,KAAK;AACR,eAAK,OAAO,QAAQ,KAAM,QAAU,KAAK,SAAS;AAAA,QACnD;AAEA,aAAK;AAAA,MACN;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA,QAAI,SAAS;AAAA,MAEZ,MAAO,SAAS,GAAG;AAElB,YAAI,IAAI,GAAG;AACV,gBAAM,IAAI,MAAM,UAAU,IAAI,GAAG;AAAA,QAClC;AAEA,eAAO,OAAO,UAAU,CAAC;AAAA,MAC1B;AAAA,MAEA,MAAO,SAAS,GAAG;AAElB,eAAO,IAAI,GAAG;AACb,eAAK;AAAA,QACN;AAEA,eAAO,KAAK,KAAK;AAChB,eAAK;AAAA,QACN;AAEA,eAAO,OAAO,UAAU,CAAC;AAAA,MAC1B;AAAA,MAEA,WAAY,IAAI,MAAM,GAAG;AAAA,MAEzB,WAAY,IAAI,MAAM,GAAG;AAAA,IAE1B;AAEA,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,aAAO,UAAU,CAAC,IAAI,KAAK;AAAA,IAC5B;AAFS;AAGT,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,aAAO,UAAU,CAAC,IAAI,OAAO,UAAU,IAAI,CAAC,IACzC,OAAO,UAAU,IAAI,CAAC,IACtB,OAAO,UAAU,IAAI,CAAC,IACtB,OAAO,UAAU,IAAI,CAAC;AAAA,IAC1B;AALS;AAMT,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,aAAO,UAAU,OAAO,UAAU,CAAC,CAAE,IAAI;AAAA,IAC1C;AAFS;AAIT,WAAO,UAAU;AAAA;AAAA;;;AC3CjB;AAAA;AAAA,QAAI,OAAO;AAEX,aAAS,aAAa,KAAK,OAAO;AAEjC,UAAI,IAAI,UAAU,QAAW;AAC5B,cAAM,IAAI,MAAM,IAAI,SAAS,MAAM,KAAK;AAAA,MACzC;AAEA,UAAI,SAAS;AAEb,aAAO,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,GAAG;AAC/C;AAAA,MACD;AAEA,WAAK,MAAM,IAAI,MAAM,IAAI,SAAS,SAAS,KAAK;AAChD,eAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC7C,aAAK,IAAI,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MAC7B;AAAA,IACD;AAEA,iBAAa,YAAY;AAAA,MAExB,KAAM,SAAS,OAAO;AACrB,eAAO,KAAK,IAAI,KAAK;AAAA,MACtB;AAAA,MAEA,WAAY,WAAW;AACtB,eAAO,KAAK,IAAI;AAAA,MACjB;AAAA,MAEA,UAAW,SAAS,GAAG;AAEtB,YAAI,MAAM,IAAI,MAAM,KAAK,UAAU,IAAI,EAAE,UAAU,IAAI,CAAC;AAExD,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK;AAC1C,mBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK;AACvC,gBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,CAAE,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE,CAAE;AAAA,UACxE;AAAA,QACD;AAEA,eAAO,IAAI,aAAa,KAAK,CAAC;AAAA,MAC/B;AAAA,MAEA,KAAM,SAAS,GAAG;AAEjB,YAAI,KAAK,UAAU,IAAI,EAAE,UAAU,IAAI,GAAG;AACzC,iBAAO;AAAA,QACR;AAEA,YAAI,QAAQ,KAAK,KAAK,KAAK,IAAI,CAAC,CAAE,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE;AAEzD,YAAI,MAAM,IAAI,MAAM,KAAK,UAAU,CAAE;AAErC,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK;AAC1C,cAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QACpB;AAEA,iBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK;AACvC,cAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE,IAAI,KAAK;AAAA,QACjD;AAGA,eAAO,IAAI,aAAa,KAAK,CAAC,EAAE,IAAI,CAAC;AAAA,MACtC;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClEjB;AAAA;AAAA,QAAI,OAAO;AACX,QAAI,aAAa;AACjB,QAAI,OAAO;AAEX,QAAI,gBAAgB;AAAA,MACnB,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,IACd;AAEA,QAAI,SAAS;AAAA,MAET,wBAAyB;AAAA,QACxB,CAAC;AAAA,QACD,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAClB,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAClB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,QACvB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,MAC9B;AAAA,MAEA,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAAA,MAC/E,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAAA,MAC5F,UAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AAAA,MAEhE,gBAAiB,SAAS,MAAM;AAC/B,YAAI,IAAI,QAAQ;AAChB,eAAO,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AACnE,eAAM,OAAO,OAAQ,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG;AAAA,QAC3E;AACA,gBAAU,QAAQ,KAAM,KAAK,OAAO;AAAA,MACrC;AAAA,MAEA,kBAAmB,SAAS,MAAM;AACjC,YAAI,IAAI,QAAQ;AAChB,eAAO,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AACnE,eAAM,OAAO,OAAQ,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG;AAAA,QAC3E;AACA,eAAQ,QAAQ,KAAM;AAAA,MACvB;AAAA,MAEA,aAAc,SAAS,MAAM;AAE5B,YAAI,QAAQ;AAEZ,eAAO,QAAQ,GAAG;AACjB;AACA,oBAAU;AAAA,QACX;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,oBAAqB,SAAS,YAAY;AACzC,eAAO,OAAO,uBAAuB,aAAa,CAAC;AAAA,MACpD;AAAA,MAEA,SAAU,SAAS,aAAa,GAAG,GAAG;AAErC,gBAAQ,aAAa;AAAA,UAErB,KAAK,cAAc;AAAa,oBAAQ,IAAI,KAAK,KAAK;AAAA,UACtD,KAAK,cAAc;AAAa,mBAAO,IAAI,KAAK;AAAA,UAChD,KAAK,cAAc;AAAa,mBAAO,IAAI,KAAK;AAAA,UAChD,KAAK,cAAc;AAAa,oBAAQ,IAAI,KAAK,KAAK;AAAA,UACtD,KAAK,cAAc;AAAa,oBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAM,KAAK;AAAA,UACvF,KAAK,cAAc;AAAa,mBAAQ,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK;AAAA,UACpE,KAAK,cAAc;AAAa,oBAAU,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK,KAAK;AAAA,UAC3E,KAAK,cAAc;AAAa,oBAAU,IAAI,IAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,UAE3E;AACC,kBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,QACjD;AAAA,MACD;AAAA,MAEA,2BAA4B,SAAS,oBAAoB;AAExD,YAAI,IAAI,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC;AAE7B,iBAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC5C,cAAI,EAAE,SAAS,IAAI,WAAW,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAE;AAAA,QACrD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAkB,SAAS,MAAM,MAAM;AAEtC,YAAI,KAAK,QAAQ,OAAO,IAAI;AAI3B,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAkB,qBAAO;AAAA,YACnC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,WAAW,OAAO,IAAI;AAIrB,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAkB,qBAAO;AAAA,YACnC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,WAAW,OAAO,IAAI;AAIrB,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,OAAO;AACN,gBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,QAC/B;AAAA,MACD;AAAA,MAEA,cAAe,SAAS,QAAQ;AAE/B,YAAI,cAAc,OAAO,eAAe;AAExC,YAAI,YAAY;AAIhB,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAE3C,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAE3C,gBAAI,YAAY;AAChB,gBAAI,OAAO,OAAO,OAAO,KAAK,GAAG;AAEpC,qBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE1B,kBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AAC1C;AAAA,cACD;AAEA,uBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,oBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AAC1C;AAAA,gBACD;AAEA,oBAAI,KAAK,KAAK,KAAK,GAAG;AACrB;AAAA,gBACD;AAEA,oBAAI,QAAQ,OAAO,OAAO,MAAM,GAAG,MAAM,CAAC,GAAI;AAC7C;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG;AAClB,2BAAc,IAAI,YAAY;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAIA,iBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,QAAQ;AACZ,gBAAI,OAAO,OAAO,KAAS,GAAO,EAAI;AACtC,gBAAI,OAAO,OAAO,MAAM,GAAG,GAAO,EAAI;AACtC,gBAAI,OAAO,OAAO,KAAS,MAAM,CAAC,EAAI;AACtC,gBAAI,OAAO,OAAO,MAAM,GAAG,MAAM,CAAC,EAAI;AACtC,gBAAI,SAAS,KAAK,SAAS,GAAG;AAC7B,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAIA,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,OAAO,OAAO,KAAK,GAAG,KACrB,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC3B,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,GAAI;AACnC,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAEA,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,OAAO,OAAO,KAAK,GAAG,KACrB,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC3B,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,GAAI;AACnC,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAIA,YAAI,YAAY;AAEhB,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,gBAAI,OAAO,OAAO,KAAK,GAAG,GAAI;AAC7B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,QAAQ,KAAK,IAAI,MAAM,YAAY,cAAc,cAAc,EAAE,IAAI;AACzE,qBAAa,QAAQ;AAErB,eAAO;AAAA,MACR;AAAA,IACJ;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtRjB;AAAA;AAAA,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,aAAa;AAEjB,aAAS,OAAO,YAAY,mBAAmB;AAC9C,WAAK,aAAa;AAClB,WAAK,oBAAoB;AACzB,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,WAAW,CAAC;AAAA,IAClB;AAGA,QAAI,QAAQ,OAAO;AAEnB,UAAM,UAAU,SAAS,MAAM;AAC9B,UAAI,UAAU,IAAI,QAAQ,IAAI;AAC9B,WAAK,SAAS,KAAK,OAAO;AAC1B,WAAK,YAAY;AAAA,IAClB;AAEA,UAAM,SAAS,SAAS,KAAK,KAAK;AACjC,UAAI,MAAM,KAAK,KAAK,eAAe,OAAO,MAAM,KAAK,KAAK,eAAe,KAAK;AAC7E,cAAM,IAAI,MAAM,MAAM,MAAM,GAAG;AAAA,MAChC;AACA,aAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;AAAA,IAC7B;AAEA,UAAM,iBAAiB,WAAW;AACjC,aAAO,KAAK;AAAA,IACb;AAEA,UAAM,OAAO,WAAW;AAEvB,UAAI,KAAK,aAAa,GAAG;AACxB,YAAI,aAAa;AACjB,aAAK,aAAa,GAAG,aAAa,IAAI,cAAc;AACnD,cAAI,WAAW,QAAQ,YAAY,YAAY,KAAK,iBAAiB;AAErE,cAAI,SAAS,IAAI,UAAU;AAC3B,cAAI,iBAAiB;AACrB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,8BAAkB,SAAS,CAAC,EAAE;AAAA,UAC/B;AAEA,mBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC9C,gBAAI,OAAO,KAAK,SAAS,CAAC;AAC1B,mBAAO,IAAI,KAAK,MAAM,CAAC;AACvB,mBAAO,IAAI,KAAK,UAAU,GAAG,KAAK,gBAAgB,KAAK,MAAM,UAAU,CAAE;AACzE,iBAAK,MAAM,MAAM;AAAA,UAClB;AACA,cAAI,OAAO,gBAAgB,KAAK,iBAAiB;AAChD;AAAA,QACF;AACA,aAAK,aAAa;AAAA,MACnB;AACA,WAAK,SAAS,OAAO,KAAK,mBAAmB,CAAE;AAAA,IAChD;AAEA,UAAM,WAAW,SAAS,MAAM,aAAa;AAE5C,WAAK,cAAc,KAAK,aAAa,IAAI;AACzC,WAAK,UAAU,IAAI,MAAM,KAAK,WAAW;AAEzC,eAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAEhD,aAAK,QAAQ,GAAG,IAAI,IAAI,MAAM,KAAK,WAAW;AAE9C,iBAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAChD,eAAK,QAAQ,GAAG,EAAE,GAAG,IAAI;AAAA,QAC1B;AAAA,MACD;AAEA,WAAK,0BAA0B,GAAG,CAAC;AACnC,WAAK,0BAA0B,KAAK,cAAc,GAAG,CAAC;AACtD,WAAK,0BAA0B,GAAG,KAAK,cAAc,CAAC;AACtD,WAAK,2BAA2B;AAChC,WAAK,mBAAmB;AACxB,WAAK,cAAc,MAAM,WAAW;AAEpC,UAAI,KAAK,cAAc,GAAG;AACzB,aAAK,gBAAgB,IAAI;AAAA,MAC1B;AAEA,UAAI,KAAK,aAAa,MAAM;AAC3B,aAAK,YAAY,OAAO,WAAW,KAAK,YAAY,KAAK,mBAAmB,KAAK,QAAQ;AAAA,MAC1F;AAEA,WAAK,QAAQ,KAAK,WAAW,WAAW;AAAA,IACzC;AAEA,UAAM,4BAA4B,SAAS,KAAK,KAAM;AAErD,eAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,YAAI,MAAM,KAAK,MAAM,KAAK,eAAe,MAAM,EAAG;AAElD,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,cAAI,MAAM,KAAK,MAAM,KAAK,eAAe,MAAM,EAAG;AAElD,cAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAK;AAC7C,iBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,UAClC,OAAO;AACN,iBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,UAClC;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,UAAM,qBAAqB,WAAW;AAErC,UAAI,eAAe;AACnB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE3B,aAAK,SAAS,MAAM,CAAC;AAErB,YAAI,YAAY,KAAK,aAAa,IAAI;AAEtC,YAAI,KAAK,KAAK,eAAgB,WAAW;AACxC,yBAAe;AACf,oBAAU;AAAA,QACX;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,kBAAkB,SAAS,WAAW,eAAe,OAAO;AAEjE,UAAI,QAAQ,UAAU,qBAAqB,eAAe,KAAK;AAC/D,UAAI,KAAK;AAET,WAAK,KAAK;AAEV,eAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,QAAQ,OAAO;AAEnD,YAAI,IAAI,MAAM;AAEd,iBAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,GAAG,EAAE,QAAQ,OAAO;AAExD,cAAI,IAAI,MAAM;AACd,cAAI,OAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;AAEhC,cAAI,MAAM;AACT,kBAAM,UAAU,GAAG,GAAG;AACtB,kBAAM,OAAO,GAAG,CAAC;AACjB,kBAAM,OAAO,IAAI,IAAI,CAAC;AACtB,kBAAM,OAAO,IAAI,IAAI,IAAI,EAAE;AAC3B,kBAAM,OAAO,GAAG,IAAI,EAAE;AACtB,kBAAM,QAAQ;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,qBAAqB,WAAW;AAErC,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,GAAG,KAAK;AAC9C,YAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM;AAC/B;AAAA,QACD;AACA,aAAK,QAAQ,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,MAChC;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,GAAG,KAAK;AAC9C,YAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM;AAC/B;AAAA,QACD;AACA,aAAK,QAAQ,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,MAChC;AAAA,IACD;AAEA,UAAM,6BAA6B,WAAW;AAE7C,UAAI,MAAM,KAAK,mBAAmB,KAAK,UAAU;AAEjD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAEpC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAEpC,cAAI,MAAM,IAAI,CAAC;AACf,cAAI,MAAM,IAAI,CAAC;AAEf,cAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,KAAK,MAAM;AACnC;AAAA,UACD;AAEA,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,qBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,kBAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KACnC,KAAK,KAAK,KAAK,GAAK;AACzB,qBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAClC,OAAO;AACN,qBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAClC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,UAAM,kBAAkB,SAAS,MAAM;AAEtC,UAAI,OAAO,KAAK,iBAAiB,KAAK,UAAU;AAEhD,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,aAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,IAAI;AAAA,MACrE;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,aAAK,QAAQ,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,MACrE;AAAA,IACD;AAEA,UAAM,gBAAgB,SAAS,MAAM,aAAa;AAEjD,UAAI,OAAQ,KAAK,qBAAqB,IAAK;AAC3C,UAAI,OAAO,KAAK,eAAe,IAAI;AAGnC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,YAAI,IAAI,GAAG;AACV,eAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,QACtB,WAAW,IAAI,GAAG;AACjB,eAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QAC1B,OAAO;AACN,eAAK,QAAQ,KAAK,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI;AAAA,QAC9C;AAAA,MACD;AAGA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,YAAI,IAAI,GAAG;AACV,eAAK,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI,CAAC,IAAI;AAAA,QAC7C,WAAW,IAAI,GAAG;AACjB,eAAK,QAAQ,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,QACnC,OAAO;AACN,eAAK,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI;AAAA,QAC/B;AAAA,MACD;AAGA,WAAK,QAAQ,KAAK,cAAc,CAAC,EAAE,CAAC,IAAK,CAAC;AAAA,IAC3C;AAEA,UAAM,UAAU,SAAS,MAAM,aAAa;AAE3C,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,cAAc;AAC7B,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,eAAS,MAAM,KAAK,cAAc,GAAG,MAAM,GAAG,OAAO,GAAG;AAEvD,YAAI,OAAO,EAAG;AAEd,eAAO,MAAM;AAEZ,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE3B,gBAAI,KAAK,QAAQ,GAAG,EAAE,MAAM,CAAC,KAAK,MAAM;AAEvC,kBAAI,OAAO;AAEX,kBAAI,YAAY,KAAK,QAAQ;AAC5B,wBAAY,KAAK,SAAS,MAAM,WAAY,MAAM;AAAA,cACnD;AAEA,kBAAI,OAAO,KAAK,QAAQ,aAAa,KAAK,MAAM,CAAC;AAEjD,kBAAI,MAAM;AACT,uBAAO,CAAC;AAAA,cACT;AAEA,mBAAK,QAAQ,GAAG,EAAE,MAAM,CAAC,IAAI;AAC7B;AAEA,kBAAI,YAAY,IAAI;AACnB;AACA,2BAAW;AAAA,cACZ;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAEP,cAAI,MAAM,KAAK,KAAK,eAAe,KAAK;AACvC,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,OAAO;AACd,WAAO,OAAO;AAEd,WAAO,aAAa,SAAS,YAAY,mBAAmB,UAAU;AAErE,UAAI,WAAW,QAAQ,YAAY,YAAY,iBAAiB;AAEhE,UAAI,SAAS,IAAI,UAAU;AAE3B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,YAAI,OAAO,SAAS,CAAC;AACrB,eAAO,IAAI,KAAK,MAAM,CAAC;AACvB,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,gBAAgB,KAAK,MAAM,UAAU,CAAE;AACzE,aAAK,MAAM,MAAM;AAAA,MAClB;AAGA,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,0BAAkB,SAAS,CAAC,EAAE;AAAA,MAC/B;AAEA,UAAI,OAAO,gBAAgB,IAAI,iBAAiB,GAAG;AAClD,cAAM,IAAI,MAAM,4BACb,OAAO,gBAAgB,IACvB,MACC,iBAAiB,IAClB,GAAG;AAAA,MACP;AAGA,UAAI,OAAO,gBAAgB,IAAI,KAAK,iBAAiB,GAAG;AACvD,eAAO,IAAI,GAAG,CAAC;AAAA,MAChB;AAGA,aAAO,OAAO,gBAAgB,IAAI,KAAK,GAAG;AACzC,eAAO,OAAO,KAAK;AAAA,MACpB;AAGA,aAAO,MAAM;AAEZ,YAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AACnD;AAAA,QACD;AACA,eAAO,IAAI,OAAO,MAAM,CAAC;AAEzB,YAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AACnD;AAAA,QACD;AACA,eAAO,IAAI,OAAO,MAAM,CAAC;AAAA,MAC1B;AAEA,aAAO,OAAO,YAAY,QAAQ,QAAQ;AAAA,IAC3C;AAEA,WAAO,cAAc,SAAS,QAAQ,UAAU;AAE/C,UAAI,SAAS;AAEb,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,UAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AACtC,UAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AAEtC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAEzC,YAAI,UAAU,SAAS,CAAC,EAAE;AAC1B,YAAI,UAAU,SAAS,CAAC,EAAE,aAAa;AAEvC,qBAAa,KAAK,IAAI,YAAY,OAAO;AACzC,qBAAa,KAAK,IAAI,YAAY,OAAO;AAEzC,eAAO,CAAC,IAAI,IAAI,MAAM,OAAO;AAE7B,iBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AAC1C,iBAAO,CAAC,EAAE,CAAC,IAAI,MAAO,OAAO,OAAO,IAAI,MAAM;AAAA,QAC/C;AACA,kBAAU;AAEV,YAAI,SAAS,KAAK,0BAA0B,OAAO;AACnD,YAAI,UAAU,IAAI,WAAW,OAAO,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC;AAE9D,YAAI,UAAU,QAAQ,IAAI,MAAM;AAChC,eAAO,CAAC,IAAI,IAAI,MAAM,OAAO,UAAU,IAAI,CAAC;AAC5C,iBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACjC,cAAI,WAAW,IAAI,QAAQ,UAAU,IAAI,OAAO,CAAC,EAAE;AAC5D,iBAAO,CAAC,EAAE,CAAC,IAAK,YAAY,IAAI,QAAQ,IAAI,QAAQ,IAAI;AAAA,QACzD;AAAA,MAED;AAEA,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,0BAAkB,SAAS,CAAC,EAAE;AAAA,MAC/B;AAEA,UAAI,OAAO,IAAI,MAAM,cAAc;AACnC,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACpC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACzB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC5B;AAAA,QACD;AAAA,MACD;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACpC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACzB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC5B;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpbjB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,UAAU,CAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,yBAAyB,KAAK,MAAM;AAAE,UAAI,SAAS,CAAC;AAAG,eAAS,KAAK,KAAK;AAAE,YAAI,KAAK,QAAQ,CAAC,KAAK,EAAG;AAAU,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG;AAAU,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAE3N,QAAI,YAAY;AAAA,MACd,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC,EAAE;AAAA,MACjG,KAAK,YAAY,QAAQ,OAAO;AAAA,MAChC,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC,EAAE;AAAA,MACjG,KAAK,YAAY,QAAQ,OAAO;AAAA,MAChC,MAAM,YAAY,QAAQ,OAAO;AAAA,MACjC,OAAO,YAAY,QAAQ;AAAA,MAC3B,aAAa,YAAY,QAAQ,OAAO;AAAA,MACxC,OAAO,YAAY,QAAQ;AAAA,IAC7B;AAEA,QAAI,aAAa,GAAG,OAAO,YAAY,SAAU,MAAM,KAAK;AAC1D,UAAI,UAAU,KAAK,SACf,MAAM,KAAK,KACX,MAAM,KAAK,KACX,UAAU,KAAK,SACf,OAAO,KAAK,MACZ,QAAQ,KAAK,OACb,cAAc,KAAK,aACnB,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAY,+BAA+B,YAClE,QAAQ,yBAAyB,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW,QAAQ,SAAS,eAAe,OAAO,CAAC;AAExH,aAAO,QAAQ,QAAQ;AAAA,QACrB;AAAA,QACA,SAAS,CAAC,GAAG,OAAO,EAAE,QAAQ,MAAM,KAAU,SAAS,SAAS,cAAc,MAAM,aAAa,OAAO,MAAM,MAAa,CAAC;AAAA,QAC5H,QAAQ,QAAQ,QAAQ;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAAA,QACJ,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,QAC/D,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AAED,cAAU,cAAc;AACxB,cAAU,YAAY;AAEtB,YAAQ,UAAU;AAAA;AAAA;;;AC3DlB;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS;AAEjB,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,UAAU,CAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,qBAAqB;AAEzB,QAAI,sBAAsB,uBAAuB,kBAAkB;AAEnE,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,yBAAyB,KAAK,MAAM;AAAE,UAAI,SAAS,CAAC;AAAG,eAAS,KAAK,KAAK;AAAE,YAAI,KAAK,QAAQ,CAAC,KAAK,EAAG;AAAU,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG;AAAU,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAI3N,QAAI,YAAY;AAAA,MACd,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC;AAAA,MAC/F,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC;AAAA,MAC/F,OAAO,YAAY,QAAQ;AAAA,MAC3B,MAAM,YAAY,QAAQ;AAAA,MAC1B,OAAO,YAAY,QAAQ,OAAO;AAAA,IACpC;AAEA,QAAI,UAAU,GAAG,OAAO,YAAY,SAAU,MAAM,KAAK;AACvD,UAAI,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAY,YAAY,cACnD,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAY,YAAY,cACnD,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAY,MAAM,YACzC,YAAY,KAAK,MACjB,OAAO,cAAc,SAAY,MAAM,WACvC,QAAQ,KAAK,OACb,QAAQ,yBAAyB,MAAM,CAAC,WAAW,WAAW,SAAS,QAAQ,OAAO,CAAC;AAG3F,UAAI,SAAS,IAAI,SAAS,QAAQ,IAAI,oBAAoB,QAAQ,KAAK,CAAC;AACxE,aAAO,QAAQ,KAAK;AACpB,aAAO,KAAK;AACZ,UAAI,QAAQ,OAAO;AACnB,aAAO,QAAQ,QAAQ,cAAc,YAAY,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA,QAC5E;AAAA,QACA,KAAK,MAAM,IAAI,SAAU,KAAK,UAAU;AACtC,iBAAO,IAAI,IAAI,SAAU,MAAM,WAAW;AACxC,mBAAO,CAAC,OAAO,OAAO,YAAY,MAAM,WAAW,sBAAsB;AAAA,UAC3E,CAAC,EAAE,KAAK,GAAG;AAAA,QACb,CAAC,EAAE,KAAK,GAAG;AAAA,QACX;AAAA,QACA,KAAK,MAAM,IAAI,SAAU,KAAK,UAAU;AACtC,iBAAO,IAAI,IAAI,SAAU,MAAM,WAAW;AACxC,mBAAO,OAAO,OAAO,YAAY,MAAM,WAAW,sBAAsB;AAAA,UAC1E,CAAC,EAAE,KAAK,GAAG;AAAA,QACb,CAAC,EAAE,KAAK,GAAG;AAAA,QACX;AAAA,QACA;AAAA,QACA,aAAa,MAAM;AAAA,MACrB,CAAC,CAAC;AAAA,IACJ,CAAC;AAED,YAAQ,SAAS;AACjB,WAAO,cAAc;AACrB,WAAO,YAAY;AAEnB,YAAQ,UAAU;AAAA;AAAA;", "names": []}