{"version": 3, "names": ["emulatorCommand", "process", "env", "ANDROID_HOME", "getEmulators", "emulatorsOutput", "execa", "sync", "stdout", "split", "os", "EOL", "filter", "name", "launchEmulator", "emulator<PERSON>ame", "adbPath", "port", "manualCommand", "cp", "detached", "stdio", "unref", "timeout", "Promise", "resolve", "reject", "bootCheckInterval", "setInterval", "devices", "adb", "getDevices", "connected", "find", "d", "includes", "length", "cleanup", "rejectTimeout", "setTimeout", "stopWaitingAndReject", "clearTimeout", "clearInterval", "message", "Error", "on", "tryLaunchEmulator", "emulators", "success", "error"], "sources": ["../../../src/commands/runAndroid/tryLaunchEmulator.ts"], "sourcesContent": ["import os from 'os';\nimport execa from 'execa';\nimport adb from './adb';\n\nconst emulatorCommand = process.env.ANDROID_HOME\n  ? `${process.env.ANDROID_HOME}/emulator/emulator`\n  : 'emulator';\n\nexport const getEmulators = () => {\n  try {\n    const emulatorsOutput = execa.sync(emulatorCommand, ['-list-avds']).stdout;\n    return emulatorsOutput.split(os.EOL).filter((name) => name !== '');\n  } catch {\n    return [];\n  }\n};\n\nconst launchEmulator = async (\n  emulatorName: string,\n  adbPath: string,\n  port?: number,\n): Promise<boolean> => {\n  const manualCommand = `${emulatorCommand} @${emulatorName}`;\n\n  const cp = execa(\n    emulatorCommand,\n    port ? [`@${emulatorName}`, '-port', `${port}`] : [`@${emulatorName}`],\n    {\n      detached: true,\n      stdio: 'ignore',\n    },\n  );\n  cp.unref();\n  const timeout = 30;\n\n  return new Promise<boolean>((resolve, reject) => {\n    const bootCheckInterval = setInterval(async () => {\n      const devices = adb.getDevices(adbPath);\n      const connected = port\n        ? devices.find((d) => d.includes(`${port}`))\n        : devices.length > 0;\n      if (connected) {\n        cleanup();\n        resolve(true);\n      }\n    }, 1000);\n\n    // Reject command after timeout\n    const rejectTimeout = setTimeout(() => {\n      stopWaitingAndReject(\n        `It took too long to start and connect with Android emulator: ${emulatorName}. You can try starting the emulator manually from the terminal with: ${manualCommand}`,\n      );\n    }, timeout * 1000);\n\n    const cleanup = () => {\n      clearTimeout(rejectTimeout);\n      clearInterval(bootCheckInterval);\n    };\n\n    const stopWaitingAndReject = (message: string) => {\n      cleanup();\n      reject(new Error(message));\n    };\n\n    cp.on('error', ({message}) => stopWaitingAndReject(message));\n\n    cp.on('exit', () => {\n      stopWaitingAndReject(\n        `The emulator (${emulatorName}) quit before it finished opening. You can try starting the emulator manually from the terminal with: ${manualCommand}`,\n      );\n    });\n  });\n};\n\nexport default async function tryLaunchEmulator(\n  adbPath: string,\n  emulatorName?: string,\n  port?: number,\n): Promise<{success: boolean; error?: string}> {\n  const emulators = getEmulators();\n  if (emulators.length > 0) {\n    try {\n      await launchEmulator(emulatorName ?? emulators[0], adbPath, port);\n      return {success: true};\n    } catch (error) {\n      return {success: false, error: (error as any)?.message};\n    }\n  }\n  return {\n    success: false,\n    error: 'No emulators found as an output of `emulator -list-avds`',\n  };\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAwB;AAExB,MAAMA,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,YAAY,GAC3C,GAAEF,OAAO,CAACC,GAAG,CAACC,YAAa,oBAAmB,GAC/C,UAAU;AAEP,MAAMC,YAAY,GAAG,MAAM;EAChC,IAAI;IACF,MAAMC,eAAe,GAAGC,gBAAK,CAACC,IAAI,CAACP,eAAe,EAAE,CAAC,YAAY,CAAC,CAAC,CAACQ,MAAM;IAC1E,OAAOH,eAAe,CAACI,KAAK,CAACC,aAAE,CAACC,GAAG,CAAC,CAACC,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC;EACpE,CAAC,CAAC,MAAM;IACN,OAAO,EAAE;EACX;AACF,CAAC;AAAC;AAEF,MAAMC,cAAc,GAAG,OACrBC,YAAoB,EACpBC,OAAe,EACfC,IAAa,KACQ;EACrB,MAAMC,aAAa,GAAI,GAAElB,eAAgB,KAAIe,YAAa,EAAC;EAE3D,MAAMI,EAAE,GAAG,IAAAb,gBAAK,EACdN,eAAe,EACfiB,IAAI,GAAG,CAAE,IAAGF,YAAa,EAAC,EAAE,OAAO,EAAG,GAAEE,IAAK,EAAC,CAAC,GAAG,CAAE,IAAGF,YAAa,EAAC,CAAC,EACtE;IACEK,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;EACT,CAAC,CACF;EACDF,EAAE,CAACG,KAAK,EAAE;EACV,MAAMC,OAAO,GAAG,EAAE;EAElB,OAAO,IAAIC,OAAO,CAAU,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/C,MAAMC,iBAAiB,GAAGC,WAAW,CAAC,YAAY;MAChD,MAAMC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACf,OAAO,CAAC;MACvC,MAAMgB,SAAS,GAAGf,IAAI,GAClBY,OAAO,CAACI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAAE,GAAElB,IAAK,EAAC,CAAC,CAAC,GAC1CY,OAAO,CAACO,MAAM,GAAG,CAAC;MACtB,IAAIJ,SAAS,EAAE;QACbK,OAAO,EAAE;QACTZ,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,MAAMa,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrCC,oBAAoB,CACjB,gEAA+DzB,YAAa,wEAAuEG,aAAc,EAAC,CACpK;IACH,CAAC,EAAEK,OAAO,GAAG,IAAI,CAAC;IAElB,MAAMc,OAAO,GAAG,MAAM;MACpBI,YAAY,CAACH,aAAa,CAAC;MAC3BI,aAAa,CAACf,iBAAiB,CAAC;IAClC,CAAC;IAED,MAAMa,oBAAoB,GAAIG,OAAe,IAAK;MAChDN,OAAO,EAAE;MACTX,MAAM,CAAC,IAAIkB,KAAK,CAACD,OAAO,CAAC,CAAC;IAC5B,CAAC;IAEDxB,EAAE,CAAC0B,EAAE,CAAC,OAAO,EAAE,CAAC;MAACF;IAAO,CAAC,KAAKH,oBAAoB,CAACG,OAAO,CAAC,CAAC;IAE5DxB,EAAE,CAAC0B,EAAE,CAAC,MAAM,EAAE,MAAM;MAClBL,oBAAoB,CACjB,iBAAgBzB,YAAa,yGAAwGG,aAAc,EAAC,CACtJ;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEc,eAAe4B,iBAAiB,CAC7C9B,OAAe,EACfD,YAAqB,EACrBE,IAAa,EACgC;EAC7C,MAAM8B,SAAS,GAAG3C,YAAY,EAAE;EAChC,IAAI2C,SAAS,CAACX,MAAM,GAAG,CAAC,EAAE;IACxB,IAAI;MACF,MAAMtB,cAAc,CAACC,YAAY,IAAIgC,SAAS,CAAC,CAAC,CAAC,EAAE/B,OAAO,EAAEC,IAAI,CAAC;MACjE,OAAO;QAAC+B,OAAO,EAAE;MAAI,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MACd,OAAO;QAACD,OAAO,EAAE,KAAK;QAAEC,KAAK,UAAGA,KAAK,yCAAN,KAAgBN;MAAO,CAAC;IACzD;EACF;EACA,OAAO;IACLK,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;AACH"}