// Importer les annotations de données pour valider les propriétés
// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour les modèles de données
// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Modelsnamespace AxiaLivraisonAPI.Models
{
    // Classe qui représente un token blacklisté (interdit) dans la base de données
    // Classe publique qui définit un objetpublic class BlacklistedToken    public class BlacklistedToken
    {
        // Clé primaire de la table blacklisted token (auto-incrémentée)
        // Marquer comme clé primaire de la base de données[Key]        [Key]
        public int Id { get; set; }

        // ID unique du token JWT (claim jti) qui est blacklisté (obligatoire)
        // Cette propriété est obligatoire[Required]        [Required]
        public string TokenId { get; set; } // JWT ID (jti claim)

        // Date à laquelle le token a été blacklisté (obligatoire, par défaut: maintenant en UTC)
        // Cette propriété est obligatoire[Required]        [Required]
        public DateTime BlacklistedDate { get; set; } = DateTime.UtcNow;

        // Date d'expiration originale du token (obligatoire)
        // Cette propriété est obligatoire[Required]        [Required]
        public DateTime ExpiryDate { get; set; }

        // Raison pour laquelle le token a été blacklisté (optionnel)
        public string? Reason { get; set; }

        // ID de l'utilisateur propriétaire du token blacklisté (optionnel)
        public int? UtilisateurId { get; set; }

        // Propriété de navigation vers l'utilisateur propriétaire (optionnel)
        public Utilisateur? Utilisateur { get; set; }
    }
}
