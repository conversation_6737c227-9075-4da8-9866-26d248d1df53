// Importer des fonctionnalités depuis un autre fichierimport React, {useState, useEffect} fromimport React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
  ScrollView,
  ActivityIndicator,
  Platform,
  Dimensions,
} from 'react-native';
// Importer des fonctionnalités depuis un autre fichierimport {useNavigation} fromimport {useNavigation} from '@react-navigation/native';
// Importer des fonctionnalités depuis un autre fichierimport AntDesign fromimport AntDesign from 'react-native-vector-icons/AntDesign';
// Importer des fonctionnalités depuis un autre fichierimport FontAwesome fromimport FontAwesome from 'react-native-vector-icons/FontAwesome';
// Importer des fonctionnalités depuis un autre fichierimport Ionicons fromimport Ionicons from 'react-native-vector-icons/Ionicons';
// Importer des fonctionnalités depuis un autre fichierimport MaterialIcons fromimport MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// Importer des fonctionnalités depuis un autre fichierimport Feather fromimport Feather from 'react-native-vector-icons/Feather';
// Importer des fonctionnalités depuis un autre fichierimport { colors } fromimport { colors } from '../theme';

// Get screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive helper functions
// Créer une constanteconst wp =const wp = (percentage) => {
  return (percentage * screenWidth) / 100;
};

// Créer une constanteconst hp =
const hp = (percentage) => {
  return (percentage * screenHeight) / 100;
};

// Device type detection
// Créer une constanteconst isTablet =const isTablet = screenWidth >= 768;
// Créer une constanteconst isSmallDevice =const isSmallDevice = screenWidth < 350;

// Créer une constanteconst API_BASE_URL =
const API_BASE_URL = Platform.select({
  android: 'http://*************:5283', // My computer's IP address
  ios: 'http://localhost:5283',
  default: 'http://localhost:5283',
});

// Créer une constanteconst ProfileScreen =
const ProfileScreen = ({route}) => {
  // Créer une constanteconst navigation =  const navigation = useNavigation();
  const {user} = route.params || {};
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Créer une constanteconst fetchDeliveryStats =    const fetchDeliveryStats = async () => {
      try {
        setLoading(true);
        // Créer une constanteconst response =        const response = await fetch(
          `${API_BASE_URL}/api/Commandes/livreur/${user.id}`,
        );
        // Créer une constanteconst data =        const data = await response.json();

        if (response.ok) {
          // Calculate real statistics from API data
          // Créer une constanteconst totalOrders =          const totalOrders = data.length;
          // Créer une constanteconst deliveredOrders =          const deliveredOrders = data.filter(
            c => c.statut?.toLowerCase() === 'livré',
          ).length;
          // Créer une constanteconst inTransitOrders =          const inTransitOrders = data.filter(
            c => c.statut?.toLowerCase() === 'en transit',
          ).length;
          // Créer une constanteconst inPreparationOrders =          const inPreparationOrders = data.filter(
            c => c.statut?.toLowerCase() === 'en préparation',
          ).length;
          // Créer une constanteconst totalRevenue =          const totalRevenue = data.reduce(
            (sum, order) => sum + (order.montantTotale || 0),
            0,
          );

          // Calculate success rate (delivered/total)
          // Créer une constanteconst successRate =          const successRate =
            totalOrders > 0
              ? Math.round((deliveredOrders / totalOrders) * 100)
              : 0;

          setStats({
            totalOrders,
            deliveredOrders,
            inTransitOrders,
            inPreparationOrders,
            totalRevenue,
            successRate,
          });
        } else {
          setError(data.message || 'Échec de récupération des statistiques');
        }
      } catch (err) {
        setError('Erreur de connexion au serveur');
        console.error('Error fetching stats:', err);
      } finally {
        setLoading(false);
      }
    };

    if (user?.id) {
      fetchDeliveryStats();
    }
  }, [user]);

  // Créer une constanteconst handleLogout =
  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Déconnecter',
          onPress: () => navigation.navigate('Login'),
          style: 'destructive',
        },
      ],
      {cancelable: false},
    );
  };

  // Créer une constanteconst renderStatCard =
  const renderStatCard = (icon, value, label, color, percentage) => (
    <View style={styles.statCard}>
      <View style={styles.statCardContent}>
        <View style={styles.statTextSection}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statLabel}>{label}</Text>
        </View>
        <View style={[styles.statIconContainer, {backgroundColor: color}]}>
          {React.cloneElement(icon, {size: isTablet ? 16 : 20, color: '#FFF'})}
        </View>
      </View>
      <View style={styles.statProgressBar}>
        <View style={[styles.statProgress, {backgroundColor: color, width: `${percentage}%`}]} />
      </View>
      <Text style={styles.statPercentage}>{percentage}%</Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <ActivityIndicator size="large" color={colors.primary.main} style={styles.loader} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* White Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}>
            <AntDesign name="arrowleft" size={24} color={colors.primary.main} />
          </TouchableOpacity>
          <View style={styles.headerSpacer} />
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/images/logo.png')}
              style={styles.headerLogo}
            />
          </View>
        </View>

        {/* Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Image
              source={
                user?.image
                  ? {uri: user.image}
                  : require('../assets/images/default-profile.png')
              }
              style={styles.avatar}
            />
          </View>

          <Text style={styles.userName}>{user?.nom || 'Livreur'}</Text>
          <Text style={styles.userTitle}>Livreur Professionnel</Text>
        </View>

        {/* Stats Section - Only Real Data */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mes Performances</Text>

          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : (
            <View style={styles.statsGrid}>
              {renderStatCard(
                <Feather name="package" />,
                stats?.totalOrders || 0,
                'Total Commandes',
                colors.primary.main,
                100, // Total commandes = 100% de référence
              )}

              {renderStatCard(
                <Feather name="check-circle" />,
                stats?.deliveredOrders || 0,
                'Livrées',
                colors.status.success,
                stats?.totalOrders > 0 ? Math.round((stats.deliveredOrders / stats.totalOrders) * 100) : 0,
              )}

              {renderStatCard(
                <Feather name="truck" />,
                stats?.inTransitOrders || 0,
                'En Transit',
                colors.status.info,
                stats?.totalOrders > 0 ? Math.round((stats.inTransitOrders / stats.totalOrders) * 100) : 0,
              )}

              {renderStatCard(
                <Feather name="clock" />,
                stats?.inPreparationOrders || 0,
                'En Préparation',
                colors.status.warning,
                stats?.totalOrders > 0 ? Math.round((stats.inPreparationOrders / stats.totalOrders) * 100) : 0,
              )}

              {renderStatCard(
                <FontAwesome name="money" />,
                `${(stats?.totalRevenue || 0).toFixed(2)} DT`,
                'Revenu Total',
                colors.secondary.main,
                stats?.totalRevenue > 0 ? Math.min(100, Math.round((stats.totalRevenue / 1000) * 10)) : 0, // Échelle relative
              )}

              {renderStatCard(
                <MaterialIcons name="verified" />,
                `${stats?.successRate || 0}%`,
                'Taux de Réussite',
                colors.primary.dark,
                stats?.successRate || 0,
              )}
            </View>
          )}
        </View>

        {/* Personal Info Section - Only Real Data */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations Personnelles</Text>

          <View style={styles.infoItem}>
            <View style={[styles.infoIcon, {backgroundColor: colors.primary.lighter}]}>
              <Ionicons name="mail" size={20} color={colors.primary.main} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>
                {user?.email || 'Non disponible'}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={[styles.infoIcon, {backgroundColor: colors.primary.lighter}]}>
              <FontAwesome name="phone" size={20} color={colors.primary.main} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Téléphone</Text>
              <Text style={styles.infoValue}>
                {user?.telephone || 'Non disponible'}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={[styles.infoIcon, {backgroundColor: colors.primary.lighter}]}>
              <MaterialIcons name="person" size={20} color={colors.primary.main} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Identifiant</Text>
              <Text style={styles.infoValue}>
                {user?.identifiant || 'Non disponible'}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Logout Button */}
      <TouchableOpacity
        onPress={handleLogout}
        style={styles.logoutButton}
        activeOpacity={0.8}>
        <Text style={styles.logoutButtonText}>Déconnexion</Text>
        <MaterialIcons name="logout" size={20} color="#FFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

// Créer une constanteconst styles =
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral.background,
  },
  scrollContainer: {
    paddingBottom: isTablet ? hp(3) : hp(2.5),
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: isTablet ? wp(4) : wp(5),
    paddingTop: Platform.OS === 'android' ? (isTablet ? hp(6) : hp(7)) : (isTablet ? hp(4) : hp(4.5)),
    backgroundColor: colors.neutral.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral.border,
  },
  backButton: {
    padding: isTablet ? wp(1.5) : wp(1.2),
  },
  headerTitle: {
    fontSize: isTablet ? wp(3.5) : wp(5),
    fontWeight: 'bold',
    color: colors.primary.main,
  },
  headerSpacer: {
    flex: 1,
  },
  logoContainer: {
    width: isTablet ? wp(10) : wp(14),
    height: isTablet ? wp(10) : wp(14),
    borderRadius: isTablet ? wp(5) : wp(7),
    borderWidth: 3,
    borderColor: '#FFAA80',
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
  },
  headerLogo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  profileSection: {
    alignItems: 'center',
    padding: isTablet ? wp(4) : wp(5),
    marginTop: isTablet ? hp(1.5) : hp(1.2),
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: isTablet ? hp(2) : hp(1.8),
    shadowColor: colors.shadow.medium,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  avatar: {
    width: isTablet ? wp(20) : wp(30),
    height: isTablet ? wp(20) : wp(30),
    borderRadius: isTablet ? wp(10) : wp(15),
    borderWidth: isTablet ? 2 : 3,
    borderColor: colors.neutral.surface,
    backgroundColor: colors.neutral.border,
  },
  userName: {
    fontSize: isTablet ? wp(4.5) : wp(6),
    fontWeight: 'bold',
    color: colors.text.dark,
    marginBottom: isTablet ? hp(0.8) : hp(0.6),
  },
  userTitle: {
    fontSize: isTablet ? wp(3) : wp(4),
    color: colors.text.secondary,
    marginBottom: isTablet ? hp(1.5) : hp(1.2),
  },
  section: {
    backgroundColor: colors.neutral.surface,
    borderRadius: isTablet ? wp(3) : wp(4),
    padding: isTablet ? wp(4) : wp(5),
    marginHorizontal: isTablet ? wp(3) : wp(4),
    marginBottom: isTablet ? wp(3) : wp(4),
    shadowColor: colors.shadow.medium,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: isTablet ? wp(3.5) : wp(4.5),
    fontWeight: 'bold',
    color: colors.text.dark,
    marginBottom: isTablet ? wp(3) : wp(4),
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral.border,
    paddingBottom: isTablet ? wp(1.5) : wp(2),
  },
  statsGrid: {
    flexDirection: 'column',
  },
  statCard: {
    width: '100%',
    backgroundColor: colors.neutral.surface,
    borderRadius: isTablet ? wp(2) : wp(2.5),
    padding: isTablet ? wp(3) : wp(3.5),
    marginBottom: isTablet ? wp(1.5) : wp(2),
    shadowColor: colors.shadow.medium,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 0.5,
    borderColor: colors.neutral.borderLight,
  },
  statCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: isTablet ? wp(1.5) : wp(2),
  },
  statTextSection: {
    flex: 1,
  },
  statIconContainer: {
    width: isTablet ? wp(8) : wp(10),
    height: isTablet ? wp(8) : wp(10),
    borderRadius: isTablet ? wp(4) : wp(5),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: isTablet ? wp(3.5) : wp(4.5),
    fontWeight: 'bold',
    color: colors.text.dark,
    marginBottom: isTablet ? wp(0.5) : wp(0.8),
  },
  statLabel: {
    fontSize: isTablet ? wp(2.2) : wp(2.8),
    color: colors.text.secondary,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.3,
  },
  statProgressBar: {
    height: isTablet ? wp(1) : wp(1.2),
    backgroundColor: colors.neutral.borderLight,
    borderRadius: isTablet ? wp(0.5) : wp(0.6),
    overflow: 'hidden',
  },
  statProgress: {
    height: '100%',
    borderRadius: isTablet ? wp(0.5) : wp(0.6),
  },
  statPercentage: {
    fontSize: isTablet ? wp(2) : wp(2.5),
    color: colors.text.secondary,
    fontWeight: '500',
    textAlign: 'right',
    marginTop: isTablet ? wp(0.8) : wp(1),
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: isTablet ? wp(2.5) : wp(3.5),
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral.borderLight,
  },
  infoIcon: {
    width: isTablet ? wp(8) : wp(11),
    height: isTablet ? wp(8) : wp(11),
    borderRadius: isTablet ? wp(4) : wp(5.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: isTablet ? wp(3) : wp(4),
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: isTablet ? wp(2.5) : wp(3.5),
    color: colors.text.secondary,
    marginBottom: isTablet ? wp(0.5) : wp(0.5),
  },
  infoValue: {
    fontSize: isTablet ? wp(3) : wp(4),
    color: colors.text.dark,
    fontWeight: '500',
  },
  errorContainer: {
    padding: isTablet ? wp(3) : wp(3.8),
    backgroundColor: colors.statusBackground.error,
    borderRadius: isTablet ? wp(1.5) : wp(2),
    marginBottom: isTablet ? wp(3) : wp(3.8),
  },
  errorText: {
    color: colors.status.error,
    textAlign: 'center',
    fontSize: isTablet ? wp(2.5) : wp(3.5),
  },
  logoutButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    margin: isTablet ? wp(3) : wp(4),
    padding: isTablet ? wp(2) : wp(2.5), // Réduit de wp(3)/wp(4) à wp(2)/wp(2.5)
    backgroundColor: colors.primary.main,
    borderRadius: isTablet ? wp(2) : wp(3),
    shadowColor: colors.primary.main,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  logoutButtonText: {
    color: colors.text.inverse,
    fontSize: isTablet ? wp(2.5) : wp(3.5), // Réduit de wp(3)/wp(4) à wp(2.5)/wp(3.5)
    fontWeight: 'bold',
    marginRight: isTablet ? wp(1.5) : wp(2), // Réduit de wp(2)/wp(2.5) à wp(1.5)/wp(2)
  },
});

// Exporter comme élément principal de ce fichierexport default
export default ProfileScreen;