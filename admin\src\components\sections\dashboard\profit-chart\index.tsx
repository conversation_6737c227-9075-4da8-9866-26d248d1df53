// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport ButtonGroup fromimport ButtonGroup from '@mui/material/ButtonGroup';
// Importer des fonctionnalités depuis un autre fichierimport Button fromimport Button from '@mui/material/Button';
// Importer des fonctionnalités depuis un autre fichierimport ProfitChart fromimport ProfitChart from './ProfitChart';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

type Periode = 'semaine' | 'mois' | 'annee';

// Définir une interface TypeScriptinterface ProfitData
interface ProfitData {
  profitTotal: number;
  pourcentageChangement: number;
  donneesPeriode: Array<{
    jour?: string;
    mois?: string;
    annee?: string;
    date: string;
    profit: number;
  }>;
  periode: Periode;
}

// Définir une interface TypeScriptinterface ProfitChartSectionProps
interface ProfitChartSectionProps {
  sx?: SxProps;
}

// Créer une constanteconst ProfitChartSection =
const ProfitChartSection = ({ sx }: ProfitChartSectionProps) => {
  const [periode, setPeriode] = useState<Periode>('semaine');
  const [data, setData] = useState<ProfitData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchData =    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        // Créer une constanteconst response =        const response = await axios.get(`${API_URL}/api/statistiques/profits?periode=${periode}`);
        setData(response.data);
      } catch (err) {
        console.error('Erreur lors de la récupération des profits:', err);
        setError('Impossible de charger les données');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [periode]);

  // Préparer les données pour le graphique
  // Créer une constanteconst chartData =  const chartData = data?.donneesPeriode
    ? {
        labels: data.donneesPeriode.map((item) => item.jour || item.mois || item.annee || ''),
        profits: data.donneesPeriode.map((item) => item.profit),
      }
    : { labels: [], profits: [] };

  return (
    <Box component={Paper} sx={{ p: 3, ...sx }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h4" color="text.primary">
          Profits
        </Typography>
        <ButtonGroup variant="outlined" size="small">
          {['semaine', 'mois', 'annee'].map((p) => (
            <Button
              key={p}
              onClick={() => setPeriode(p as Periode)}
              sx={{
                ...(periode === p
                  ? {
                      bgcolor: 'secondary.main', // Bleu ciel
                      borderColor: 'secondary.main',
                      color: 'white',
                      '&:hover': {
                        bgcolor: 'secondary.dark',
                        borderColor: 'secondary.dark',
                      },
                    }
                  : {
                      color: 'secondary.main', // Bleu ciel
                      borderColor: 'secondary.main',
                      '&:hover': {
                        bgcolor: 'secondary.light',
                        borderColor: 'secondary.main',
                      },
                    }),
              }}
            >
              {p === 'semaine' ? 'Semaine' : p === 'mois' ? 'Mois' : 'Année'}
            </Button>
          ))}
        </ButtonGroup>
      </Stack>

      <Stack direction="row" spacing={3} height="calc(100% - 60px)">
        {/* Graphique sur toute la largeur */}
        <Box width="100%" height="100%">
          {loading ? (
            <Stack alignItems="center" justifyContent="center" height="100%">
              <Typography variant="body1" color="text.secondary">
                Chargement des données...
              </Typography>
            </Stack>
          ) : error ? (
            <Stack alignItems="center" justifyContent="center" height="100%">
              <Typography variant="body1" color="error">
                {error}
              </Typography>
            </Stack>
          ) : (
            <ProfitChart data={chartData} periode={periode} />
          )}
        </Box>
      </Stack>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default ProfitChartSection;
