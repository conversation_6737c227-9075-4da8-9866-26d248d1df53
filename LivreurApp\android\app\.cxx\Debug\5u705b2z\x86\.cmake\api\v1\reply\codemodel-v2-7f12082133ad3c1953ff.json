{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-e61434a1f3bae5159f0a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNCGeolocationSpec_autolinked_build", "jsonFile": "directory-RNCGeolocationSpec_autolinked_build-Debug-91293c3955e8526e0aeb.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rncamerakit_specs_autolinked_build", "jsonFile": "directory-rncamerakit_specs_autolinked_build-Debug-4b4fa6d20a29aa6782e4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-12bdb728d2217a3f1617.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-a4a5cd74c5bbf1c5c4cd.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-dd5e70785d21430f6f7d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-d950655e3e0eb8468cc0.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba", "jsonFile": "target-react_codegen_RNCGeolocationSpec-Debug-4e3d3821af5e3531f040.json", "name": "react_codegen_RNCGeolocationSpec", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-543eb29369f9b2242d0e.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-c9eb9ca4aff0ab5867f9.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rncamerakit_specs::@d945e98661337998d651", "jsonFile": "target-react_codegen_rncamerakit_specs-Debug-0a94d1edda64c0980533.json", "name": "react_codegen_rncamerakit_specs", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-5b36973ad55c154a3a7b.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-6e896463f389426862a9.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/x86", "source": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}