﻿// Importer des fonctionnalités depuis un autre fichierimport { useState } fromimport { useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Menu fromimport Menu from '@mui/material/Menu';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from '@mui/material/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconButton fromimport IconButton from '@mui/material/IconButton';
// Importer des fonctionnalités depuis un autre fichierimport ListItemText fromimport ListItemText from '@mui/material/ListItemText';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from '@mui/material/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface Action
interface Action {
  id: number;
  icon: string;
  title: string;
}

const actions: Action[] = [
  {
    id: 1,
    icon: 'ic:baseline-sync',
    title: 'Sync',
  },
  {
    id: 2,
    icon: 'ic:baseline-edit',
    title: 'Edit',
  },
  {
    id: 3,
    icon: 'ic:baseline-delete-outline',
    title: 'Remove',
  },
];

// Créer une constanteconst ActionMenu =
const ActionMenu = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  // Créer une constanteconst open =  const open = Boolean(anchorEl);

  // Créer une constanteconst handleActionButtonClick =
  const handleActionButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Créer une constanteconst handleActionMenuClose =
  const handleActionMenuClose = () => {
    setAnchorEl(null);
  };

  // Créer une constanteconst handleActionItemClick =
  const handleActionItemClick = () => {
    handleActionMenuClose();
  };

  return (
    <Box pr={2}>
      <IconButton
        onClick={handleActionButtonClick}
        sx={{ p: 0.75, border: 'none', bgcolor: 'transparent !important' }}
        size="medium"
      >
        <IconifyIcon icon="solar:menu-dots-bold" color="text.primary" />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleActionMenuClose}
        onClick={handleActionMenuClose}
        sx={{
          mt: 0.5,
          '& .MuiList-root': {
            width: 140,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {actions.map((actionItem) => {
          return (
            <MenuItem key={actionItem.id} onClick={handleActionItemClick}>
              <ListItemIcon sx={{ mr: 1, fontSize: 'h5.fontSize' }}>
                <IconifyIcon
                  icon={actionItem.icon}
                  color={actionItem.id === 3 ? 'error.main' : 'text.primary'}
                />
              </ListItemIcon>
              <ListItemText>
                <Typography color={actionItem.id === 3 ? 'error.main' : 'text.primary'}>
                  {actionItem.title}
                </Typography>
              </ListItemText>
            </MenuItem>
          );
        })}
      </Menu>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default ActionMenu;
