// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const YearCalendar: Components<Omit<Theme, 'components'>>['MuiYearCalendar'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      '& .MuiPickersYear-root': {
        '& .MuiPickersYear-yearButton': {
          '&.Mui-selected': {
            backgroundColor: theme.palette.primary.main,
          },
        },
      },
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default YearCalendar;
