{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,303,415,541,622,689,792,867,930,1022,1087,1154,1226,1298,1352,1473,1532,1596,1650,1727,1859,1944,2025,2144,2231,2314,2406,2473,2539,2611,2688,2779,2859,2938,3013,3092,3182,3255,3349,3446,3520,3593,3692,3747,3815,3903,3992,4054,4118,4181,4290,4395,4498,4607", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "221,298,410,536,617,684,787,862,925,1017,1082,1149,1221,1293,1347,1468,1527,1591,1645,1722,1854,1939,2020,2139,2226,2309,2401,2468,2534,2606,2683,2774,2854,2933,3008,3087,3177,3250,3344,3441,3515,3588,3687,3742,3810,3898,3987,4049,4113,4176,4285,4390,4493,4602,4684"}, "to": {"startLines": "2,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3769,3881,4007,6373,6440,6543,6618,6681,6773,6838,6905,6977,7049,7103,7224,7283,7347,7401,7478,7610,7695,7776,7895,7982,8065,8157,8224,8290,8362,8439,8530,8610,8689,8764,8843,8933,9006,9100,9197,9271,9344,9443,9498,9566,9654,9743,9805,9869,9932,10041,10146,10249,10358", "endLines": "5,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "271,3034,3876,4002,4083,6435,6538,6613,6676,6768,6833,6900,6972,7044,7098,7219,7278,7342,7396,7473,7605,7690,7771,7890,7977,8060,8152,8219,8285,8357,8434,8525,8605,8684,8759,8838,8928,9001,9095,9192,9266,9339,9438,9493,9561,9649,9738,9800,9864,9927,10036,10141,10244,10353,10435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4088,4199,4375,4510,4614,4782,4911,5035,5277,5437,5547,5712,5843,6001,6158,6219,6288", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "4194,4370,4505,4609,4777,4906,5030,5140,5432,5542,5707,5838,5996,6153,6214,6283,6368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,491,603,691,794,909,988,1065,1156,1249,1344,1438,1538,1631,1726,1820,1911,2004,2085,2189,2292,2390,2497,2604,2709,2866,10440", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "379,486,598,686,789,904,983,1060,1151,1244,1339,1433,1533,1626,1721,1815,1906,1999,2080,2184,2287,2385,2492,2599,2704,2861,2957,10517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "34,35,36,37,38,39,40,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3039,3137,3241,3340,3443,3549,3656,10522", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3132,3236,3335,3438,3544,3651,3764,10618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5145", "endColumns": "131", "endOffsets": "5272"}}]}]}