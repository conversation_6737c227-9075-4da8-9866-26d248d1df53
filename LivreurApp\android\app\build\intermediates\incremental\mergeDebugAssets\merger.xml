<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:barcode-scanning:17.3.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1d2d269b27637256fd6a043b4cddf4b\transformed\barcode-scanning-17.3.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1d2d269b27637256fd6a043b4cddf4b\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1d2d269b27637256fd6a043b4cddf4b\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1d2d269b27637256fd6a043b4cddf4b\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-linear-gradient\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-camera-kit" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-camera-kit\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\netinfo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_geolocation" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-community\geolocation\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\main\assets"/><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons"><file name="fonts/AntDesign.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\AntDesign.ttf"/><file name="fonts/Entypo.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Entypo.ttf"/><file name="fonts/EvilIcons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\EvilIcons.ttf"/><file name="fonts/Feather.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Feather.ttf"/><file name="fonts/FontAwesome.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Brands.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Regular.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome6_Brands.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Brands.ttf"/><file name="fonts/FontAwesome6_Regular.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Regular.ttf"/><file name="fonts/FontAwesome6_Solid.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Solid.ttf"/><file name="fonts/Fontisto.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Fontisto.ttf"/><file name="fonts/Foundation.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialIcons.ttf"/><file name="fonts/Octicons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Octicons.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\SimpleLineIcons.ttf"/><file name="fonts/Zocial.ttf" path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Zocial.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>