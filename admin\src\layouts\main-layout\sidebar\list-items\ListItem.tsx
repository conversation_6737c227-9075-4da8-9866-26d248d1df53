﻿// Importer des fonctionnalités depuis un autre fichierimport { MenuItem } fromimport { MenuItem } from 'routes/sitemap';
// Importer des fonctionnalités depuis un autre fichierimport Link fromimport Link from '@mui/material/Link';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from '@mui/material/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport ListItemButton fromimport ListItemButton from '@mui/material/ListItemButton';
// Importer des fonctionnalités depuis un autre fichierimport ListItemText fromimport ListItemText from '@mui/material/ListItemText';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface ListItemProps
interface ListItemProps extends MenuItem {
  onClick?: () => void;
}

// Créer une constanteconst ListItem =
const ListItem = ({ subheader, icon, path, active, onClick }: ListItemProps) => {
  return (
    <Stack mb={1} component={Link} href={path} alignItems="center" justifyContent="space-between">
      <ListItemButton onClick={onClick}>
        <ListItemIcon>
          {icon && (
            <IconifyIcon
              icon={icon}
              fontSize="h4.fontSize"
              sx={{
                color: active ? 'primary.main' : null,
              }}
            />
          )}
        </ListItemIcon>
        <ListItemText
          primary={subheader}
          sx={{
            '& .MuiListItemText-primary': {
              color: active ? 'primary.main' : null,
              fontWeight: active ? 600 : 500,
            },
          }}
        />
      </ListItemButton>

      <Box
        height={36}
        width={4}
        borderRadius={10}
        bgcolor={active ? 'primary.main' : 'transparent'}
      />
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default ListItem;
