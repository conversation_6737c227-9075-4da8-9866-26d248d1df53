import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  DialogContentText,
} from '@mui/material';
import axios from 'axios';
import { Utilisateur } from './types';
import GestionPermissionsUtilisateur from '../permission/GestionPermissionsUtilisateur';

interface UtilisateurTableProps {
  utilisateurs: Utilisateur[];
  onEditUtilisateur: (utilisateur: Utilisateur) => void;
  onDeleteUtilisateur: (id: number) => void;
}

const UtilisateurTable: React.FC<UtilisateurTableProps> = ({
  utilisateurs,
  onEditUtilisateur,
  onDeleteUtilisateur,
}) => {
  const [selectedUtilisateur, setSelectedUtilisateur] = useState<Utilisateur | null>(null);
  const [openPermissionsDialog, setOpenPermissionsDialog] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [utilisateurToDelete, setUtilisateurToDelete] = useState<Utilisateur | null>(null);
  const [forceDeleteDialogOpen, setForceDeleteDialogOpen] = useState(false);
  const API_URL = 'http://localhost:5283';

  const handleSavePermissions = async (utilisateurId: number, permissionIds: number[]) => {
    try {
      await axios.post(`${API_URL}/api/permissions/assigner`, {
        utilisateurId,
        permissionIds,
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des permissions:', error);
    }
  };

  const handleDeleteClick = (utilisateur: Utilisateur) => {
    setUtilisateurToDelete(utilisateur);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!utilisateurToDelete) return;

    try {
      await axios.delete(`${API_URL}/api/utilisateurs/supprimer/${utilisateurToDelete.id}`);
      onDeleteUtilisateur(utilisateurToDelete.id);
      setDeleteDialogOpen(false);
      setUtilisateurToDelete(null);
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);

      // Si l'utilisateur a des commandes, proposer la suppression forcée
      if (error.response?.status === 400) {
        setDeleteDialogOpen(false);
        setForceDeleteDialogOpen(true);
      }
    }
  };

  const confirmForceDelete = async () => {
    if (!utilisateurToDelete) return;

    try {
      await axios.delete(`${API_URL}/api/utilisateurs/supprimer-force/${utilisateurToDelete.id}`);
      onDeleteUtilisateur(utilisateurToDelete.id);
      setForceDeleteDialogOpen(false);
      setUtilisateurToDelete(null);
    } catch (error) {
      console.error('Erreur lors de la suppression forcée:', error);
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setForceDeleteDialogOpen(false);
    setUtilisateurToDelete(null);
  };

  return (
    <>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>Image</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Nom</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Téléphone</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Identifiant</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {utilisateurs.map((utilisateur) => (
              <TableRow
                key={utilisateur.id}
                hover
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell>
                  {utilisateur.imagePath && (
                    <img
                      src={`${API_URL}${utilisateur.imagePath}`}
                      alt={utilisateur.nom}
                      style={{
                        width: '50px',
                        height: '50px',
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  )}
                </TableCell>
                <TableCell>{utilisateur.nom}</TableCell>
                <TableCell>{utilisateur.email}</TableCell>
                <TableCell>{utilisateur.telephone}</TableCell>
                <TableCell>{utilisateur.identifiant}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={() => onEditUtilisateur(utilisateur)}
                      sx={{ textTransform: 'none' }}
                    >
                      Modifier
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      onClick={() => handleDeleteClick(utilisateur)}
                      sx={{ textTransform: 'none' }}
                    >
                      Supprimer
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => {
                        setSelectedUtilisateur(utilisateur);
                        setOpenPermissionsDialog(true);
                      }}
                      sx={{
                        backgroundColor: '#ff9800',
                        '&:hover': { backgroundColor: '#f57c00' },
                        textTransform: 'none',
                      }}
                    >
                      Permissions
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {selectedUtilisateur && (
        <GestionPermissionsUtilisateur
          utilisateur={selectedUtilisateur}
          open={openPermissionsDialog}
          onClose={() => setOpenPermissionsDialog(false)}
          onSave={handleSavePermissions}
        />
      )}

      {/* Dialog de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Confirmer la suppression
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Êtes-vous sûr de vouloir supprimer l'utilisateur{' '}
            <strong>{utilisateurToDelete?.nom} {utilisateurToDelete?.prenom}</strong> ?
            <br />
            Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de suppression forcée */}
      <Dialog
        open={forceDeleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="force-delete-dialog-title"
        aria-describedby="force-delete-dialog-description"
      >
        <DialogTitle id="force-delete-dialog-title">
          Suppression avec données associées
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="force-delete-dialog-description">
            L'utilisateur <strong>{utilisateurToDelete?.nom} {utilisateurToDelete?.prenom}</strong>
            est associé à des commandes existantes.
            <br /><br />
            <Typography color="error" variant="body2">
              ⚠️ Attention : Supprimer cet utilisateur supprimera également toutes ses commandes
              et rapports associés. Cette action est irréversible.
            </Typography>
            <br />
            Voulez-vous continuer avec la suppression forcée ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={confirmForceDelete} color="error" variant="contained">
            Supprimer tout
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default UtilisateurTable;
