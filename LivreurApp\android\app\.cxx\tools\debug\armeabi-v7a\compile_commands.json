[{"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IE:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\E_\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IE:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\RNCGeolocationSpec-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\RNCGeolocationSpec-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\RNCGeolocationSpec-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\RNCGeolocationSpecJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\RNCGeolocationSpecJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\RNCGeolocationSpecJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\States.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\react\\renderer\\components\\rncamerakit_specs\\rncamerakit_specsJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\rncamerakit_specsJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rncamerakit_specs\\rncamerakit_specsJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rncamerakit_specs_autolinked_build\\CMakeFiles\\react_codegen_rncamerakit_specs.dir\\rncamerakit_specs-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\rncamerakit_specs-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\rncamerakit_specs-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\61012cd45ff08503015a1b280de4bb9f\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0179b6856136b4560df75b9b86818a06\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\9780e997eff3556d23f9b860e9143e13\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\34726ede54477dee9ea1cf0b6a664602\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a65a39f1b80c9741b274fd3bdce7b0a7\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\34726ede54477dee9ea1cf0b6a664602\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a65a39f1b80c9741b274fd3bdce7b0a7\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a5b5463ae6dbfd96fd48ad2a092ae403\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\127806176f131c321a809f16310076ba\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\153a811717e8c5211c1c3aec748ca078\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\3830aa37505d69fb05b3a7646cccbc8d\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\42b2e89a5e76b482872d03d137254c4d\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\641051fb0d7860fc43ae0c9e6c0f6e7d\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\153a811717e8c5211c1c3aec748ca078\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\641051fb0d7860fc43ae0c9e6c0f6e7d\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\153a811717e8c5211c1c3aec748ca078\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e42da9fcab52f45e9128dc732ec983e1\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8d3a359c534d1e4805cc9427aa6643fd\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5508d8ec90151540e638d1910dc6ebe2\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\186d6580070f96e0f6f71ddc609ada8a\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5508d8ec90151540e638d1910dc6ebe2\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\186d6580070f96e0f6f71ddc609ada8a\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8d3a359c534d1e4805cc9427aa6643fd\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/5u705b2z/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IE:/PFE/Test/AxiaLivraison2/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison2\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]