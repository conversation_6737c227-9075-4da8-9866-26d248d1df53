// Importer des fonctionnalités depuis un autre fichierimport { Box, BoxProps } fromimport { Box, BoxProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Icon, IconProps } fromimport { Icon, IconProps } from '@iconify/react';

// Définir une interface TypeScriptinterface IconifyProps
interface IconifyProps extends BoxProps {
  icon: IconProps['icon'];
}

// Créer une constanteconst IconifyIcon =
const IconifyIcon = ({ icon, ...rest }: IconifyProps) => {
  return <Box component={Icon} icon={icon} {...rest} />;
};

// Exporter comme élément principal de ce fichierexport default
export default IconifyIcon;
