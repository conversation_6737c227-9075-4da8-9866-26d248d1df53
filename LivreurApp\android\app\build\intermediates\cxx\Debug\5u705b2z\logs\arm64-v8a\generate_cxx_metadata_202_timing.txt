# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 157ms
  generate-prefab-packages
    exec-prefab 876ms
    [gap of 25ms]
  generate-prefab-packages completed in 908ms
  execute-generate-process
    exec-configure 1324ms
    [gap of 153ms]
  execute-generate-process completed in 1478ms
  [gap of 37ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 2631ms

