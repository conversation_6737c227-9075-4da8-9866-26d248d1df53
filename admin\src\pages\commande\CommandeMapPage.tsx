// Importer des fonctionnalités depuis un autre fichierimport React fromimport React from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useParams, useNavigate } fromimport { useParams, useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport CommandeMap fromimport CommandeMap from './CommandeMap';

const CommandeMapPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  // Créer une constanteconst navigate =  const navigate = useNavigate();

  // Créer une constanteconst handleBack =
  const handleBack = () => {
    navigate(-1);
  };

  return <>{id && <CommandeMap commandeId={parseInt(id, 10)} onBack={handleBack} />}</>;
};

// Exporter comme élément principal de ce fichierexport default
export default CommandeMapPage;
