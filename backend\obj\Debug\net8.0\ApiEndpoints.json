[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_4", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "Connexion", "RelativePath": "api/authentification/connexion", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDTO", "Type": "AxiaLivraisonAPI.DTO.LoginDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "ConnexionClient", "RelativePath": "api/authentification/connexion-client", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDTO", "Type": "AxiaLivraisonAPI.DTO.LoginDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "ConnexionLivreur", "RelativePath": "api/authentification/connexion-livreur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDTO", "Type": "AxiaLivraisonAPI.DTO.LoginDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "Deconnexion", "RelativePath": "api/authentification/deconnexion", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "RefreshToken", "RelativePath": "api/authentification/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "AxiaLivraisonAPI.DTO.RefreshTokenDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "RegisterClient", "RelativePath": "api/authentification/register-client", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDTO", "Type": "AxiaLivraisonAPI.DTO.RegisterClientDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.AuthentificationController", "Method": "RevokeToken", "RelativePath": "api/authentification/revoke-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "AxiaLivraisonAPI.DTO.RefreshTokenDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "CreateCommande", "RelativePath": "api/commandes/ajouter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeDTO", "Type": "AxiaLivraisonAPI.DTO.CommandeDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetCommandeById", "RelativePath": "api/commandes/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.DTO.CommandeDetailsDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetCommandeByCodeSuivi", "RelativePath": "api/commandes/details/code/{codeSuivi}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeSuivi", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.DTO.CommandeDetailsDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetAllCommandes", "RelativePath": "api/commandes/liste", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.DTO.CommandeDTO, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetCommandesByLivreurId", "RelativePath": "api/commandes/livreur/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.DTO.CommandeDTO, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "UpdateCommandeStatut", "RelativePath": "api/commandes/modifier-statut/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "AxiaLivraisonAPI.DTO.UpdateStatutDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "UpdateCommande", "RelativePath": "api/commandes/modifier/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "commandeDTO", "Type": "AxiaLivraisonAPI.DTO.CommandeDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "UpdateLocation", "RelativePath": "api/commandes/position", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "locationUpdate", "Type": "AxiaLivraisonAPI.DTO.LocationUpdateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetDeliveryLocation", "RelativePath": "api/commandes/position/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "GetDeliveryLocationByCode", "RelativePath": "api/commandes/position/code/{codeSuivi}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeSuivi", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.CommandeController", "Method": "DeleteCommande", "RelativePath": "api/commandes/supprimer/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "CreateFournisseur", "RelativePath": "api/fournisseurs/ajouter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurDTO", "Type": "FournisseurDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "GetFournisseurById", "RelativePath": "api/fournisseurs/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.Models.Fournisseur", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "GetFournisseurIdentifiants", "RelativePath": "api/fournisseurs/identifiants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "GetAllFournisseurs", "RelativePath": "api/fournisseurs/liste", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.Models.Fournisseur, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "UpdateFournisseur", "RelativePath": "api/fournisseurs/modifier/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "fournisseurDTO", "Type": "FournisseurDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "DeleteFournisseurWithCommandes", "RelativePath": "api/fournisseurs/supprimer-avec-commandes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.FournisseurController", "Method": "DeleteFournisseur", "RelativePath": "api/fournisseurs/supprimer/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "GetNotification", "RelativePath": "api/Notification/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "DeleteNotification", "RelativePath": "api/Notification/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "GetNotificationsByCommande", "RelativePath": "api/Notification/by-commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "MarkAllAsReadForCommande", "RelativePath": "api/Notification/mark-all-read/by-commande/{commandeId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "MarkAsRead", "RelativePath": "api/Notification/mark-read/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.NotificationController", "Method": "GetUnreadCountByCommande", "RelativePath": "api/Notification/unread-count/by-commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "CreatePermission", "RelativePath": "api/permissions/ajouter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "permissionDTO", "Type": "AxiaLivraisonAPI.DTO.PermissionDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "AssignerPermissions", "RelativePath": "api/permissions/assigner", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "AxiaLivraisonAPI.DTO.AssignerPermissionsDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "GetPermissionById", "RelativePath": "api/permissions/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.Models.Permission", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "GetAllPermissions", "RelativePath": "api/permissions/liste", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.Models.Permission, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "UpdatePermission", "RelativePath": "api/permissions/modifier/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "permissionDTO", "Type": "AxiaLivraisonAPI.DTO.PermissionDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "DeletePermission", "RelativePath": "api/permissions/supprimer/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.PermissionController", "Method": "GetPermissionsByUtilisateur", "RelativePath": "api/permissions/utilisateur/{utilisateurId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "utilisateurId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.Models.Permission, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "CreateTestData", "RelativePath": "api/Rapport/create-test-data/{livreurId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "livreurId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetDonneesCompletes", "RelativePath": "api/Rapport/donnees-completes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreurDashboard", "RelativePath": "api/Rapport/livreur-dashboard/{livreurId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "livreurId", "Type": "System.Int32", "IsRequired": true}, {"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreurs", "RelativePath": "api/Rapport/livreurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreursEvolutionTemporelle", "RelativePath": "api/Rapport/livreurs-evolution-temporelle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreursPerformance", "RelativePath": "api/Rapport/livreurs-performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreursRepartitionStatuts", "RelativePath": "api/Rapport/livreurs-repartition-statuts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreursStatistiquesGlobales", "RelativePath": "api/Rapport/livreurs-statistiques-globales", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}, {"Name": "livreurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetLivreursTempsTraitementMoyen", "RelativePath": "api/Rapport/livreurs-temps-moyen", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.RapportController", "Method": "GetTopLivreurs", "RelativePath": "api/Rapport/top-livreurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesAujourdhui", "RelativePath": "api/Statistiques/commandes-aujourdhui", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesCreesParJour", "RelativePath": "api/Statistiques/commandes-creees-par-jour", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesFournisseursMensuels", "RelativePath": "api/Statistiques/commandes-fournisseurs-mensuels", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesParFournisseur", "RelativePath": "api/Statistiques/commandes-par-fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesParJour", "RelativePath": "api/Statistiques/commandes-par-jour", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesParStatut", "RelativePath": "api/Statistiques/commandes-par-statut", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesParStatutSemaine", "RelativePath": "api/Statistiques/commandes-par-statut-semaine", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandesParUtilisateur", "RelativePath": "api/Statistiques/commandes-par-utilisateur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetLivreursActifs", "RelativePath": "api/Statistiques/livreurs-actifs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetLivreursCommandesMois", "RelativePath": "api/Statistiques/livreurs-commandes-mois", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetLivreursDisponibles", "RelativePath": "api/Statistiques/livreurs-disponibles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetLivreursEnTransit", "RelativePath": "api/Statistiques/livreurs-en-transit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetCommandeLocations", "RelativePath": "api/Statistiques/locations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetMontantTotal", "RelativePath": "api/Statistiques/montant-total", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetPerformanceMensuelle", "RelativePath": "api/Statistiques/performance-mensuelle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetProfits", "RelativePath": "api/Statistiques/profits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "periode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetRevenusMois", "RelativePath": "api/Statistiques/revenus-mois", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTauxConversion", "RelativePath": "api/Statistiques/taux-conversion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTempsTraitement", "RelativePath": "api/Statistiques/temps-traitement", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTopClients", "RelativePath": "api/Statistiques/top-clients", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTopFournisseur", "RelativePath": "api/Statistiques/top-fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTopLivreur", "RelativePath": "api/Statistiques/top-livreur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.StatistiquesController", "Method": "GetTotalCommandes", "RelativePath": "api/Statistiques/total-commandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "CreateUtilisateur", "RelativePath": "api/utilisateurs/ajouter", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Telephone", "Type": "System.String", "IsRequired": false}, {"Name": "Identifiant", "Type": "System.String", "IsRequired": false}, {"Name": "MotDePasse", "Type": "System.String", "IsRequired": false}, {"Name": "EstAdmin", "Type": "System.Boolean", "IsRequired": false}, {"Name": "EstLivreur", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "CreateLivreur", "RelativePath": "api/utilisateurs/ajouter-livreur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Telephone", "Type": "System.String", "IsRequired": false}, {"Name": "Identifiant", "Type": "System.String", "IsRequired": false}, {"Name": "MotDePasse", "Type": "System.String", "IsRequired": false}, {"Name": "EstAdmin", "Type": "System.Boolean", "IsRequired": false}, {"Name": "EstLivreur", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetUtilisateursAvecPermissions", "RelativePath": "api/utilisateurs/avec-permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.DTO.UtilisateurAvecPermissionsDTO, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetUtilisateurInfoByIdentifiant", "RelativePath": "api/utilisateurs/details-livreur/{identifiant}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "identifiant", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.Controllers.UtilisateurController+LivreurDetailsDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetUtilisateurById", "RelativePath": "api/utilisateurs/details/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "AxiaLivraisonAPI.Models.Utilisateur", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetUtilisateurIdentifiants", "RelativePath": "api/utilisateurs/identifiants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetAllUtilisateurs", "RelativePath": "api/utilisateurs/liste", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.Models.Utilisateur, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "GetLivreurs", "RelativePath": "api/utilisateurs/livreurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AxiaLivraisonAPI.Models.Utilisateur, AxiaLivraisonAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "MigrerMotsDePasse", "RelativePath": "api/utilisateurs/migrer-mots-de-passe", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "UpdateUtilisateur", "RelativePath": "api/utilisateurs/modifier/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Telephone", "Type": "System.String", "IsRequired": false}, {"Name": "Identifiant", "Type": "System.String", "IsRequired": false}, {"Name": "MotDePasse", "Type": "System.String", "IsRequired": false}, {"Name": "EstAdmin", "Type": "System.Boolean", "IsRequired": false}, {"Name": "EstLivreur", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "DeleteUtilisateurForce", "RelativePath": "api/utilisateurs/supprimer-force/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AxiaLivraisonAPI.Controllers.UtilisateurController", "Method": "DeleteUtilisateur", "RelativePath": "api/utilisateurs/supprimer/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]