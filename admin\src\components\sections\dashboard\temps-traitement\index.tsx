// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport ToggleButtonGroup fromimport ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
// Importer des fonctionnalités depuis un autre fichierimport ToggleButton fromimport ToggleButton from '@mui/material/ToggleButton';
// Importer des fonctionnalités depuis un autre fichierimport FormControl fromimport FormControl from '@mui/material/FormControl';
// Importer des fonctionnalités depuis un autre fichierimport Select, { SelectChangeEvent } fromimport Select, { SelectChangeEvent } from '@mui/material/Select';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from '@mui/material/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport InputLabel fromimport InputLabel from '@mui/material/InputLabel';
// Importer des fonctionnalités depuis un autre fichierimport { useTheme } fromimport { useTheme } from '@mui/material/styles';
// Importer des fonctionnalités depuis un autre fichierimport TempsTraitementChart fromimport TempsTraitementChart from './TempsTraitementChart';
// Importer des fonctionnalités depuis un autre fichierimport TempsTraitementGaugeChart fromimport TempsTraitementGaugeChart from './TempsTraitementGaugeChart';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

// Définir une interface TypeScriptinterface CommandeTempsTraitement
interface CommandeTempsTraitement {
  commandeId: number;
  tempsTraitement: number; // en heures
  nomLivreur: string;
}

type ChartType = 'triangle' | 'pyramid';

// Définir une interface TypeScriptinterface TempsTraitementProps
interface TempsTraitementProps {
  sx?: SxProps;
}

// Créer une constanteconst TempsTraitement =
const TempsTraitement = ({ sx }: TempsTraitementProps) => {
  // Créer une constanteconst theme =  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<CommandeTempsTraitement[]>([]);
  const [chartType, setChartType] = useState<ChartType>('triangle');
  const [selectedMonth, setSelectedMonth] = useState<string>('');

  // Générer la liste des 12 derniers mois
  // Créer une constanteconst generateMonthOptions =  const generateMonthOptions = () => {
    // Créer une constanteconst options =    const options = [];
    // Créer une constanteconst currentDate =    const currentDate = new Date();

    for (let i = 0; i < 12; i++) {
      // Créer une constanteconst date =      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      // Créer une constanteconst monthKey =      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      // Créer une constanteconst monthLabel =      const monthLabel = date.toLocaleDateString('fr-FR', {
        month: 'long',
        year: 'numeric',
      });

      options.push({
        value: monthKey,
        label: monthLabel,
        month: date.getMonth() + 1,
        year: date.getFullYear(),
      });
    }

    return options;
  };

  // Créer une constanteconst monthOptions =
  const monthOptions = generateMonthOptions();

  // Initialiser avec le mois actuel
  useEffect(() => {
    if (monthOptions.length > 0 && !selectedMonth) {
      setSelectedMonth(monthOptions[0].value);
    }
  }, [monthOptions, selectedMonth]);

  useEffect(() => {
    // Créer une constanteconst fetchData =    const fetchData = async () => {
      if (!selectedMonth) return;

      try {
        setLoading(true);

        // Extraire le mois et l'année du selectedMonth
        const [year, month] = selectedMonth.split('-').map(Number);

        // Créer une constanteconst response =
        const response = await axios.get(
          'http://localhost:5283/api/statistiques/temps-traitement',
          {
            params: {
              mois: month,
              annee: year,
            },
          },
        );
        setData(response.data);
        setError(null);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError('Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedMonth]);

  // Préparer les données pour le graphique
  // Créer une constanteconst chartData =  const chartData = {
    ids: data.map((item) => `#${item.commandeId}`),
    temps: data.map((item) => item.tempsTraitement),
    livreurs: data.map((item) => item.nomLivreur),
  };

  // Obtenir le label du mois sélectionné
  // Créer une constanteconst selectedMonthLabel =  const selectedMonthLabel =
    monthOptions.find((option) => option.value === selectedMonth)?.label || '';

  // Créer une constanteconst handleMonthChange =
  const handleMonthChange = (event: SelectChangeEvent<string>) => {
    setSelectedMonth(event.target.value);
  };

  // Créer une constanteconst handleChartTypeChange =
  const handleChartTypeChange = (
    _event: React.MouseEvent<HTMLElement>,
    newType: ChartType | null,
  ) => {
    if (newType !== null) {
      setChartType(newType);
    }
  };

  // Créer une constanteconst renderChart =
  const renderChart = () => {
    if (loading) {
      return (
        <Stack alignItems="center" justifyContent="center" height="100%">
          <Typography variant="body1" color="text.secondary">
            Chargement des données...
          </Typography>
        </Stack>
      );
    }

    if (error) {
      return (
        <Stack alignItems="center" justifyContent="center" height="100%">
          <Typography variant="body1" color="error">
            {error}
          </Typography>
        </Stack>
      );
    }

    if (data.length === 0) {
      return (
        <Stack alignItems="center" justifyContent="center" height="100%">
          <Typography variant="body1" color="text.secondary">
            Aucune commande livrée ce mois-ci
          </Typography>
        </Stack>
      );
    }

    switch (chartType) {
      case 'pyramid':
        return <TempsTraitementGaugeChart data={chartData} />;
      case 'triangle':
      default:
        return <TempsTraitementChart data={chartData} />;
    }
  };

  return (
    <Box
      component={Paper}
      p={3}
      sx={{
        height: 350,
        boxShadow: '0 4px 7px rgba(0, 0, 0, 0.05)',
        borderRadius: 5,
        ...sx,
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
        <Box>
          <Typography variant="h5" color="text.primary" fontWeight={700}>
            Temps de Traitement - Commandes Livrées
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            {selectedMonthLabel.charAt(0).toUpperCase() + selectedMonthLabel.slice(1)}
          </Typography>
        </Box>
        <Stack direction="row" sx={{ marginRight: -2 }} spacing={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 160 }}>
            <InputLabel id="month-select-label">Mois</InputLabel>
            <Select
              labelId="month-select-label"
              value={selectedMonth}
              label="Mois"
              onChange={handleMonthChange}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                },
              }}
            >
              {monthOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <ToggleButtonGroup
            size="small"
            value={chartType}
            exclusive
            onChange={handleChartTypeChange}
            aria-label="type de graphique"
            sx={{
              '& .MuiToggleButton-root': {
                marginTop: -3,
                marginRight: 1,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: '8px !important',
                padding: '6px 10px',
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main,
                  color: theme.palette.info.lighter,
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                  },
                },
              },
            }}
          >
            <ToggleButton value="triangle" aria-label="graphique en triangles">
              <IconifyIcon
                icon="mdi:chart-timeline-variant"
                fontSize="0.8rem"
                sx={{ color: chartType === 'triangle' ? 'inherit' : theme.palette.text.primary }}
              />
            </ToggleButton>
            <ToggleButton value="pyramid" aria-label="graphique en pyramide">
              <IconifyIcon
                icon="mdi:chart-donut-variant"
                fontSize="0.8rem"
                sx={{ color: chartType === 'pyramid' ? 'inherit' : theme.palette.text.primary }}
              />
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      </Stack>

      <Box height="calc(100% - 60px)">{renderChart()}</Box>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default TempsTraitement;
