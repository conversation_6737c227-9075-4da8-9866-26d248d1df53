# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/RNCGeolocationSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/Props.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/RNCGeolocationSpecJSI-generated.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/rncamerakit_specs-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/ComponentDescriptors.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/EventEmitters.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/Props.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/ShadowNodes.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/States.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs/rncamerakit_specsJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp"
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86_64/CMakeFiles/cmake.verify_globs")
endif()
