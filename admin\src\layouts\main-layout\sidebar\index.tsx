// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Drawer fromimport Drawer from '@mui/material/Drawer';
// Importer des fonctionnalités depuis un autre fichierimport DrawerItems fromimport DrawerItems from './DrawerItems';

// Définir une interface TypeScriptinterface SidebarProps
interface SidebarProps {
  mobileOpen: boolean;
  setMobileOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsClosing: React.Dispatch<React.SetStateAction<boolean>>;
}

// Créer une constanteconst Sidebar =
const Sidebar = ({ mobileOpen, setMobileOpen, setIsClosing }: SidebarProps) => {
  // Créer une constanteconst handleDrawerClose =  const handleDrawerClose = () => {
    setIsClosing(true);
    setMobileOpen(false);
  };

  // Créer une constanteconst handleDrawerTransitionEnd =
  const handleDrawerTransitionEnd = () => {
    setIsClosing(false);
  };

  return (
    <Box
      component="nav"
      width={{ lg: 290 }}
      flexShrink={{ lg: 0 }}
      display={{ xs: 'none', lg: 'block' }}
    >
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onTransitionEnd={handleDrawerTransitionEnd}
        onClose={handleDrawerClose}
        ModalProps={{ keepMounted: true }}
        sx={{ display: { xs: 'block', lg: 'none' } }}
      >
        <DrawerItems />
      </Drawer>

      <Drawer variant="permanent" sx={{ display: { xs: 'none', lg: 'block' } }} open>
        <DrawerItems />
      </Drawer>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Sidebar;
