{"version": 3, "names": ["findBuildGradle", "sourceDir", "isLibrary", "buildGradlePath", "path", "join", "buildGradleKtsPath", "fs", "existsSync"], "sources": ["../../src/config/findBuildGradle.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nexport function findBuildGradle(sourceDir: string, isLibrary: boolean) {\n  const buildGradlePath = path.join(\n    sourceDir,\n    isLibrary ? 'build.gradle' : 'app/build.gradle',\n  );\n  const buildGradleKtsPath = path.join(\n    sourceDir,\n    isLibrary ? 'build.gradle.kts' : 'app/build.gradle.kts',\n  );\n\n  if (fs.existsSync(buildGradlePath)) {\n    return buildGradlePath;\n  } else if (fs.existsSync(buildGradleKtsPath)) {\n    return buildGradleKtsPath;\n  } else {\n    return null;\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAEjB,SAASA,eAAe,CAACC,SAAiB,EAAEC,SAAkB,EAAE;EACrE,MAAMC,eAAe,GAAGC,eAAI,CAACC,IAAI,CAC/BJ,SAAS,EACTC,SAAS,GAAG,cAAc,GAAG,kBAAkB,CAChD;EACD,MAAMI,kBAAkB,GAAGF,eAAI,CAACC,IAAI,CAClCJ,SAAS,EACTC,SAAS,GAAG,kBAAkB,GAAG,sBAAsB,CACxD;EAED,IAAIK,aAAE,CAACC,UAAU,CAACL,eAAe,CAAC,EAAE;IAClC,OAAOA,eAAe;EACxB,CAAC,MAAM,IAAII,aAAE,CAACC,UAAU,CAACF,kBAAkB,CAAC,EAAE;IAC5C,OAAOA,kBAAkB;EAC3B,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF"}