// Importer une bibliothèque nécessaireusing System;using System;
// Importer une bibliothèque nécessaireusing System.Text.Json.Serialization;using System.Text.Json.Serialization;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Models
namespace AxiaLivraisonAPI.Models
{
    // Classe publique qui définit un objetpublic class Notification    public class Notification
    {
        public int Id { get; set; }

        [Json<PERSON>ropertyName("recipient")]
        public string Destinataire { get; set; }

        [JsonPropertyName("subject")]
        public string Sujet { get; set; }

        [JsonPropertyName("body")]
        public string Contenu { get; set; }

        [JsonPropertyName("sentDate")]
        public DateTime DateEnvoi { get; set; }

        public bool EstLue { get; set; }

        // Command relationship
        public int CommandeId { get; set; }  // Nullable if notifications can exist without commands

        [JsonIgnore]  // Prevents circular references in JSON
        public Commande? Commande { get; set; }
    }
}