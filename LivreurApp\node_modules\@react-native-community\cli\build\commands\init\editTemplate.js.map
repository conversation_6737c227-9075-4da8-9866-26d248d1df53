{"version": 3, "names": ["DEFAULT_TITLE_PLACEHOLDER", "validatePackageName", "packageName", "packageNameParts", "split", "packageNameRegex", "length", "test", "replaceNameInUTF8File", "filePath", "projectName", "templateName", "logger", "debug", "fileContent", "fs", "readFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "RegExp", "toLowerCase", "writeFile", "renameFile", "old<PERSON>ame", "newName", "newFileName", "path", "join", "dirname", "basename", "rename", "shouldRenameFile", "nameToReplace", "includes", "shouldIgnoreFile", "match", "isIosFile", "UNDERSCORED_DOTFILES", "processDotfiles", "dotfile", "find", "e", "undefined", "createAndroidPackagePaths", "pathParts", "slice", "pathToFolders", "segmentsList", "initialDir", "process", "cwd", "chdir", "rmdir", "segment", "mkdirSync", "replacePlaceholderWithPackageName", "placeholder<PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "cleanPackageName", "walk", "reverse", "iosFile", "stat", "isDirectory", "fileName", "error", "CLIError", "changePlaceholderInTemplate", "projectTitle", "message"], "sources": ["../../../src/commands/init/editTemplate.ts"], "sourcesContent": ["import path from 'path';\nimport {C<PERSON><PERSON>rror, logger} from '@react-native-community/cli-tools';\nimport walk from '../../tools/walk';\n\n// We need `graceful-fs` behavior around async file renames on Win32.\n// `gracefulify` does not support patching `fs.promises`. Use `fs-extra`, which\n// exposes its own promise-based interface over `graceful-fs`.\nimport fs from 'fs-extra';\n\ninterface PlaceholderConfig {\n  projectName: string;\n  placeholderName: string;\n  placeholderTitle?: string;\n  projectTitle?: string;\n  packageName?: string;\n}\n\n/**\n  TODO: This is a default placeholder for title in react-native template.\n  We should get rid of this once custom templates adapt `placeholderTitle` in their configurations.\n*/\nconst DEFAULT_TITLE_PLACEHOLDER = 'Hello App Display Name';\n\nexport function validatePackageName(packageName: string) {\n  const packageNameParts = packageName.split('.');\n  const packageNameRegex = /^([a-zA-Z]([a-zA-Z0-9_])*\\.)+[a-zA-Z]([a-zA-Z0-9_])*$/u;\n\n  if (packageNameParts.length < 2) {\n    throw `The package name ${packageName} is invalid. It should contain at least two segments, e.g. com.app`;\n  }\n\n  if (!packageNameRegex.test(packageName)) {\n    throw `The ${packageName} package name is not valid. It can contain only alphanumeric characters and dots.`;\n  }\n}\n\nexport async function replaceNameInUTF8File(\n  filePath: string,\n  projectName: string,\n  templateName: string,\n) {\n  logger.debug(`Replacing in ${filePath}`);\n  const fileContent = await fs.readFile(filePath, 'utf8');\n  const replacedFileContent = fileContent\n    .replace(new RegExp(templateName, 'g'), projectName)\n    .replace(\n      new RegExp(templateName.toLowerCase(), 'g'),\n      projectName.toLowerCase(),\n    );\n\n  if (fileContent !== replacedFileContent) {\n    await fs.writeFile(filePath, replacedFileContent, 'utf8');\n  }\n}\n\nasync function renameFile(filePath: string, oldName: string, newName: string) {\n  const newFileName = path.join(\n    path.dirname(filePath),\n    path.basename(filePath).replace(new RegExp(oldName, 'g'), newName),\n  );\n\n  logger.debug(`Renaming ${filePath} -> file:${newFileName}`);\n\n  await fs.rename(filePath, newFileName);\n}\n\nfunction shouldRenameFile(filePath: string, nameToReplace: string) {\n  return path.basename(filePath).includes(nameToReplace);\n}\n\nfunction shouldIgnoreFile(filePath: string) {\n  return filePath.match(/node_modules|yarn.lock|package-lock.json/g);\n}\n\nfunction isIosFile(filePath: string) {\n  return filePath.includes('ios');\n}\n\nconst UNDERSCORED_DOTFILES = [\n  'buckconfig',\n  'eslintrc.js',\n  'flowconfig',\n  'gitattributes',\n  'gitignore',\n  'prettierrc.js',\n  'watchmanconfig',\n  'editorconfig',\n  'bundle',\n  'ruby-version',\n  'node-version',\n  'xcode.env',\n];\n\nasync function processDotfiles(filePath: string) {\n  const dotfile = UNDERSCORED_DOTFILES.find((e) => filePath.includes(`_${e}`));\n\n  if (dotfile === undefined) {\n    return;\n  }\n\n  await renameFile(filePath, `_${dotfile}`, `.${dotfile}`);\n}\n\nasync function createAndroidPackagePaths(\n  filePath: string,\n  packageName: string,\n) {\n  const pathParts = filePath.split('/').slice(-2);\n\n  if (pathParts[0] === 'java' && pathParts[1] === 'com') {\n    const pathToFolders = filePath.split('/').slice(0, -2).join('/');\n    const segmentsList = packageName.split('.');\n\n    if (segmentsList.length > 1) {\n      const initialDir = process.cwd();\n      process.chdir(filePath.split('/').slice(0, -1).join('/'));\n\n      try {\n        await fs.rename(\n          `${filePath}/${segmentsList.join('.')}`,\n          `${pathToFolders}/${segmentsList[segmentsList.length - 1]}`,\n        );\n        await fs.rmdir(filePath);\n\n        for (const segment of segmentsList) {\n          fs.mkdirSync(segment);\n          process.chdir(segment);\n        }\n        await fs.rename(\n          `${pathToFolders}/${segmentsList[segmentsList.length - 1]}`,\n          process.cwd(),\n        );\n      } catch {\n        throw 'Failed to create correct paths for Android.';\n      }\n\n      process.chdir(initialDir);\n    }\n  }\n}\n\nexport async function replacePlaceholderWithPackageName({\n  projectName,\n  placeholderName,\n  placeholderTitle,\n  packageName,\n}: Omit<Required<PlaceholderConfig>, 'projectTitle'>) {\n  validatePackageName(packageName);\n  const cleanPackageName = packageName.replace(/[^\\p{L}\\p{N}.]+/gu, '');\n\n  for (const filePath of walk(process.cwd()).reverse()) {\n    if (shouldIgnoreFile(filePath)) {\n      continue;\n    }\n\n    const iosFile = isIosFile(filePath);\n\n    if (!(await fs.stat(filePath)).isDirectory()) {\n      let newName = iosFile ? projectName : cleanPackageName;\n\n      //replace bundleID for iOS\n      await replaceNameInUTF8File(\n        filePath,\n        `PRODUCT_BUNDLE_IDENTIFIER = \"${cleanPackageName}\"`,\n        'PRODUCT_BUNDLE_IDENTIFIER = \"(.*)\"',\n      );\n\n      if (filePath.includes('app.json')) {\n        await replaceNameInUTF8File(filePath, projectName, placeholderName);\n      } else {\n        // replace main component name for Android package\n        await replaceNameInUTF8File(\n          filePath,\n          `return \"${projectName}\"`,\n          `return \"${placeholderName}\"`,\n        );\n        await replaceNameInUTF8File(\n          filePath,\n          `<string name=\"app_name\">${projectName}</string>`,\n          `<string name=\"app_name\">${placeholderTitle}</string>`,\n        );\n\n        await replaceNameInUTF8File(\n          filePath,\n          newName,\n          `com.${placeholderName}`,\n        );\n        await replaceNameInUTF8File(filePath, newName, placeholderName);\n        await replaceNameInUTF8File(filePath, newName, placeholderTitle);\n      }\n    }\n\n    let fileName = cleanPackageName;\n\n    if (shouldRenameFile(filePath, placeholderName)) {\n      if (iosFile) {\n        fileName = projectName;\n      }\n\n      await renameFile(filePath, placeholderName, fileName);\n    } else if (shouldRenameFile(filePath, placeholderName.toLowerCase())) {\n      await renameFile(\n        filePath,\n        placeholderName.toLowerCase(),\n        fileName.toLowerCase(),\n      );\n    }\n    try {\n      await createAndroidPackagePaths(filePath, cleanPackageName);\n    } catch (error) {\n      throw new CLIError('Failed to create correct paths for Android.');\n    }\n\n    await processDotfiles(filePath);\n  }\n}\n\nexport async function changePlaceholderInTemplate({\n  projectName,\n  placeholderName,\n  placeholderTitle = DEFAULT_TITLE_PLACEHOLDER,\n  projectTitle = projectName,\n  packageName,\n}: PlaceholderConfig) {\n  logger.debug(`Changing ${placeholderName} for ${projectName} in template`);\n\n  if (packageName) {\n    try {\n      await replacePlaceholderWithPackageName({\n        projectName,\n        placeholderName,\n        placeholderTitle,\n        packageName,\n      });\n    } catch (error) {\n      throw new CLIError((error as Error).message);\n    }\n  } else {\n    for (const filePath of walk(process.cwd()).reverse()) {\n      if (shouldIgnoreFile(filePath)) {\n        continue;\n      }\n      if (!(await fs.stat(filePath)).isDirectory()) {\n        await replaceNameInUTF8File(filePath, projectName, placeholderName);\n        await replaceNameInUTF8File(filePath, projectTitle, placeholderTitle);\n      }\n      if (shouldRenameFile(filePath, placeholderName)) {\n        await renameFile(filePath, placeholderName, projectName);\n      } else if (shouldRenameFile(filePath, placeholderName.toLowerCase())) {\n        await renameFile(\n          filePath,\n          placeholderName.toLowerCase(),\n          projectName.toLowerCase(),\n        );\n      }\n\n      await processDotfiles(filePath);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAH1B;AACA;AACA;;AAWA;AACA;AACA;AACA;AACA,MAAMA,yBAAyB,GAAG,wBAAwB;AAEnD,SAASC,mBAAmB,CAACC,WAAmB,EAAE;EACvD,MAAMC,gBAAgB,GAAGD,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;EAC/C,MAAMC,gBAAgB,GAAG,wDAAwD;EAEjF,IAAIF,gBAAgB,CAACG,MAAM,GAAG,CAAC,EAAE;IAC/B,MAAO,oBAAmBJ,WAAY,oEAAmE;EAC3G;EAEA,IAAI,CAACG,gBAAgB,CAACE,IAAI,CAACL,WAAW,CAAC,EAAE;IACvC,MAAO,OAAMA,WAAY,mFAAkF;EAC7G;AACF;AAEO,eAAeM,qBAAqB,CACzCC,QAAgB,EAChBC,WAAmB,EACnBC,YAAoB,EACpB;EACAC,kBAAM,CAACC,KAAK,CAAE,gBAAeJ,QAAS,EAAC,CAAC;EACxC,MAAMK,WAAW,GAAG,MAAMC,kBAAE,CAACC,QAAQ,CAACP,QAAQ,EAAE,MAAM,CAAC;EACvD,MAAMQ,mBAAmB,GAAGH,WAAW,CACpCI,OAAO,CAAC,IAAIC,MAAM,CAACR,YAAY,EAAE,GAAG,CAAC,EAAED,WAAW,CAAC,CACnDQ,OAAO,CACN,IAAIC,MAAM,CAACR,YAAY,CAACS,WAAW,EAAE,EAAE,GAAG,CAAC,EAC3CV,WAAW,CAACU,WAAW,EAAE,CAC1B;EAEH,IAAIN,WAAW,KAAKG,mBAAmB,EAAE;IACvC,MAAMF,kBAAE,CAACM,SAAS,CAACZ,QAAQ,EAAEQ,mBAAmB,EAAE,MAAM,CAAC;EAC3D;AACF;AAEA,eAAeK,UAAU,CAACb,QAAgB,EAAEc,OAAe,EAAEC,OAAe,EAAE;EAC5E,MAAMC,WAAW,GAAGC,eAAI,CAACC,IAAI,CAC3BD,eAAI,CAACE,OAAO,CAACnB,QAAQ,CAAC,EACtBiB,eAAI,CAACG,QAAQ,CAACpB,QAAQ,CAAC,CAACS,OAAO,CAAC,IAAIC,MAAM,CAACI,OAAO,EAAE,GAAG,CAAC,EAAEC,OAAO,CAAC,CACnE;EAEDZ,kBAAM,CAACC,KAAK,CAAE,YAAWJ,QAAS,YAAWgB,WAAY,EAAC,CAAC;EAE3D,MAAMV,kBAAE,CAACe,MAAM,CAACrB,QAAQ,EAAEgB,WAAW,CAAC;AACxC;AAEA,SAASM,gBAAgB,CAACtB,QAAgB,EAAEuB,aAAqB,EAAE;EACjE,OAAON,eAAI,CAACG,QAAQ,CAACpB,QAAQ,CAAC,CAACwB,QAAQ,CAACD,aAAa,CAAC;AACxD;AAEA,SAASE,gBAAgB,CAACzB,QAAgB,EAAE;EAC1C,OAAOA,QAAQ,CAAC0B,KAAK,CAAC,2CAA2C,CAAC;AACpE;AAEA,SAASC,SAAS,CAAC3B,QAAgB,EAAE;EACnC,OAAOA,QAAQ,CAACwB,QAAQ,CAAC,KAAK,CAAC;AACjC;AAEA,MAAMI,oBAAoB,GAAG,CAC3B,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,eAAe,EACf,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,QAAQ,EACR,cAAc,EACd,cAAc,EACd,WAAW,CACZ;AAED,eAAeC,eAAe,CAAC7B,QAAgB,EAAE;EAC/C,MAAM8B,OAAO,GAAGF,oBAAoB,CAACG,IAAI,CAAEC,CAAC,IAAKhC,QAAQ,CAACwB,QAAQ,CAAE,IAAGQ,CAAE,EAAC,CAAC,CAAC;EAE5E,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB;EACF;EAEA,MAAMpB,UAAU,CAACb,QAAQ,EAAG,IAAG8B,OAAQ,EAAC,EAAG,IAAGA,OAAQ,EAAC,CAAC;AAC1D;AAEA,eAAeI,yBAAyB,CACtClC,QAAgB,EAChBP,WAAmB,EACnB;EACA,MAAM0C,SAAS,GAAGnC,QAAQ,CAACL,KAAK,CAAC,GAAG,CAAC,CAACyC,KAAK,CAAC,CAAC,CAAC,CAAC;EAE/C,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IACrD,MAAME,aAAa,GAAGrC,QAAQ,CAACL,KAAK,CAAC,GAAG,CAAC,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAClB,IAAI,CAAC,GAAG,CAAC;IAChE,MAAMoB,YAAY,GAAG7C,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;IAE3C,IAAI2C,YAAY,CAACzC,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAM0C,UAAU,GAAGC,OAAO,CAACC,GAAG,EAAE;MAChCD,OAAO,CAACE,KAAK,CAAC1C,QAAQ,CAACL,KAAK,CAAC,GAAG,CAAC,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAClB,IAAI,CAAC,GAAG,CAAC,CAAC;MAEzD,IAAI;QACF,MAAMZ,kBAAE,CAACe,MAAM,CACZ,GAAErB,QAAS,IAAGsC,YAAY,CAACpB,IAAI,CAAC,GAAG,CAAE,EAAC,EACtC,GAAEmB,aAAc,IAAGC,YAAY,CAACA,YAAY,CAACzC,MAAM,GAAG,CAAC,CAAE,EAAC,CAC5D;QACD,MAAMS,kBAAE,CAACqC,KAAK,CAAC3C,QAAQ,CAAC;QAExB,KAAK,MAAM4C,OAAO,IAAIN,YAAY,EAAE;UAClChC,kBAAE,CAACuC,SAAS,CAACD,OAAO,CAAC;UACrBJ,OAAO,CAACE,KAAK,CAACE,OAAO,CAAC;QACxB;QACA,MAAMtC,kBAAE,CAACe,MAAM,CACZ,GAAEgB,aAAc,IAAGC,YAAY,CAACA,YAAY,CAACzC,MAAM,GAAG,CAAC,CAAE,EAAC,EAC3D2C,OAAO,CAACC,GAAG,EAAE,CACd;MACH,CAAC,CAAC,MAAM;QACN,MAAM,6CAA6C;MACrD;MAEAD,OAAO,CAACE,KAAK,CAACH,UAAU,CAAC;IAC3B;EACF;AACF;AAEO,eAAeO,iCAAiC,CAAC;EACtD7C,WAAW;EACX8C,eAAe;EACfC,gBAAgB;EAChBvD;AACiD,CAAC,EAAE;EACpDD,mBAAmB,CAACC,WAAW,CAAC;EAChC,MAAMwD,gBAAgB,GAAGxD,WAAW,CAACgB,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;EAErE,KAAK,MAAMT,QAAQ,IAAI,IAAAkD,aAAI,EAACV,OAAO,CAACC,GAAG,EAAE,CAAC,CAACU,OAAO,EAAE,EAAE;IACpD,IAAI1B,gBAAgB,CAACzB,QAAQ,CAAC,EAAE;MAC9B;IACF;IAEA,MAAMoD,OAAO,GAAGzB,SAAS,CAAC3B,QAAQ,CAAC;IAEnC,IAAI,CAAC,CAAC,MAAMM,kBAAE,CAAC+C,IAAI,CAACrD,QAAQ,CAAC,EAAEsD,WAAW,EAAE,EAAE;MAC5C,IAAIvC,OAAO,GAAGqC,OAAO,GAAGnD,WAAW,GAAGgD,gBAAgB;;MAEtD;MACA,MAAMlD,qBAAqB,CACzBC,QAAQ,EACP,gCAA+BiD,gBAAiB,GAAE,EACnD,oCAAoC,CACrC;MAED,IAAIjD,QAAQ,CAACwB,QAAQ,CAAC,UAAU,CAAC,EAAE;QACjC,MAAMzB,qBAAqB,CAACC,QAAQ,EAAEC,WAAW,EAAE8C,eAAe,CAAC;MACrE,CAAC,MAAM;QACL;QACA,MAAMhD,qBAAqB,CACzBC,QAAQ,EACP,WAAUC,WAAY,GAAE,EACxB,WAAU8C,eAAgB,GAAE,CAC9B;QACD,MAAMhD,qBAAqB,CACzBC,QAAQ,EACP,2BAA0BC,WAAY,WAAU,EAChD,2BAA0B+C,gBAAiB,WAAU,CACvD;QAED,MAAMjD,qBAAqB,CACzBC,QAAQ,EACRe,OAAO,EACN,OAAMgC,eAAgB,EAAC,CACzB;QACD,MAAMhD,qBAAqB,CAACC,QAAQ,EAAEe,OAAO,EAAEgC,eAAe,CAAC;QAC/D,MAAMhD,qBAAqB,CAACC,QAAQ,EAAEe,OAAO,EAAEiC,gBAAgB,CAAC;MAClE;IACF;IAEA,IAAIO,QAAQ,GAAGN,gBAAgB;IAE/B,IAAI3B,gBAAgB,CAACtB,QAAQ,EAAE+C,eAAe,CAAC,EAAE;MAC/C,IAAIK,OAAO,EAAE;QACXG,QAAQ,GAAGtD,WAAW;MACxB;MAEA,MAAMY,UAAU,CAACb,QAAQ,EAAE+C,eAAe,EAAEQ,QAAQ,CAAC;IACvD,CAAC,MAAM,IAAIjC,gBAAgB,CAACtB,QAAQ,EAAE+C,eAAe,CAACpC,WAAW,EAAE,CAAC,EAAE;MACpE,MAAME,UAAU,CACdb,QAAQ,EACR+C,eAAe,CAACpC,WAAW,EAAE,EAC7B4C,QAAQ,CAAC5C,WAAW,EAAE,CACvB;IACH;IACA,IAAI;MACF,MAAMuB,yBAAyB,CAAClC,QAAQ,EAAEiD,gBAAgB,CAAC;IAC7D,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd,MAAM,KAAIC,oBAAQ,EAAC,6CAA6C,CAAC;IACnE;IAEA,MAAM5B,eAAe,CAAC7B,QAAQ,CAAC;EACjC;AACF;AAEO,eAAe0D,2BAA2B,CAAC;EAChDzD,WAAW;EACX8C,eAAe;EACfC,gBAAgB,GAAGzD,yBAAyB;EAC5CoE,YAAY,GAAG1D,WAAW;EAC1BR;AACiB,CAAC,EAAE;EACpBU,kBAAM,CAACC,KAAK,CAAE,YAAW2C,eAAgB,QAAO9C,WAAY,cAAa,CAAC;EAE1E,IAAIR,WAAW,EAAE;IACf,IAAI;MACF,MAAMqD,iCAAiC,CAAC;QACtC7C,WAAW;QACX8C,eAAe;QACfC,gBAAgB;QAChBvD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACd,MAAM,KAAIC,oBAAQ,EAAED,KAAK,CAAWI,OAAO,CAAC;IAC9C;EACF,CAAC,MAAM;IACL,KAAK,MAAM5D,QAAQ,IAAI,IAAAkD,aAAI,EAACV,OAAO,CAACC,GAAG,EAAE,CAAC,CAACU,OAAO,EAAE,EAAE;MACpD,IAAI1B,gBAAgB,CAACzB,QAAQ,CAAC,EAAE;QAC9B;MACF;MACA,IAAI,CAAC,CAAC,MAAMM,kBAAE,CAAC+C,IAAI,CAACrD,QAAQ,CAAC,EAAEsD,WAAW,EAAE,EAAE;QAC5C,MAAMvD,qBAAqB,CAACC,QAAQ,EAAEC,WAAW,EAAE8C,eAAe,CAAC;QACnE,MAAMhD,qBAAqB,CAACC,QAAQ,EAAE2D,YAAY,EAAEX,gBAAgB,CAAC;MACvE;MACA,IAAI1B,gBAAgB,CAACtB,QAAQ,EAAE+C,eAAe,CAAC,EAAE;QAC/C,MAAMlC,UAAU,CAACb,QAAQ,EAAE+C,eAAe,EAAE9C,WAAW,CAAC;MAC1D,CAAC,MAAM,IAAIqB,gBAAgB,CAACtB,QAAQ,EAAE+C,eAAe,CAACpC,WAAW,EAAE,CAAC,EAAE;QACpE,MAAME,UAAU,CACdb,QAAQ,EACR+C,eAAe,CAACpC,WAAW,EAAE,EAC7BV,WAAW,CAACU,WAAW,EAAE,CAC1B;MACH;MAEA,MAAMkB,eAAe,CAAC7B,QAAQ,CAAC;IACjC;EACF;AACF"}