{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5329", "endColumns": "159", "endOffsets": "5484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,320,424,546,627,692,787,868,931,1020,1089,1152,1226,1290,1346,1464,1522,1584,1640,1720,1859,1948,2030,2141,2222,2302,2392,2459,2525,2604,2686,2774,2848,2925,2995,3074,3158,3242,3334,3434,3508,3589,3691,3744,3811,3904,3993,4055,4119,4182,4295,4388,4492,4586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "228,315,419,541,622,687,782,863,926,1015,1084,1147,1221,1285,1341,1459,1517,1579,1635,1715,1854,1943,2025,2136,2217,2297,2387,2454,2520,2599,2681,2769,2843,2920,2990,3069,3153,3237,3329,3429,3503,3584,3686,3739,3806,3899,3988,4050,4114,4177,4290,4383,4487,4581,4664"}, "to": {"startLines": "2,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3084,3903,4007,4129,6630,6695,6865,7099,7231,7320,7389,7452,7526,7590,7646,7764,7822,7884,7940,8020,8389,8478,8560,8671,8752,8832,8922,8989,9055,9134,9216,9304,9378,9455,9525,9604,9688,9772,9864,9964,10038,10119,10221,10274,10341,10434,10523,10585,10649,10712,10825,10918,11022,11116", "endLines": "5,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "278,3166,4002,4124,4205,6690,6785,6941,7157,7315,7384,7447,7521,7585,7641,7759,7817,7879,7935,8015,8154,8473,8555,8666,8747,8827,8917,8984,9050,9129,9211,9299,9373,9450,9520,9599,9683,9767,9859,9959,10033,10114,10216,10269,10336,10429,10518,10580,10644,10707,10820,10913,11017,11111,11194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "35,36,37,38,39,40,41,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3270,3372,3472,3570,3677,3783,12271", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3265,3367,3467,3565,3672,3778,3898,12367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4293,4401,4564,4695,4803,4964,5097,5219,5489,5681,5790,5955,6087,6252,6409,6476,6545", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4396,4559,4690,4798,4959,5092,5214,5324,5676,5785,5950,6082,6247,6404,6471,6540,6625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,385,498,606,691,792,920,1006,1087,1179,1273,1370,1464,1564,1658,1754,1850,1942,2034,2116,2223,2334,2433,2541,2649,2756,2915,11544", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "380,493,601,686,787,915,1001,1082,1174,1268,1365,1459,1559,1653,1749,1845,1937,2029,2111,2218,2329,2428,2536,2644,2751,2910,3009,11622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,283,353,436,505,572,652,735,822,917,989,1080,1164,1240,1323,1405,1480,1559,1634,1724,1797,1880,1956", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,203,278,348,431,500,567,647,730,817,912,984,1075,1159,1235,1318,1400,1475,1554,1629,1719,1792,1875,1951,2038"}, "to": {"startLines": "33,45,66,68,69,71,84,85,86,121,122,123,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,4210,6790,6946,7016,7162,8159,8226,8306,11199,11286,11381,11453,11627,11711,11787,11870,11952,12027,12106,12181,12372,12445,12528,12604", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3079,4288,6860,7011,7094,7226,8221,8301,8384,11281,11376,11448,11539,11706,11782,11865,11947,12022,12101,12176,12266,12440,12523,12599,12686"}}]}]}