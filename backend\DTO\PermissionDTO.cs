// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.DTO
namespace AxiaLivraisonAPI.DTO
{
    // Classe publique qui définit un objetpublic class PermissionDTO    public class PermissionDTO
    {

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(255)]
        public string PermissionName { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }
        public int Id { get; internal set; }
    }
}