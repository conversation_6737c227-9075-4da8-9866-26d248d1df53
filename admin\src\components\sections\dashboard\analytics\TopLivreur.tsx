﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport LinearProgress fromimport LinearProgress from '@mui/material/LinearProgress';

// Définir une interface TypeScriptinterface TopLivreur
interface TopLivreur {
  livreurId: number;
  nomLivreur: string;
  nombreCommandesLivrees: number;
  nombreCommandesTotal: number;
  pourcentageLivraison: number;
  montantTotal: number; // Ajout du montant total
}

// Créer une constanteconst TopLivreur =
const TopLivreur = () => {
  const [topLivreur, setTopLivreur] = useState<TopLivreur | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchTopLivreur =    const fetchTopLivreur = async () => {
      try {
        setLoading(true);
        // Créer une constanteconst response =        const response = await axios.get<TopLivreur>(`${API_URL}/api/statistiques/top-livreur`);
        setTopLivreur(response.data);
      } catch (err) {
        console.error('Erreur lors de la récupération du top livreur:', err);
        setTopLivreur(null);
      } finally {
        setLoading(false);
      }
    };

    fetchTopLivreur();
  }, []);

  // Formater le montant en dinar tunisien
  // Créer une constanteconst formatDinar =  const formatDinar = (montant: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 3,
    }).format(montant);
  };

  return (
    <Stack component={Paper} p={2.5} alignItems="center" spacing={2.25} height={100}>
      <Stack
        alignItems="center"
        justifyContent="center"
        height={56}
        width={72}
        sx={(theme) => ({
          background: `linear-gradient(90deg, ${theme.palette.gradients.secondary.main} 0%, ${theme.palette.gradients.secondary.state} 100%)`,
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
        })}
        borderRadius="50%"
      >
        <IconifyIcon icon="mdi:trophy-award" fontSize="h3.fontSize" color="info.lighter" />
      </Stack>
      <div style={{ width: '100%' }}>
        <Typography variant="body2" color="text.disabled" noWrap>
          Top Livreur
        </Typography>
        {loading ? (
          <Typography mt={0.25} variant="h3">
            ...
          </Typography>
        ) : (
          <>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography mt={0.25} variant="h3" noWrap sx={{ maxWidth: '60%' }}>
                {topLivreur?.nomLivreur || 'Aucun'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {topLivreur?.pourcentageLivraison || 0}%
              </Typography>
            </Stack>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mt={0.5}>
              <Typography variant="caption" color="text.secondary">
                {topLivreur?.nombreCommandesLivrees || 0}/{topLivreur?.nombreCommandesTotal || 0}{' '}
                livraisons
              </Typography>
              {topLivreur?.montantTotal && (
                <Typography variant="caption" fontWeight="bold" color="primary.main">
                  {formatDinar(topLivreur.montantTotal)}
                </Typography>
              )}
            </Stack>
            <LinearProgress
              variant="determinate"
              value={topLivreur?.pourcentageLivraison || 0}
              sx={{
                mt: 0.5,
                height: 6,
                borderRadius: 1,
                backgroundColor: 'action.hover',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'success.main',
                },
              }}
            />
          </>
        )}
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default TopLivreur;
