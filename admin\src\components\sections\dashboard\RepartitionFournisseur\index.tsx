// Importer des fonctionnalités depuis un autre fichierimport React, { useState, useEffect, useRef } fromimport React, { useState, useEffect, useRef } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useTheme } fromimport { useTheme } from '@mui/material';
import {
  Stack,
  Paper,
  MenuItem,
  Typography,
  FormControl,
  Select,
  SelectChangeEvent,
  SxProps,
} from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport EChartsReactCore fromimport EChartsReactCore from 'echarts-for-react/lib/core';
// Importer des fonctionnalités depuis un autre fichierimport DistributionChart fromimport DistributionChart from './RepartitionFournisseur';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';

// Créer une constanteconst API_URL =
const API_URL = 'http://localhost:5283';

// Définir une interface TypeScriptinterface SupplierApiData
interface SupplierApiData {
  fournisseurId: number; // minuscule maintenant
  nomFournisseur: string; // minuscule
  nombreTotalCommandes: number; // minuscule
  montantTotal: number; // minuscule
}

// Définir une interface TypeScriptinterface SupplierData
interface SupplierData {
  id: number;
  value: number;
  name: string;
  visible: boolean;
  amount: number;
}

// Définir une interface TypeScriptinterface SupplierDistributionChartProps
interface SupplierDistributionChartProps {
  sx?: SxProps;
}

// Créer une constanteconst RepartitionFournisseur =
const RepartitionFournisseur = ({ sx }: SupplierDistributionChartProps) => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'all'>('month');
  const [chartData, setChartData] = useState<SupplierData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Créer une constanteconst chartRef =  const chartRef = useRef<EChartsReactCore>(null);
  // Créer une constanteconst theme =  const theme = useTheme();

  useEffect(() => {
    fetchSupplierData();
  }, [timeRange]);

  // Créer une constanteconst fetchSupplierData =
  const fetchSupplierData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Déterminer l'endpoint en fonction de la période sélectionnée
      let endpoint = `${API_URL}/api/statistiques/commandes-par-fournisseur`;

      // Ajouter le paramètre de période si nécessaire
      if (timeRange !== 'all') {
        endpoint += `?periode=${timeRange}`;
      }

      // Créer une constanteconst response =
      const response = await axios.get<SupplierApiData[]>(endpoint);

      // Créer une constanteconst processedData =
      const processedData = (response.data || [])
        .map((item, index) => ({
          id: item?.fournisseurId || index + 1,
          value: item?.nombreTotalCommandes || 0,
          name: item?.nomFournisseur || `Fournisseur ${index + 1}`,
          visible: true,
          amount: item?.montantTotal || 0,
        }))
        .filter((item) => item.value > 0);

      setChartData(processedData);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Erreur de chargement des données');
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };
  // Créer une constanteconst handleSelectChange =  const handleSelectChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value as 'week' | 'month' | 'all');
  };

  // Removed the unused toggleVisibility function

  // Créer une constanteconst getColorForId =
  const getColorForId = (id: number) => {
    // Palette de couleurs étendue et harmonieuse avec le design
    // Créer une constanteconst colors =    const colors = [
      theme.palette.primary.main, // Violet principal (#4318FF)
      theme.palette.secondary.main, // Bleu ciel (#04BEFE)
      theme.palette.success.main, // Vert (#05CD99)
      theme.palette.warning.main, // Jaune (#FFCE20)
      theme.palette.error.main, // Rouge (#EE5D50)
      theme.palette.primary.light, // Violet clair (#6946ff)
      theme.palette.secondary.light, // Bleu ciel clair (#6AD2FF)
      theme.palette.info.dark, // Bleu foncé
      '#FFA500', // Orange
      '#800080', // Violet foncé
      '#008080', // Turquoise
      '#FF6347', // Tomate
      '#4B0082', // Indigo
      '#2E8B57', // Vert mer
      '#9932CC', // Orchidée foncée
      '#1E90FF', // Bleu dodger
      '#FF1493', // Rose profond
      '#32CD32', // Vert lime
      '#FF8C00', // Orange foncé
      '#8A2BE2', // Bleu violet
    ];

    // Assurer que chaque fournisseur a une couleur unique
    return colors[(id - 1) % colors.length];
  };

  if (loading) {
    return (
      <Paper
        sx={{
          py: 2.5,
          height: 350,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography>Chargement...</Typography>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper
        sx={{
          py: 2.5,
          height: 350,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography color="error">{error}</Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ py: 2.5, height: 350, ...sx }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" px={2}>
        <Typography variant="body1" fontWeight={700}>
          Répartition par Fournisseur
        </Typography>

        <FormControl variant="filled" size="small" sx={{ minWidth: 120 }}>
          <Select value={timeRange} onChange={handleSelectChange}>
            <MenuItem value="week">Semaine</MenuItem>
            <MenuItem value="month">Mois</MenuItem>
            <MenuItem value="all">Tout</MenuItem>
          </Select>
        </FormControl>
      </Stack>

      <DistributionChart
        chartRef={chartRef}
        data={chartData}
        getColorForId={getColorForId}
        sx={{ height: '200px !important', mt: '40px' }}
      />
    </Paper>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default RepartitionFournisseur;
