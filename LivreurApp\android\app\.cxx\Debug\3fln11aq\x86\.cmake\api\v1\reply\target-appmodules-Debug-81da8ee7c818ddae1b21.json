{"artifacts": [{"path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/intermediates/cxx/Debug/3fln11aq/obj/x86/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 83, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 81, "parent": 10}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 11, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-camera-kit/android/build/generated/source/codegen/jni/react/renderer/components/rncamerakit_specs"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba"}, {"backtrace": 4, "id": "react_codegen_rncamerakit_specs::@d945e98661337998d651"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/RNCGeolocationSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/rncamerakit_specsJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/rncamerakit_specs-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/.cxx/Debug/3fln11aq/x86/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}