{"buildFiles": ["E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\2v2q4961\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"artifactName": "rnscreens", "abi": "arm64-v8a", "output": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\2v2q4961\\obj\\arm64-v8a\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}}}