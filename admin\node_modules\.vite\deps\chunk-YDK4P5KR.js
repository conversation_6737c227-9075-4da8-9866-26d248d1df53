import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-VLOFTIDF.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/ToggleButton/toggleButtonClasses.js
function getToggleButtonUtilityClass(slot) {
  return generateUtilityClass("MuiToggleButton", slot);
}
var toggleButtonClasses = generateUtilityClasses("MuiToggleButton", ["root", "disabled", "selected", "standard", "primary", "secondary", "sizeSmall", "sizeMedium", "sizeLarge", "fullWidth"]);
var toggleButtonClasses_default = toggleButtonClasses;

// node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroupContext.js
var React = __toESM(require_react());
var ToggleButtonGroupContext = React.createContext({});
if (true) {
  ToggleButtonGroupContext.displayName = "ToggleButtonGroupContext";
}
var ToggleButtonGroupContext_default = ToggleButtonGroupContext;

// node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroupButtonContext.js
var React2 = __toESM(require_react());
var ToggleButtonGroupButtonContext = React2.createContext(void 0);
if (true) {
  ToggleButtonGroupButtonContext.displayName = "ToggleButtonGroupButtonContext";
}
var ToggleButtonGroupButtonContext_default = ToggleButtonGroupButtonContext;

export {
  getToggleButtonUtilityClass,
  toggleButtonClasses_default,
  ToggleButtonGroupContext_default,
  ToggleButtonGroupButtonContext_default
};
//# sourceMappingURL=chunk-YDK4P5KR.js.map
