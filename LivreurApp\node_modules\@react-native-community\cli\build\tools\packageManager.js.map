{"version": 3, "names": ["packageManagers", "yarn", "init", "install", "installDev", "uninstall", "installAll", "npm", "configurePackageManager", "packageNames", "action", "options", "pm", "should<PERSON><PERSON><PERSON><PERSON><PERSON>", "executable", "flags", "args", "executeCommand", "command", "execa", "stdio", "silent", "logger", "isVerbose", "cwd", "root", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "getYarnVersionIfAvailable", "isProjectUsingYarn"], "sources": ["../../src/tools/packageManager.ts"], "sourcesContent": ["import execa from 'execa';\nimport {logger} from '@react-native-community/cli-tools';\nimport {getYarnVersionIfAvailable, isProjectUsingYarn} from './yarn';\n\ntype Options = {\n  preferYarn?: boolean;\n  silent?: boolean;\n  root: string;\n};\n\nconst packageManagers = {\n  yarn: {\n    init: ['init', '-y'],\n    install: ['add'],\n    installDev: ['add', '-D'],\n    uninstall: ['remove'],\n    installAll: ['install'],\n  },\n  npm: {\n    init: ['init', '-y'],\n    install: ['install', '--save', '--save-exact'],\n    installDev: ['install', '--save-dev', '--save-exact'],\n    uninstall: ['uninstall', '--save'],\n    installAll: ['install'],\n  },\n};\n\nfunction configurePackageManager(\n  packageNames: Array<string>,\n  action: 'init' | 'install' | 'installDev' | 'installAll' | 'uninstall',\n  options: Options,\n) {\n  const pm = shouldUseYarn(options) ? 'yarn' : 'npm';\n  const [executable, ...flags] = packageManagers[pm][action];\n  const args = [executable, ...flags, ...packageNames];\n  return executeCommand(pm, args, options);\n}\n\nfunction executeCommand(\n  command: string,\n  args: Array<string>,\n  options: Options,\n) {\n  return execa(command, args, {\n    stdio: options.silent && !logger.isVerbose() ? 'pipe' : 'inherit',\n    cwd: options.root,\n  });\n}\n\nfunction shouldUseYarn(options: Options) {\n  if (options && options.preferYarn !== undefined) {\n    return options.preferYarn && getYarnVersionIfAvailable();\n  }\n\n  return isProjectUsingYarn(options.root) && getYarnVersionIfAvailable();\n}\n\nexport function init(options: Options) {\n  return configurePackageManager([], 'init', options);\n}\n\nexport function install(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'install', options);\n}\n\nexport function installDev(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'installDev', options);\n}\n\nexport function uninstall(packageNames: Array<string>, options: Options) {\n  return configurePackageManager(packageNames, 'uninstall', options);\n}\n\nexport function installAll(options: Options) {\n  return configurePackageManager([], 'installAll', options);\n}\n"], "mappings": ";;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAqE;AAQrE,MAAMA,eAAe,GAAG;EACtBC,IAAI,EAAE;IACJC,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACpBC,OAAO,EAAE,CAAC,KAAK,CAAC;IAChBC,UAAU,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IACzBC,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrBC,UAAU,EAAE,CAAC,SAAS;EACxB,CAAC;EACDC,GAAG,EAAE;IACHL,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACpBC,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,cAAc,CAAC;IAC9CC,UAAU,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,cAAc,CAAC;IACrDC,SAAS,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCC,UAAU,EAAE,CAAC,SAAS;EACxB;AACF,CAAC;AAED,SAASE,uBAAuB,CAC9BC,YAA2B,EAC3BC,MAAsE,EACtEC,OAAgB,EAChB;EACA,MAAMC,EAAE,GAAGC,aAAa,CAACF,OAAO,CAAC,GAAG,MAAM,GAAG,KAAK;EAClD,MAAM,CAACG,UAAU,EAAE,GAAGC,KAAK,CAAC,GAAGf,eAAe,CAACY,EAAE,CAAC,CAACF,MAAM,CAAC;EAC1D,MAAMM,IAAI,GAAG,CAACF,UAAU,EAAE,GAAGC,KAAK,EAAE,GAAGN,YAAY,CAAC;EACpD,OAAOQ,cAAc,CAACL,EAAE,EAAEI,IAAI,EAAEL,OAAO,CAAC;AAC1C;AAEA,SAASM,cAAc,CACrBC,OAAe,EACfF,IAAmB,EACnBL,OAAgB,EAChB;EACA,OAAO,IAAAQ,gBAAK,EAACD,OAAO,EAAEF,IAAI,EAAE;IAC1BI,KAAK,EAAET,OAAO,CAACU,MAAM,IAAI,CAACC,kBAAM,CAACC,SAAS,EAAE,GAAG,MAAM,GAAG,SAAS;IACjEC,GAAG,EAAEb,OAAO,CAACc;EACf,CAAC,CAAC;AACJ;AAEA,SAASZ,aAAa,CAACF,OAAgB,EAAE;EACvC,IAAIA,OAAO,IAAIA,OAAO,CAACe,UAAU,KAAKC,SAAS,EAAE;IAC/C,OAAOhB,OAAO,CAACe,UAAU,IAAI,IAAAE,+BAAyB,GAAE;EAC1D;EAEA,OAAO,IAAAC,wBAAkB,EAAClB,OAAO,CAACc,IAAI,CAAC,IAAI,IAAAG,+BAAyB,GAAE;AACxE;AAEO,SAAS1B,IAAI,CAACS,OAAgB,EAAE;EACrC,OAAOH,uBAAuB,CAAC,EAAE,EAAE,MAAM,EAAEG,OAAO,CAAC;AACrD;AAEO,SAASR,OAAO,CAACM,YAA2B,EAAEE,OAAgB,EAAE;EACrE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,SAAS,EAAEE,OAAO,CAAC;AAClE;AAEO,SAASP,UAAU,CAACK,YAA2B,EAAEE,OAAgB,EAAE;EACxE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,YAAY,EAAEE,OAAO,CAAC;AACrE;AAEO,SAASN,SAAS,CAACI,YAA2B,EAAEE,OAAgB,EAAE;EACvE,OAAOH,uBAAuB,CAACC,YAAY,EAAE,WAAW,EAAEE,OAAO,CAAC;AACpE;AAEO,SAASL,UAAU,CAACK,OAAgB,EAAE;EAC3C,OAAOH,uBAAuB,CAAC,EAAE,EAAE,YAAY,EAAEG,OAAO,CAAC;AAC3D"}