import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { colors } from '../theme';

// Get screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive helper functions
const wp = (percentage) => {
  return (percentage * screenWidth) / 100;
};

const hp = (percentage) => {
  return (percentage * screenHeight) / 100;
};

// Device type detection
const isTablet = screenWidth >= 768;
const isSmallDevice = screenWidth < 350;

const StatusUpdateForm = ({
  visible,
  currentStatus,
  onClose,
  onSave,
  loading,
}) => {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);

  // Synchronise le statut quand le currentStatus change
  useEffect(() => {
    setSelectedStatus(currentStatus);
  }, [currentStatus]);

  const statusOptions = [
    {label: 'En préparation', value: 'en préparation'},
    {label: 'En transit', value: 'en transit'},
    {label: 'Livré', value: 'livré'},
  ];



  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Modifier le statut</Text>
            <TouchableOpacity onPress={onClose}>
              <AntDesign name="close" size={24} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>

          {statusOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                selectedStatus === option.value && styles.selectedOptionButton,
              ]}
              onPress={() => setSelectedStatus(option.value)}>
              <View
                style={[
                  styles.radioButton,
                  selectedStatus === option.value && styles.selectedRadioButton,
                ]}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedStatus === option.value && styles.selectedOptionText,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
              disabled={loading}>
              <Text style={styles.cancelText}>Annuler</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.saveButton,
                (!selectedStatus || loading) && {backgroundColor: '#94A3B8'},
              ]}
              onPress={() => onSave(selectedStatus)}
              disabled={!selectedStatus || loading}>
              {loading ? (
                <ActivityIndicator color="#FFF" />
              ) : (
                <Text style={styles.saveText}>Enregistrer</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: isTablet ? wp(10) : wp(5),
  },
  modalContent: {
    width: isTablet ? wp(60) : wp(85),
    maxWidth: isTablet ? 500 : 350,
    backgroundColor: colors.neutral.surface,
    borderRadius: isTablet ? wp(2.5) : wp(3),
    padding: isTablet ? wp(4) : wp(5),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: isTablet ? wp(3) : wp(4),
  },
  modalTitle: {
    fontSize: isTablet ? wp(3.5) : wp(4.5),
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: isTablet ? wp(2.5) : wp(3),
    marginBottom: isTablet ? wp(1.5) : wp(2),
    borderRadius: isTablet ? wp(1.5) : wp(2),
    borderWidth: 1,
    borderColor: colors.neutral.border,
    backgroundColor: colors.neutral.surface,
  },
  selectedOptionButton: {
    borderColor: colors.primary.main,
    backgroundColor: colors.primary.lighter,
  },
  radioButton: {
    width: isTablet ? wp(3.5) : wp(4.5),
    height: isTablet ? wp(3.5) : wp(4.5),
    borderRadius: isTablet ? wp(1.75) : wp(2.25),
    borderWidth: 1,
    borderColor: colors.neutral.border,
    marginRight: isTablet ? wp(2.5) : wp(3),
    backgroundColor: colors.neutral.surface,
  },
  selectedRadioButton: {
    backgroundColor: colors.primary.main,
    borderColor: colors.primary.main,
  },
  optionText: {
    fontSize: isTablet ? wp(2.8) : wp(3.8),
    color: colors.text.primary,
  },
  selectedOptionText: {
    color: colors.primary.dark,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: isTablet ? wp(3) : wp(4),
  },
  cancelButton: {
    flex: 1,
    padding: isTablet ? wp(2.5) : wp(3),
    marginRight: isTablet ? wp(2) : wp(2.5),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary.main,
    borderRadius: isTablet ? wp(1.5) : wp(2),
    backgroundColor: colors.neutral.surface,
  },
  saveButton: {
    flex: 1,
    padding: isTablet ? wp(2.5) : wp(3),
    backgroundColor: colors.primary.main,
    borderRadius: isTablet ? wp(1.5) : wp(2),
    alignItems: 'center',
  },
  cancelText: {
    color: colors.primary.main,
    fontWeight: '600',
    fontSize: isTablet ? wp(2.8) : wp(3.5),
  },
  saveText: {
    color: colors.text.inverse,
    fontWeight: '600',
    fontSize: isTablet ? wp(2.8) : wp(3.5),
  },
});

export default StatusUpdateForm;