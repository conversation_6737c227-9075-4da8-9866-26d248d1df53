"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.iosBuildOptions = exports.default = void 0;
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _chalk() {
  const data = _interopRequireDefault(require("chalk"));
  _chalk = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var _buildProject = require("./buildProject");
var _getDestinationSimulator = require("../../tools/getDestinationSimulator");
var _selectFromInteractiveMode = require("../../tools/selectFromInteractiveMode");
var _getProjectInfo = require("../../tools/getProjectInfo");
var _checkIfConfigurationExists = require("../../tools/checkIfConfigurationExists");
var _getConfigurationScheme = require("../../tools/getConfigurationScheme");
var _listIOSDevices = _interopRequireDefault(require("../../tools/listIOSDevices"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

async function buildIOS(_, ctx, args) {
  if (!ctx.project.ios) {
    throw new (_cliTools().CLIError)('iOS project folder not found. Are you sure this is a React Native project?');
  }
  const {
    xcodeProject,
    sourceDir
  } = ctx.project.ios;
  if (!xcodeProject) {
    throw new (_cliTools().CLIError)(`Could not find Xcode project files in "${sourceDir}" folder`);
  }
  process.chdir(sourceDir);
  if (args.configuration) {
    _cliTools().logger.warn('--configuration has been deprecated. Use --mode instead.');
    _cliTools().logger.warn('Parameters were automatically reassigned to --mode on this run.');
    args.mode = args.configuration;
  }
  const projectInfo = (0, _getProjectInfo.getProjectInfo)();
  if (args.mode) {
    (0, _checkIfConfigurationExists.checkIfConfigurationExists)(projectInfo, args.mode);
  }
  const inferredSchemeName = _path().default.basename(xcodeProject.name, _path().default.extname(xcodeProject.name));
  let scheme = args.scheme || inferredSchemeName;
  let mode = args.mode;
  if (args.interactive) {
    const selection = await (0, _selectFromInteractiveMode.selectFromInteractiveMode)({
      scheme,
      mode
    });
    if (selection.scheme) {
      scheme = selection.scheme;
    }
    if (selection.mode) {
      mode = selection.mode;
    }
  }
  const modifiedArgs = {
    ...args,
    scheme,
    mode
  };
  args.mode = (0, _getConfigurationScheme.getConfigurationScheme)({
    scheme: args.scheme,
    mode: args.mode
  }, sourceDir);
  _cliTools().logger.info(`Found Xcode ${xcodeProject.isWorkspace ? 'workspace' : 'project'} "${_chalk().default.bold(xcodeProject.name)}"`);
  const extendedArgs = {
    ...modifiedArgs,
    packager: false
  };

  // // No need to load all available devices
  if (!args.device && !args.udid) {
    if (!args.simulator) {
      return (0, _buildProject.buildProject)(xcodeProject, undefined, scheme, extendedArgs);
    }

    /**
     * If provided simulator does not exist, try simulators in following order
     * - iPhone 14
     * - iPhone 13
     * - iPhone 12
     * - iPhone 11
     */
    const fallbackSimulators = ['iPhone 14', 'iPhone 13', 'iPhone 12', 'iPhone 11'];
    const selectedSimulator = (0, _getDestinationSimulator.getDestinationSimulator)(args, fallbackSimulators);
    return (0, _buildProject.buildProject)(xcodeProject, selectedSimulator.udid, scheme, extendedArgs);
  }
  if (args.device && args.udid) {
    return _cliTools().logger.error('The `device` and `udid` options are mutually exclusive.');
  }
  const devices = await (0, _listIOSDevices.default)();
  if (args.udid) {
    const device = devices.find(d => d.udid === args.udid);
    if (!device) {
      return _cliTools().logger.error(`Could not find a device with udid: "${_chalk().default.bold(args.udid)}". ${printFoundDevices(devices)}`);
    }
    return (0, _buildProject.buildProject)(xcodeProject, device.udid, scheme, extendedArgs);
  } else {
    const physicalDevices = devices.filter(d => d.type !== 'simulator');
    const device = matchingDevice(physicalDevices, args.device);
    if (device) {
      return (0, _buildProject.buildProject)(xcodeProject, device.udid, scheme, extendedArgs);
    }
  }
}
function matchingDevice(devices, deviceName) {
  if (deviceName === true) {
    const firstIOSDevice = devices.find(d => d.type === 'device');
    if (firstIOSDevice) {
      _cliTools().logger.info(`Using first available device named "${_chalk().default.bold(firstIOSDevice.name)}" due to lack of name supplied.`);
      return firstIOSDevice;
    } else {
      _cliTools().logger.error('No iOS devices connected.');
      return undefined;
    }
  }
  const deviceByName = devices.find(device => device.name === deviceName || formattedDeviceName(device) === deviceName);
  if (!deviceByName) {
    _cliTools().logger.error(`Could not find a device named: "${_chalk().default.bold(String(deviceName))}". ${printFoundDevices(devices)}`);
  }
  return deviceByName;
}
function formattedDeviceName(simulator) {
  return simulator.version ? `${simulator.name} (${simulator.version})` : simulator.name;
}
function printFoundDevices(devices) {
  return ['Available devices:', ...devices.map(device => `  - ${device.name} (${device.udid})`)].join('\n');
}
const iosBuildOptions = [{
  name: '--simulator <string>',
  description: 'Explicitly set simulator to use. Optionally include iOS version between ' + 'parenthesis at the end to match an exact version: "iPhone 6 (10.0)"'
}, {
  name: '--mode <string>',
  description: 'Explicitly set the scheme configuration to use. This option is case sensitive.'
}, {
  name: '--configuration <string>',
  description: '[Deprecated] Explicitly set the scheme configuration to use'
}, {
  name: '--scheme <string>',
  description: 'Explicitly set Xcode scheme to use'
}, {
  name: '--device [string]',
  description: 'Explicitly set device to use by name.  The value is not required if you have a single device connected.'
}, {
  name: '--destination <string>',
  description: 'Explicitly extend distination e.g. "arch=x86_64"'
}, {
  name: '--udid <string>',
  description: 'Explicitly set device to use by udid'
}, {
  name: '--verbose',
  description: 'Do not use xcbeautify or xcpretty even if installed'
}, {
  name: '--port <number>',
  default: process.env.RCT_METRO_PORT || 8081,
  parse: Number
}, {
  name: '--terminal <string>',
  description: 'Launches the Metro Bundler in a new window using the specified terminal path.',
  default: (0, _cliTools().getDefaultUserTerminal)()
}, {
  name: '--xcconfig [string]',
  description: 'Explicitly set xcconfig to use'
}, {
  name: '--buildFolder <string>',
  description: 'Location for iOS build artifacts. Corresponds to Xcode\'s "-derivedDataPath".'
}, {
  name: '--extra-params <string>',
  description: 'Custom params that will be passed to xcodebuild command.',
  parse: val => val.split(' ')
}];
exports.iosBuildOptions = iosBuildOptions;
var _default = {
  name: 'build-ios',
  description: 'builds your app on iOS simulator',
  func: buildIOS,
  examples: [{
    desc: 'Build the app for the IOS simulator',
    cmd: 'react-native build-ios'
  }, {
    desc: 'Build the app for all IOS devices',
    cmd: 'react-native build-ios --mode "Release"'
  }, {
    desc: 'Build the app for a specific IOS device',
    cmd: 'react-native build-ios --simulator "IPhone 11"'
  }],
  options: [...iosBuildOptions, {
    name: '--interactive',
    description: 'Explicitly select which scheme and configuration to use before running a build'
  }]
};
exports.default = _default;

//# sourceMappingURL=index.ts.map