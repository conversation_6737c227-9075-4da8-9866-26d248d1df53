// Importer des fonctionnalités depuis un autre fichierimport Grid fromimport Grid from '@mui/material/Grid';
// Importer des fonctionnalités depuis un autre fichierimport LivreurActif fromimport LivreurActif from './LivreurActif';
// Importer des fonctionnalités depuis un autre fichierimport LivreurDisponible fromimport LivreurDisponible from './LivreurDisponible';
// Importer des fonctionnalités depuis un autre fichierimport Sales fromimport Sales from './TopFournisseur';
// Importer des fonctionnalités depuis un autre fichierimport Revenu fromimport Revenu from './Revenu';
// Importer des fonctionnalités depuis un autre fichierimport Tasks fromimport Tasks from './CommandeParJour';
// Importer des fonctionnalités depuis un autre fichierimport TopLivreur fromimport TopLivreur from './TopLivreur';

// Créer une constanteconst Analytics =
const Analytics = () => {
  return (
    <Grid container spacing={2.5}>
      <Grid item xs={12} sm={6} md={4} xl={2}>
        <LivreurActif />
      </Grid>

      <Grid item xs={12} sm={6} md={4} xl={2}>
        <Revenu />
      </Grid>

      <Grid item xs={12} sm={6} md={4} xl={2}>
        <Sales />
      </Grid>
      <Grid item xs={12} sm={6} md={4} xl={2}>
        <LivreurDisponible />
      </Grid>
      <Grid item xs={12} sm={6} md={4} xl={2}>
        <Tasks />
      </Grid>

      <Grid item xs={12} sm={6} md={4} xl={2}>
        <TopLivreur />
      </Grid>
    </Grid>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Analytics;
