﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Interface pour les livreurs actifs
// Définir une interface TypeScriptinterface LivreurActifinterface LivreurActif {
  id: number;
  nom: string;
}

// Créer une constanteconst LivreurActif =
const LivreurActif = () => {
  const [livreursActifs, setLivreursActifs] = useState(0);
  const [loading, setLoading] = useState(true);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchLivreursActifs =    const fetchLivreursActifs = async () => {
      try {
        setLoading(true);
        // Utiliser l'endpoint qui compte les livreurs uniques avec commandes en transit
        // Créer une constanteconst response =        const response = await axios.get<LivreurActif[]>(
          `${API_URL}/api/statistiques/livreurs-actifs`,
        );

        // Définir le nombre de livreurs actifs (nombre d'éléments dans le tableau)
        setLivreursActifs(response.data.length);
      } catch (err) {
        console.error('Erreur lors de la récupération des livreurs actifs:', err);
        setLivreursActifs(0);
      } finally {
        setLoading(false);
      }
    };

    fetchLivreursActifs();
  }, []);

  return (
    <Stack component={Paper} p={2.5} alignItems="center" spacing={2.25} height={100}>
      <Stack
        alignItems="center"
        justifyContent="center"
        height={56}
        width={56}
        bgcolor="info.main"
        borderRadius="50%"
      >
        <IconifyIcon icon="mdi:account-clock" fontSize="h2.fontSize" color="primary.main" />
      </Stack>
      <div>
        <Typography variant="body2" color="text.disabled">
          Livreurs Actifs
        </Typography>
        <Typography mt={0.25} variant="h3">
          {loading ? '...' : livreursActifs}
        </Typography>
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default LivreurActif;
