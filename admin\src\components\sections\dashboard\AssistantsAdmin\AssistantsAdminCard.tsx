// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Avatar fromimport Avatar from '@mui/material/Avatar';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport CardMenu fromimport CardMenu from './CardMenu';
// Importer des fonctionnalités depuis un autre fichierimport { Utilisateur } fromimport { Utilisateur } from 'pages/utilisateur/types';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

// Définir une interface TypeScriptinterface MemberCardProps
interface MemberCardProps {
  data: Utilisateur;
  onEdit: (assistant: Utilisateur) => void;
  onDelete: (id: number) => void;
  sx?: SxProps;
}

// Créer une constanteconst MemberCard =
const MemberCard = ({ data, onEdit, onDelete, sx }: MemberCardProps) => {
  return (
    <Box
      sx={{
        p: 2.5,
        mb: 2.5,
        borderRadius: 2,
        bgcolor: 'background.paper',
        // Suppression de la bordure
        // border: '1px solid',
        // borderColor: 'divider',
        ...sx,
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar
            src={data.imagePath ? `http://localhost:5283${data.imagePath}` : undefined}
            sx={{
              height: 52,
              width: 52,
              bgcolor: 'primary.main',
            }}
          >
            {!data.imagePath && data.nom.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight={600}>
              {data.nom}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.identifiant}
            </Typography>
          </Box>
        </Stack>
        <CardMenu onEdit={() => onEdit(data)} onDelete={() => onDelete(data.id)} />
      </Stack>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default MemberCard;
