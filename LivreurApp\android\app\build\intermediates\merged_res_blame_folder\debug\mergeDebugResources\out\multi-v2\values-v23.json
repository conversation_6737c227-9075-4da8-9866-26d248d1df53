{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,8,13,17,20,23,26,31,34,38", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,481,801,1038,1245,1452,1655,1987,2189,2454", "endLines": "4,7,12,16,19,22,25,30,33,37,41", "endColumns": "10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,476,796,1033,1240,1447,1650,1982,2184,2449,2722"}, "to": {"startLines": "53,56,59,64,68,71,74,77,82,85,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3382,3598,3808,4128,4365,4572,4779,4982,5314,5516,5781", "endLines": "55,58,63,67,70,73,76,81,84,88,92", "endColumns": "10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "3593,3803,4123,4360,4567,4774,4977,5309,5511,5776,6049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1dcfea128d5541f1d62ec1ae8c25cadf\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3232", "endLines": "52", "endColumns": "12", "endOffsets": "3377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}}]}]}