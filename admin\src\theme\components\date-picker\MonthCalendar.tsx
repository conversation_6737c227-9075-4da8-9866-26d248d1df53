// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const MonthCalendar: Components<Omit<Theme, 'components'>>['MuiMonthCalendar'] = {
  styleOverrides: {
    root: ({ theme }) => ({
      '& .MuiPickersMonth-root': {
        '& .MuiPickersMonth-monthButton': {
          '&.Mui-selected': {
            backgroundColor: theme.palette.primary.main,
          },
        },
      },
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default MonthCalendar;
