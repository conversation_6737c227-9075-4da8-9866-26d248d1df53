{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "34,35,36,37,38,39,40,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2959,3052,3152,3249,3348,3444,3546,9671", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3047,3147,3244,3343,3439,3541,3641,9767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "266,364,462,568,654,757,874,952,1028,1119,1212,1304,1398,1498,1591,1686,1779,1870,1961,2041,2141,2241,2337,2439,2539,2638,2788,9591", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "359,457,563,649,752,869,947,1023,1114,1207,1299,1393,1493,1586,1681,1774,1865,1956,2036,2136,2236,2332,2434,2534,2633,2783,2879,9666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3914,4016,4155,4277,4379,4506,4629,4737,4971,5099,5202,5347,5470,5605,5732,5792,5849", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4011,4150,4272,4374,4501,4624,4732,4834,5094,5197,5342,5465,5600,5727,5787,5844,5915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,291,380,482,559,623,708,770,828,913,975,1033,1099,1161,1216,1312,1369,1428,1484,1551,1656,1736,1817,1916,1989,2060,2142,2209,2275,2341,2414,2495,2563,2636,2707,2774,2859,2926,3013,3101,3175,3243,3328,3379,3443,3523,3605,3667,3731,3794,3889,3978,4063,4154", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "211,286,375,477,554,618,703,765,823,908,970,1028,1094,1156,1211,1307,1364,1423,1479,1546,1651,1731,1812,1911,1984,2055,2137,2204,2270,2336,2409,2490,2558,2631,2702,2769,2854,2921,3008,3096,3170,3238,3323,3374,3438,3518,3600,3662,3726,3789,3884,3973,4058,4149,4225"}, "to": {"startLines": "2,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2884,3646,3735,3837,5920,5984,6069,6131,6189,6274,6336,6394,6460,6522,6577,6673,6730,6789,6845,6912,7017,7097,7178,7277,7350,7421,7503,7570,7636,7702,7775,7856,7924,7997,8068,8135,8220,8287,8374,8462,8536,8604,8689,8740,8804,8884,8966,9028,9092,9155,9250,9339,9424,9515", "endLines": "5,33,41,42,43,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "261,2954,3730,3832,3909,5979,6064,6126,6184,6269,6331,6389,6455,6517,6572,6668,6725,6784,6840,6907,7012,7092,7173,7272,7345,7416,7498,7565,7631,7697,7770,7851,7919,7992,8063,8130,8215,8282,8369,8457,8531,8599,8684,8735,8799,8879,8961,9023,9087,9150,9245,9334,9419,9510,9586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4839", "endColumns": "131", "endOffsets": "4966"}}]}]}