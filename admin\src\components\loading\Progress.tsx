// Importer des fonctionnalités depuis un autre fichierimport { StackOwnProps } fromimport { StackOwnProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport CircularProgress fromimport CircularProgress from '@mui/material/CircularProgress';

// Créer une constanteconst Progress =
const Progress = (props: StackOwnProps) => {
  return (
    <Stack justifyContent="center" alignItems="center" height="100vh" {...props}>
      <CircularProgress />
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Progress;
