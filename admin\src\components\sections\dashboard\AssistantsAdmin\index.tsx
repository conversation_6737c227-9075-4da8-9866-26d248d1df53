// Importer des fonctionnalités depuis un autre fichierimport { useEffect, useState } fromimport { useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport ButtonBase fromimport ButtonBase from '@mui/material/ButtonBase';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport MemberCard fromimport MemberCard from './AssistantsAdminCard';
// Importer des fonctionnalités depuis un autre fichierimport Pagination fromimport Pagination from '@mui/material/Pagination';
// Importer des fonctionnalités depuis un autre fichierimport UtilisateurForm fromimport UtilisateurForm from 'pages/utilisateur/UtilisateurForm';
// Importer des fonctionnalités depuis un autre fichierimport { Utilisateur } fromimport { Utilisateur } from 'pages/utilisateur/types';
// Importer des fonctionnalités depuis un autre fichierimport Dialog fromimport Dialog from '@mui/material/Dialog';
// Importer des fonctionnalités depuis un autre fichierimport DialogActions fromimport DialogActions from '@mui/material/DialogActions';
// Importer des fonctionnalités depuis un autre fichierimport DialogContent fromimport DialogContent from '@mui/material/DialogContent';
// Importer des fonctionnalités depuis un autre fichierimport DialogContentText fromimport DialogContentText from '@mui/material/DialogContentText';
// Importer des fonctionnalités depuis un autre fichierimport DialogTitle fromimport DialogTitle from '@mui/material/DialogTitle';
// Importer des fonctionnalités depuis un autre fichierimport Button fromimport Button from '@mui/material/Button';
// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';

// Définir une interface TypeScriptinterface TeamMembersProps
interface TeamMembersProps {
  sx?: SxProps;
}

// Créer une constanteconst AssistantAdmin =
const AssistantAdmin = ({ sx }: TeamMembersProps) => {
  const [assistants, setAssistants] = useState<Utilisateur[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [openUserDialog, setOpenUserDialog] = useState(false);
  const [currentUtilisateur, setCurrentUtilisateur] = useState<Utilisateur | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<number | null>(null);
  // Créer une constanteconst itemsPerPage =  const itemsPerPage = 3;
  // Créer une constanteconst API_URL =  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    fetchAssistants();
  }, []);

  // Créer une constanteconst fetchAssistants =
  const fetchAssistants = async () => {
    try {
      setLoading(true);
      console.log('Récupération des assistants...');
      // Créer une constanteconst response =      const response = await axios.get(`${API_URL}/api/utilisateurs/liste`);
      console.log('Assistants récupérés:', response.data);
      setAssistants(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors de la récupération des assistants:', error);
      setLoading(false);
    }
  };

  // Créer une constanteconst handlePageChange =
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  // Créer une constanteconst handleAddUser =
  const handleAddUser = () => {
    console.log("Ouverture du formulaire d'ajout");
    setCurrentUtilisateur(null);
    setOpenUserDialog(true);
  };

  // Créer une constanteconst handleEditUser =
  const handleEditUser = (utilisateur: Utilisateur) => {
    console.log("Modification de l'utilisateur:", utilisateur);
    setCurrentUtilisateur(utilisateur);
    setOpenUserDialog(true);
  };

  // Créer une constanteconst handleDeleteUser =
  const handleDeleteUser = (id: number) => {
    console.log("Demande de suppression de l'utilisateur avec ID:", id);
    setUserToDelete(id);
    setDeleteDialogOpen(true);
  };

  // Créer une constanteconst confirmDelete =
  const confirmDelete = async () => {
    if (userToDelete) {
      try {
        console.log("Suppression de l'utilisateur avec ID:", userToDelete);

        // Utiliser l'URL correcte pour la suppression
        // Créer une constanteconst response =        const response = await axios.delete(
          `${API_URL}/api/utilisateurs/supprimer/${userToDelete}`,
        );
        console.log('Utilisateur supprimé avec succès:', response.data);

        // Rafraîchir la liste après la suppression
        await fetchAssistants();

        setDeleteDialogOpen(false);
        setUserToDelete(null);

        // Optionnel: Afficher un message de succès
        if (response.data?.message) {
          console.log('Message de succès:', response.data.message);
        }
      } catch (error) {
        console.error("Erreur lors de la suppression de l'utilisateur:", error);
        if (axios.isAxiosError(error)) {
          console.error("Détails de l'erreur:", error.response?.data);
          // Optionnel: Afficher l'erreur à l'utilisateur
          alert(error.response?.data?.message || "Erreur lors de la suppression de l'utilisateur");
        }
        setDeleteDialogOpen(false);
        setUserToDelete(null);
      }
    }
  };

  // Créer une constanteconst cancelDelete =
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  // Créer une constanteconst handleSaveUser =
  const handleSaveUser = async (userData: {
    nom: string;
    email: string;
    telephone: string;
    identifiant: string;
    motDePasse: string;
    imageFile?: File;
  }) => {
    try {
      console.log('Sauvegarde des données utilisateur:', userData);

      // Créer une constanteconst formData =
      const formData = new FormData();
      formData.append('Nom', userData.nom);
      formData.append('Email', userData.email);
      formData.append('Telephone', userData.telephone);
      formData.append('Identifiant', userData.identifiant);
      formData.append('MotDePasse', userData.motDePasse);

      if (userData.imageFile) {
        formData.append('ImageFile', userData.imageFile);
        console.log('Image ajoutée au formulaire');
      }

      if (currentUtilisateur) {
        console.log("Mise à jour de l'utilisateur avec ID:", currentUtilisateur.id);
        await axios.put(`${API_URL}/api/utilisateurs/modifier/${currentUtilisateur.id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        console.log('Utilisateur mis à jour avec succès');
      } else {
        console.log("Ajout d'un nouvel utilisateur");
        await axios.post(`${API_URL}/api/utilisateurs/ajouter`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        console.log('Nouvel utilisateur ajouté avec succès');
      }

      // Rafraîchir la liste après l'ajout/modification
      await fetchAssistants();
      setOpenUserDialog(false);
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de l'utilisateur:", error);
      if (axios.isAxiosError(error)) {
        console.error("Détails de l'erreur:", error.response?.data);
      }
    }
  };

  // Calculer les assistants à afficher sur la page actuelle
  // Créer une constanteconst displayedAssistants =  const displayedAssistants = assistants.slice((page - 1) * itemsPerPage, page * itemsPerPage);

  // Calculer le nombre de pages (minimum 1)
  // Créer une constanteconst pageCount =  const pageCount = Math.max(1, Math.ceil(assistants.length / itemsPerPage));

  return (
    <Box component={Paper} p={3} sx={{ height: 350, ...sx }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Typography variant="h5">Assistant-Admin</Typography>
        <ButtonBase
          sx={{
            height: 36,
            width: 36,
            bgcolor: 'primary.main', // Changé de 'info.main' à 'primary.main' pour avoir un bouton bleu
            borderRadius: 2.5,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)', // Ajout d'une légère ombre
            '&:hover': {
              bgcolor: 'primary.dark', // Effet hover plus foncé
            },
            transition: 'background-color 0.3s ease', // Transition douce
          }}
          onClick={handleAddUser}
        >
          <IconifyIcon icon="ic:round-add" color="white" fontSize="h4.fontSize" />
        </ButtonBase>
      </Stack>

      <Box mt={3} sx={{ display: 'flex', flexDirection: 'column', minHeight: '300px' }}>
        {loading ? (
          <Typography>Chargement...</Typography>
        ) : (
          <>
            <Box sx={{ flexGrow: 1, marginTop: 1 }}>
              {displayedAssistants.map((item) => (
                <MemberCard
                  key={item.id}
                  data={item}
                  onEdit={() => handleEditUser(item)}
                  onDelete={() => handleDeleteUser(item.id)}
                  sx={{ mt: -3 }}
                />
              ))}
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 'auto', py: 0 }}>
              <Pagination
                count={pageCount}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="small"
              />
            </Box>
          </>
        )}
      </Box>

      {/* Formulaire d'ajout/modification d'utilisateur */}
      <UtilisateurForm
        open={openUserDialog}
        onClose={() => setOpenUserDialog(false)}
        onSubmit={handleSaveUser}
        initialData={currentUtilisateur || undefined}
      />

      {/* Dialogue de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{'Confirmer la suppression'}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Êtes-vous sûr de vouloir supprimer cet assistant-admin ?
          </DialogContentText>
          <DialogContentText sx={{ mt: 2, color: 'warning.main' }}>
            Cette action est irréversible et entraînera la suppression de toutes les données
            associées à cet assistant-admin (commandes, permissions, rapports, etc.).
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={confirmDelete} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default AssistantAdmin;
