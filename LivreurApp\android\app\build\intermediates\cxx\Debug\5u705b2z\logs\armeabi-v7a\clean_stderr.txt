CMake Error at E:/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/source/codegen/jni/CMakeLists.txt:11 (add_library):
  add_library cannot create target "react_codegen_RNVectorIconsSpec" because
  another target with the same name already exists.  The existing target is
  created in source directory
  "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni".
  See documentation for policy CMP0002 for more details.


ninja: error: rebuilding 'build.ninja': subcommand failed
