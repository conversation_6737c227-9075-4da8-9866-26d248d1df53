// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const CardContent: Components<Omit<Theme, 'components'>>['MuiCardContent'] = {
  styleOverrides: {
    root: {
      padding: 0,
      '&:last-child': {
        paddingBottom: 0,
      },
    },
  },
};

// Exporter comme élément principal de ce fichierexport default
export default CardContent;
