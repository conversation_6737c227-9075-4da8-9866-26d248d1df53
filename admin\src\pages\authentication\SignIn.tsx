// Importer des fonctionnalités depuis un autre fichierimport { useState, ChangeEvent, FormEvent } fromimport { useState, ChangeEvent, FormEvent } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useNavigate } fromimport { useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Button fromimport Button from '@mui/material/Button';

// Importer des fonctionnalités depuis un autre fichierimport Typography from
import Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport TextField fromimport TextField from '@mui/material/TextField';

// Importer des fonctionnalités depuis un autre fichierimport IconButton from
import IconButton from '@mui/material/IconButton';
// Importer des fonctionnalités depuis un autre fichierimport InputAdornment fromimport InputAdornment from '@mui/material/InputAdornment';
// Importer des fonctionnalités depuis un autre fichierimport Visibility fromimport Visibility from '@mui/icons-material/Visibility';
// Importer des fonctionnalités depuis un autre fichierimport VisibilityOff fromimport VisibilityOff from '@mui/icons-material/VisibilityOff';
// Importer des fonctionnalités depuis un autre fichierimport { styled, keyframes } fromimport { styled, keyframes } from '@mui/material/styles';
// Importer des fonctionnalités depuis un autre fichierimport { Card, CardContent, Container, GlobalStyles } fromimport { Card, CardContent, Container, GlobalStyles } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport paths fromimport paths from 'routes/paths';
// Importer des fonctionnalités depuis un autre fichierimport sitemap, { MenuItem } fromimport sitemap, { MenuItem } from 'routes/sitemap';
// Importer des fonctionnalités depuis un autre fichierimport Dialog fromimport Dialog from '@mui/material/Dialog';
// Importer des fonctionnalités depuis un autre fichierimport DialogActions fromimport DialogActions from '@mui/material/DialogActions';
// Importer des fonctionnalités depuis un autre fichierimport DialogContent fromimport DialogContent from '@mui/material/DialogContent';
// Importer des fonctionnalités depuis un autre fichierimport DialogContentText fromimport DialogContentText from '@mui/material/DialogContentText';
// Importer des fonctionnalités depuis un autre fichierimport DialogTitle fromimport DialogTitle from '@mui/material/DialogTitle';
// Importer des fonctionnalités depuis un autre fichierimport CircularProgress fromimport CircularProgress from '@mui/material/CircularProgress';
// Importer des fonctionnalités depuis un autre fichierimport LockOutlinedIcon fromimport LockOutlinedIcon from '@mui/icons-material/LockOutlined';
// Importer des fonctionnalités depuis un autre fichierimport PersonOutlineIcon fromimport PersonOutlineIcon from '@mui/icons-material/PersonOutline';
// Import the logo
// Importer des fonctionnalités depuis un autre fichierimport logo fromimport logo from 'assets/images/logo.png';
// Importer des fonctionnalités depuis un autre fichierimport { authService } fromimport { authService } from 'services/authService';
// Importer des fonctionnalités depuis un autre fichierimport type { AxiosErrorResponse } fromimport type { AxiosErrorResponse } from 'types/api';

// Définir une interface TypeScriptinterface User
interface User {
  identifiant: string;
  password: string;
}

// Animations
// Créer une constanteconst float =const float = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
`;

// Créer une constanteconst shimmer =
const shimmer = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
`;

// Créer une constanteconst pulse =
const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
`;

// Créer une constanteconst slideIn =
const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

// Créer une constanteconst rotate =
const rotate = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

// Global styles for background effects
// Créer une constanteconst globalStyles =const globalStyles = (
  <GlobalStyles
    styles={{
      '@keyframes float': {
        '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
        '33%': { transform: 'translateY(-10px) rotate(1deg)' },
        '66%': { transform: 'translateY(5px) rotate(-1deg)' },
      },
      '@keyframes shimmer': {
        '0%': { transform: 'translateX(-100%)' },
        '100%': { transform: 'translateX(100%)' },
      },
      '@keyframes pulse': {
        '0%, 100%': { opacity: 1 },
        '50%': { opacity: 0.8 },
      },
      '@keyframes slideIn': {
        from: {
          opacity: 0,
          transform: 'translateY(30px) scale(0.95)',
        },
        to: {
          opacity: 1,
          transform: 'translateY(0) scale(1)',
        },
      },
      '@keyframes rotate': {
        from: { transform: 'rotate(0deg)' },
        to: { transform: 'rotate(360deg)' },
      },
    }}
  />
);

// Styled components
// Créer une constanteconst LoginContainer =const LoginContainer = styled(Box)(() => ({
  minHeight: '100vh',
  background: `
    linear-gradient(135deg,
      rgba(67, 24, 255, 0.1) 0%,
      rgba(4, 190, 254, 0.1) 25%,
      rgba(147, 51, 234, 0.1) 50%,
      rgba(16, 185, 129, 0.1) 75%,
      rgba(249, 115, 22, 0.1) 100%
    )`,
  position: 'relative',
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 80%, rgba(67, 24, 255, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(4, 190, 254, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 60% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)
    `,
    pointerEvents: 'none',
    zIndex: -2,
    animation: `${float} 8s ease-in-out infinite`,
  },
  '&::after': {
    content: '""',
    position: 'fixed',
    top: '-50%',
    left: '-50%',
    width: '200%',
    height: '200%',
    background: `
      conic-gradient(from 0deg at 50% 50%,
        transparent 0deg,
        rgba(67, 24, 255, 0.03) 60deg,
        transparent 120deg,
        rgba(4, 190, 254, 0.03) 180deg,
        transparent 240deg,
        rgba(147, 51, 234, 0.03) 300deg,
        transparent 360deg
      )
    `,
    pointerEvents: 'none',
    zIndex: -1,
    animation: `${rotate} 60s linear infinite`,
  },
}));

// Créer une constanteconst LoginCard =
const LoginCard = styled(Card)(() => ({
  maxWidth: 500,
  width: '100%',
  margin: '0 auto',
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.95),
      rgba(248, 250, 252, 0.9)
    )`,
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  borderRadius: '24px',
  boxShadow: `
    0 25px 50px rgba(0, 0, 0, 0.1),
    0 15px 35px rgba(67, 24, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6)
  `,
  position: 'relative',
  overflow: 'hidden',
  animation: `${slideIn} 0.8s ease-out`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `
      linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      )`,
    animation: `${shimmer} 3s ease-in-out infinite`,
    pointerEvents: 'none',
  },
}));

// Créer une constanteconst StyledTextField =
const StyledTextField = styled(TextField)(() => ({
  '& .MuiInputBase-root': {
    height: '56px',
    borderRadius: '16px',
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(10px)',
    border: '2px solid rgba(67, 24, 255, 0.1)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      border: '2px solid rgba(67, 24, 255, 0.2)',
      background: 'rgba(255, 255, 255, 0.9)',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 25px rgba(67, 24, 255, 0.15)',
    },
    '&.Mui-focused': {
      border: '2px solid rgba(67, 24, 255, 0.4)',
      background: 'rgba(255, 255, 255, 1)',
      transform: 'translateY(-2px)',
      boxShadow: '0 12px 35px rgba(67, 24, 255, 0.2)',
    },
    '& fieldset': {
      border: 'none',
    },
  },
  '& .MuiInputBase-input': {
    padding: '16px 20px',
    fontSize: '1rem',
    fontWeight: 500,
    '&::placeholder': {
      color: 'rgba(0, 0, 0, 0.4)',
      opacity: 1,
    },
  },
  '& .MuiInputLabel-root': {
    color: 'rgba(67, 24, 255, 0.7)',
    fontWeight: 600,
    '&.Mui-focused': {
      color: 'rgba(67, 24, 255, 1)',
    },
  },
}));

// Créer une constanteconst LoginButton =
const LoginButton = styled(Button)(() => ({
  height: '56px',
  borderRadius: '16px',
  background: `
    linear-gradient(135deg,
      #4318FF 0%,
      #04BEFE 50%,
      #9333ea 100%
    )`,
  color: 'white',
  fontSize: '1.1rem',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: `
    0 8px 25px rgba(67, 24, 255, 0.3),
    0 4px 15px rgba(4, 190, 254, 0.2)
  `,
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `
      linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
      )`,
    transition: 'left 0.5s ease',
  },
  '&:hover': {
    transform: 'translateY(-3px) scale(1.02)',
    boxShadow: `
      0 15px 40px rgba(67, 24, 255, 0.4),
      0 8px 25px rgba(4, 190, 254, 0.3)
    `,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(-1px) scale(1.01)',
  },
  '&:disabled': {
    background: 'rgba(0, 0, 0, 0.12)',
    color: 'rgba(0, 0, 0, 0.26)',
    boxShadow: 'none',
    transform: 'none',
  },
}));

// Créer une constanteconst SignIn =
const SignIn = () => {
  const [user, setUser] = useState<User>({ identifiant: '', password: '' });
  const [btnPosition, setBtnPosition] = useState({ x: 0, y: 0 });
  const [preventClick, setPreventClick] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState(false);
  // Créer une constanteconst navigate =  const navigate = useNavigate();

  // Créer une constanteconst handleInputChange =
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setUser({ ...user, [e.target.name]: e.target.value });

    // Reset position if both fields are filled
    if (e.target.value && user.identifiant && user.password) {
      setBtnPosition({ x: 0, y: 0 });
    }
  };

  // Créer une constanteconst handleSubmit =
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Créer une constanteconst authResponse =      const authResponse = await authService.login({
        Identifiant: user.identifiant,
        MotDePasse: user.password,
      });

      // Check if user is a livreur
      if (authResponse.estLivreur) {
        setErrorMessage(
          "Les livreurs ne peuvent pas accéder à cette application. Veuillez utiliser l'application dédiée aux livreurs.",
        );
        setDialogOpen(true);
        await authService.logout(); // Clear any stored tokens
        return;
      }

      // Determine where to redirect based on permissions
      if (authResponse.estAdmin) {
        navigate(paths.dashboard);
      } else if (authResponse.permissions && authResponse.permissions.length > 0) {
        // Find the first permission that matches a route
        // Créer une constanteconst availableRoutes =        const availableRoutes = sitemap.filter((route: MenuItem) =>
          authResponse.permissions?.some((p) => p.permissionName === route.subheader),
        );

        if (availableRoutes.length > 0) {
          navigate(availableRoutes[0].path || paths.tracking);
        } else {
          navigate(paths.tracking); // Fallback to tracking page
        }
      } else {
        // No permissions, go to tracking page
        navigate(paths.tracking);
      }
    } catch (error: unknown) {
      console.error('Erreur de connexion:', error);

      let errorMsg = 'Erreur de connexion au serveur';

      // Créer une constanteconst axiosError =
      const axiosError = error as AxiosErrorResponse;
      if (axiosError.response?.data) {
        if (typeof axiosError.response.data === 'string') {
          errorMsg = axiosError.response.data;
        } else if (axiosError.response.data.message) {
          errorMsg = axiosError.response.data.message;
        }
      } else if (axiosError.message) {
        errorMsg = axiosError.message;
      }

      setErrorMessage(errorMsg);
      setDialogOpen(true);
    } finally {
      setLoading(false);
    }
  };

  // Créer une constanteconst shiftButtonIfEmpty =
  const shiftButtonIfEmpty = () => {
    if (!user.identifiant || !user.password) {
      setPreventClick(true);

      // Random close movement: max ±100px
      // Créer une constanteconst randomX =      const randomX = Math.floor(Math.random() * 200) - 100; // -100 to +100
      // Créer une constanteconst randomY =      const randomY = Math.floor(Math.random() * 200) - 100; // -100 to +100

      setBtnPosition({ x: randomX, y: randomY });

      setTimeout(() => setPreventClick(false), 300); // match transition
    } else {
      setBtnPosition({ x: 0, y: 0 });
    }
  };

  // Créer une constanteconst handleClickShowPassword =
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // Créer une constanteconst handleCloseDialog =
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <>
      {globalStyles}
      <LoginContainer>
        <Container maxWidth="sm" sx={{ px: { xs: 2, sm: 3 } }}>
          <LoginCard>
            <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>
              {/* Header Section */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 100,
                    height: 100,
                    borderRadius: '24px',
                    background: `
                      linear-gradient(135deg,
                        rgba(67, 24, 255, 0.1),
                        rgba(4, 190, 254, 0.1)
                      )`,
                    border: '2px solid rgba(67, 24, 255, 0.1)',
                    mb: 3,
                    animation: `${float} 3s ease-in-out infinite`,
                  }}
                >
                  <img
                    src={logo}
                    alt="Logo"
                    style={{
                      height: 60,
                      width: 'auto',
                      filter: 'drop-shadow(0 4px 8px rgba(67, 24, 255, 0.2))',
                    }}
                  />
                </Box>

                <Typography
                  variant="h3"
                  fontWeight={700}
                  sx={{
                    background: `
                      linear-gradient(135deg,
                        #4318FF 0%,
                        #04BEFE 50%,
                        #9333ea 100%
                      )`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 1,
                    letterSpacing: '-0.5px',
                  }}
                >
                  Bienvenue
                </Typography>

                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontWeight: 500,
                    opacity: 0.8,
                  }}
                >
                  Connectez-vous à votre espace personnel
                </Typography>
              </Box>

              {/* Form Section */}
              <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
                <Stack direction="column" spacing={3} sx={{ width: '100%' }}>
                  <StyledTextField
                    sx={{ marginTop: 3 }}
                    fullWidth
                    id="identifiant"
                    name="identifiant"
                    label="Identifiant"
                    type="text"
                    value={user.identifiant}
                    onChange={handleInputChange}
                    placeholder="Identifiant"
                    autoComplete="username"
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonOutlineIcon
                            sx={{
                              color: 'rgba(67, 24, 255, 0.6)',
                              mr: 1,
                            }}
                          />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <StyledTextField
                    sx={{ marginTop: 3 }}
                    fullWidth
                    id="password"
                    name="password"
                    label="Mot de passe"
                    type={showPassword ? 'text' : 'password'}
                    value={user.password}
                    onChange={handleInputChange}
                    placeholder="Mot de passe"
                    autoComplete="current-password"
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockOutlinedIcon
                            sx={{
                              color: 'rgba(67, 24, 255, 0.6)',
                              mr: 1,
                            }}
                          />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            edge="end"
                            sx={{
                              color: 'rgba(67, 24, 255, 0.6)',
                              '&:hover': {
                                color: 'rgba(67, 24, 255, 0.8)',
                                background: 'rgba(67, 24, 255, 0.1)',
                              },
                            }}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Box
                    onMouseEnter={shiftButtonIfEmpty}
                    sx={{
                      transition: 'transform 0.3s ease-in-out',
                      transform: `translate(${btnPosition.x}px, ${btnPosition.y}px)`,
                      mt: 3,
                      display: 'flex',
                      justifyContent: 'center',
                      width: '100%',
                    }}
                  >
                    <LoginButton
                      type="submit"
                      disabled={loading}
                      sx={{
                        pointerEvents: preventClick || loading ? 'none' : 'auto',
                        width: '60%',
                        minWidth: '200px',
                      }}
                    >
                      {loading ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <CircularProgress
                            size={24}
                            sx={{
                              color: 'white',
                              animation: `${pulse} 1.5s ease-in-out infinite`,
                            }}
                          />
                          <Typography variant="inherit" fontWeight={600}>
                            Connexion en cours...
                          </Typography>
                        </Box>
                      ) : (
                        'Se connecter'
                      )}
                    </LoginButton>
                  </Box>
                </Stack>
              </Box>

              {/* Footer */}
              <Box sx={{ textAlign: 'center', mt: 4 }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontWeight: 500,
                    opacity: 0.7,
                  }}
                >
                  © {new Date().getFullYear()} Tous droits réservés
                </Typography>
              </Box>
            </CardContent>
          </LoginCard>
        </Container>
      </LoginContainer>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: '20px',
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.95),
                rgba(248, 250, 252, 0.9)
              )`,
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: `
              0 25px 50px rgba(0, 0, 0, 0.15),
              0 15px 35px rgba(239, 68, 68, 0.1)
            `,
            minWidth: 400,
          },
        }}
      >
        <DialogTitle
          id="alert-dialog-title"
          sx={{
            textAlign: 'center',
            pb: 1,
            pt: 3,
          }}
        >
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 60,
              height: 60,
              borderRadius: '16px',
              background: `
                linear-gradient(135deg,
                  rgba(239, 68, 68, 0.1),
                  rgba(220, 38, 38, 0.1)
                )`,
              border: '2px solid rgba(239, 68, 68, 0.2)',
              mb: 2,
              animation: `${pulse} 2s ease-in-out infinite`,
            }}
          >
            <Typography variant="h4">⚠️</Typography>
          </Box>
          <Typography
            variant="h5"
            fontWeight={600}
            sx={{
              background: `
                linear-gradient(135deg,
                  #ef4444 0%,
                  #dc2626 100%
                )`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Accès refusé
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ textAlign: 'center', px: 3 }}>
          <DialogContentText
            id="alert-dialog-description"
            sx={{
              fontSize: '1rem',
              fontWeight: 500,
              color: 'rgba(0, 0, 0, 0.7)',
              lineHeight: 1.6,
            }}
          >
            {errorMessage}
          </DialogContentText>
        </DialogContent>

        <DialogActions sx={{ justifyContent: 'center', pb: 3, px: 3 }}>
          <Button
            onClick={handleCloseDialog}
            autoFocus
            sx={{
              borderRadius: '12px',
              px: 4,
              py: 1.5,
              background: `
                linear-gradient(135deg,
                  #ef4444 0%,
                  #dc2626 100%
                )`,
              color: 'white',
              fontWeight: 600,
              textTransform: 'none',
              boxShadow: '0 8px 25px rgba(239, 68, 68, 0.3)',
              '&:hover': {
                background: `
                  linear-gradient(135deg,
                    #dc2626 0%,
                    #b91c1c 100%
                  )`,
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 35px rgba(239, 68, 68, 0.4)',
              },
            }}
          >
            Compris
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default SignIn;
