ninja: Entering directory `E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\.cxx\Debug\5u705b2z\x86'
[0/2] Re-checking globbed directories...
[1/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o
[3/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[5/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[6/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[7/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[8/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/57] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[10/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o
[11/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o
[12/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/RNCGeolocationSpecJSI-generated.cpp.o
[13/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o
[14/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o
[15/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o
[16/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/States.cpp.o
[17/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/Props.cpp.o
[18/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ComponentDescriptors.cpp.o
[19/57] Building CXX object CMakeFiles/appmodules.dir/E_/PFE/Test/AxiaLivraison/LivreurApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[20/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/EventEmitters.cpp.o
[21/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/rncamerakit_specsJSI-generated.cpp.o
[22/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ShadowNodes.cpp.o
[23/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/rncamerakit_specs-generated.cpp.o
[24/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5c5968c28e9054d80daf3372f8875b8b/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[25/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/911185115b92084af5749d6839b1ee45/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[26/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bd3d273595b238d6103253a64b32bbdc/jni/react/renderer/components/safeareacontext/States.cpp.o
[27/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1cd311f72e1ed3397b9292b0f9cd071b/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[28/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/df9a661d896a082f4680462e13e60c6a/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[29/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bd3d273595b238d6103253a64b32bbdc/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[30/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bd3d273595b238d6103253a64b32bbdc/jni/react/renderer/components/safeareacontext/Props.cpp.o
[31/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03e520ad6db8114c54e989558c0fdc47/react/renderer/components/safeareacontext/EventEmitters.cpp.o
[32/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0614d953db3593e6c8689be428874c2/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[33/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\cxx\Debug\5u705b2z\obj\x86\libreact_codegen_safeareacontext.so
[34/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/faec87104ff69b5fdb68bd65d1924d57/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[35/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e639dc7ba55ee000855af538c502e789/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[36/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/42e9ad5a1007f5804dda9d6d2f475812/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[37/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b46e11ecdbe033d159ab7dfa122ba766/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[38/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/42e9ad5a1007f5804dda9d6d2f475812/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[39/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b46e11ecdbe033d159ab7dfa122ba766/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[40/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/faec87104ff69b5fdb68bd65d1924d57/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[41/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/01af0640e5951884982b904283ddf625/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[42/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/01af0640e5951884982b904283ddf625/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[43/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[44/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3b3363816096872fb9581d8bcdbce26e/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[45/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3b3363816096872fb9581d8bcdbce26e/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[46/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[47/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e639dc7ba55ee000855af538c502e789/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[48/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8257577ad99352d36fdd0edb604a33c/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[49/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/01af0640e5951884982b904283ddf625/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[50/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\cxx\Debug\5u705b2z\obj\x86\libreact_codegen_rnscreens.so
[51/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[52/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[53/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[54/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[55/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[56/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[57/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison\LivreurApp\android\app\build\intermediates\cxx\Debug\5u705b2z\obj\x86\libappmodules.so
