// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';

const CardMedia: Components<Omit<Theme, 'components'>>['MuiCardMedia'] = {
  styleOverrides: {
    root: {},
    img: ({ theme }) => ({
      borderRadius: theme.shape.borderRadius * 2.5,
    }),
  },
};

// Exporter comme élément principal de ce fichierexport default
export default CardMedia;
