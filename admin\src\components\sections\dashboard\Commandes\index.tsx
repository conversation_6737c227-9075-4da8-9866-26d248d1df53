// Importer des fonctionnalités depuis un autre fichierimport React, { useState, useEffect, ChangeEvent } fromimport React, { useState, useEffect, ChangeEvent } from 'react';
import {
  Paper,
  Stack,
  Typography,
  TextField,
  InputAdornment,
  Box,
  Pagination,
  CircularProgress,
  SxProps,
} from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport TableContent fromimport TableContent from './TableContent';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport { Commande } fromimport { Commande } from 'types/commande';

// Définir une interface TypeScriptinterface ComplexTableProps
interface ComplexTableProps {
  sx?: SxProps;
}

// Créer une constanteconst Commandes =
const Commandes = ({ sx }: ComplexTableProps) => {
  const [searchText, setSearchText] = useState<string>('');
  const [commandes, setCommandes] = useState<Commande[]>([]);
  const [filteredCommandes, setFilteredCommandes] = useState<Commande[]>([]);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  // Créer une constanteconst itemsPerPage =  const itemsPerPage = 4;

  useEffect(() => {
    fetchCommandes();
  }, []);

  // Créer une constanteconst fetchCommandes =
  const fetchCommandes = async () => {
    try {
      setLoading(true);
      // Créer une constanteconst response =      const response = await axios.get<Commande[]>('http://localhost:5283/api/commandes/liste');
      setCommandes(response.data);
      setFilteredCommandes(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors de la récupération des commandes:', error);
      setLoading(false);
    }
  };

  // Créer une constanteconst handleInputChange =
  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    // Créer une constanteconst value =    const value = event.target.value.toLowerCase();
    setSearchText(value);

    if (value) {
      // Créer une constanteconst filtered =      const filtered = commandes.filter((commande) => {
        return (
          commande.nomClient?.toLowerCase().includes(value) ||
          commande.statut?.toLowerCase().includes(value) ||
          commande.id.toString().includes(value)
        );
      });
      setFilteredCommandes(filtered);
    } else {
      setFilteredCommandes(commandes);
    }
    setPage(1);
  };

  // Créer une constanteconst handlePageChange =
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  // Créer une constanteconst paginatedData =
  const paginatedData = filteredCommandes.slice((page - 1) * itemsPerPage, page * itemsPerPage);

  return (
    <Box component={Paper} sx={{ p: 3, ...sx }}>
      <Stack
        px={2}
        pb={2}
        spacing={{ xs: 2, sm: 0 }}
        direction={{ xs: 'column', sm: 'row' }}
        justifyContent="space-between"
        alignItems="center"
      >
        <Typography
          variant="h5"
          textAlign={{ xs: 'center', sm: 'left' }}
          fontWeight="600"
          color="#333"
        >
          Commandes Récentes
        </Typography>

        <TextField
          variant="outlined"
          size="small"
          placeholder="Rechercher..."
          value={searchText}
          onChange={handleInputChange}
          sx={{
            mx: { xs: 'auto', sm: 'initial' },
            width: 1,
            maxWidth: { xs: 300, sm: 220 },
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#1976d2',
              },
            },
          }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconifyIcon icon="eva:search-fill" color="#666" />
              </InputAdornment>
            ),
          }}
        />
      </Stack>

      <Box sx={{ mt: 1, px: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
            <CircularProgress size={40} />
          </Box>
        ) : (
          <>
            <TableContent data={paginatedData} />
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 1 }}>
              <Pagination
                count={Math.ceil(filteredCommandes.length / itemsPerPage)}
                page={page}
                onChange={handlePageChange}
                color="primary"
                shape="rounded"
                size="medium"
              />
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Commandes;
