ninja: Entering directory `E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\.cxx\Debug\3e3a1458\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: E:/PFE/Test/AxiaLivraison2/LivreurApp/android/app/.cxx/Debug/3e3a1458/arm64-v8a
[0/2] Re-checking globbed directories...
[1/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o
[3/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/RNCGeolocationSpecJSI-generated.cpp.o
[5/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[6/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[7/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o
[8/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[10/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o
[11/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o
[12/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/States.cpp.o
[13/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o
[14/57] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o
[15/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/EventEmitters.cpp.o
[16/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/rncamerakit_specsJSI-generated.cpp.o
[17/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0179b6856136b4560df75b9b86818a06/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[18/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ShadowNodes.cpp.o
[19/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/ComponentDescriptors.cpp.o
[20/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/34726ede54477dee9ea1cf0b6a664602/renderer/components/safeareacontext/EventEmitters.cpp.o
[21/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/rncamerakit_specs-generated.cpp.o
[22/57] Building CXX object rncamerakit_specs_autolinked_build/CMakeFiles/react_codegen_rncamerakit_specs.dir/react/renderer/components/rncamerakit_specs/Props.cpp.o
[23/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/61012cd45ff08503015a1b280de4bb9f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[24/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9780e997eff3556d23f9b860e9143e13/components/safeareacontext/ComponentDescriptors.cpp.o
[25/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a65a39f1b80c9741b274fd3bdce7b0a7/react/renderer/components/safeareacontext/States.cpp.o
[26/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/44a814c64ad2ea77f5751688355f6455/jni/react/renderer/components/safeareacontext/Props.cpp.o
[27/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a5b5463ae6dbfd96fd48ad2a092ae403/safeareacontext/safeareacontextJSI-generated.cpp.o
[28/57] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[29/57] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[30/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/153a811717e8c5211c1c3aec748ca078/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[31/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/34726ede54477dee9ea1cf0b6a664602/renderer/components/safeareacontext/ShadowNodes.cpp.o
[32/57] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/127806176f131c321a809f16310076ba/source/codegen/jni/safeareacontext-generated.cpp.o
[33/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build\intermediates\cxx\Debug\3e3a1458\obj\arm64-v8a\libreact_codegen_safeareacontext.so
[34/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/42b2e89a5e76b482872d03d137254c4d/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[35/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e42da9fcab52f45e9128dc732ec983e1/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[36/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3830aa37505d69fb05b3a7646cccbc8d/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[37/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/153a811717e8c5211c1c3aec748ca078/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[38/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/153a811717e8c5211c1c3aec748ca078/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[39/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/641051fb0d7860fc43ae0c9e6c0f6e7d/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[40/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/641051fb0d7860fc43ae0c9e6c0f6e7d/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[41/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5508d8ec90151540e638d1910dc6ebe2/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[42/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/186d6580070f96e0f6f71ddc609ada8a/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[43/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8d3a359c534d1e4805cc9427aa6643fd/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[44/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/186d6580070f96e0f6f71ddc609ada8a/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[45/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[46/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5508d8ec90151540e638d1910dc6ebe2/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[47/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[48/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[49/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[50/57] Building CXX object CMakeFiles/appmodules.dir/E_/PFE/Test/AxiaLivraison2/LivreurApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[51/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[52/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[53/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[54/57] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8d3a359c534d1e4805cc9427aa6643fd/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[55/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build\intermediates\cxx\Debug\3e3a1458\obj\arm64-v8a\libreact_codegen_rnscreens.so
[56/57] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[57/57] Linking CXX shared library E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build\intermediates\cxx\Debug\3e3a1458\obj\arm64-v8a\libappmodules.so
