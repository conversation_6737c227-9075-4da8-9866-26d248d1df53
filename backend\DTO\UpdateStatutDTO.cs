// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.DTO
namespace AxiaLivraisonAPI.DTO
{
    // Classe publique qui définit un objetpublic class UpdateStatutDTO    public class UpdateStatutDTO
    {
        // Cette propriété est obligatoire[Required]        [Required]
        public string Statut { get; set; }
    }
}
