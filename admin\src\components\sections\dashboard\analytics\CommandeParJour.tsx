// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface CommandesAujourdhui
interface CommandesAujourdhui {
  nombreCommandes: number;
  pourcentageChangement: number;
}

// Créer une constanteconst Tasks =
const Tasks = () => {
  const [nombreCommandes, setNombreCommandes] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchCommandesAujourdhui =    const fetchCommandesAujourdhui = async () => {
      try {
        setLoading(true);
        // Créer une constanteconst response =        const response = await axios.get<CommandesAujourdhui>(
          `${API_URL}/api/statistiques/commandes-aujourdhui`,
        );
        setNombreCommandes(response.data.nombreCommandes);
      } catch (err) {
        console.error('Erreur lors de la récupération des commandes du jour:', err);
        setNombreCommandes(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCommandesAujourdhui();
  }, []);

  return (
    <Stack component={Paper} p={2.5} alignItems="center" spacing={2.25} height={100}>
      <Stack
        alignItems="center"
        justifyContent="center"
        height={56}
        width={56}
        borderRadius="50%"
        sx={(theme) => ({
          background: `linear-gradient(90deg, ${theme.palette.gradients.secondary.main} 0%, ${theme.palette.gradients.secondary.state} 100%)`,
        })}
      >
        <IconifyIcon icon="ic:baseline-add-task" fontSize="h3.fontSize" color="info.lighter" />
      </Stack>
      <div>
        <Typography variant="body2" color="text.disabled" noWrap>
          Commandes Aujourd'hui
        </Typography>
        <Stack alignItems="center">
          <Typography mt={0.25} variant="h3">
            {loading ? '...' : nombreCommandes}
          </Typography>
        </Stack>
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Tasks;
