                        -HE:\PFE\Test\AxiaLivraison2\LivreurApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build\intermediates\cxx\Debug\3e3a1458\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build\intermediates\cxx\Debug\3e3a1458\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\.cxx\Debug\3e3a1458\prefab\x86\prefab
-BE:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\.cxx\Debug\3e3a1458\x86
-GNinja
-DPROJECT_BUILD_DIR=E:\PFE\Test\AxiaLivraison2\LivreurApp\android\app\build
-DPROJECT_ROOT_DIR=E:\PFE\Test\AxiaLivraison2\LivreurApp\android
-DREACT_ANDROID_DIR=E:\PFE\Test\AxiaLivraison2\LivreurApp\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2