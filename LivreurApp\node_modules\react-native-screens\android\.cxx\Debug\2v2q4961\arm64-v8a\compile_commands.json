[{"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/.cxx/Debug/2v2q4961/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\E_\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/.cxx/Debug/2v2q4961/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\E_\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/.cxx/Debug/2v2q4961/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/.cxx/Debug/2v2q4961/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\NativeProxy.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp"}, {"directory": "E:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/.cxx/Debug/2v2q4961/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IE:/PFE/Test/AxiaLivraison/LivreurApp/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/072209afb1c52d43fe8bcfe0160879d7/transformed/react-android-0.79.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\OnLoad.cpp.o -c E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp", "file": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp"}]