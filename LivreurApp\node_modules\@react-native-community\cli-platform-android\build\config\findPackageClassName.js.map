{"version": 3, "names": ["getPackageClassName", "folder", "files", "glob", "sync", "cwd", "packages", "map", "filePath", "fs", "readFileSync", "path", "join", "matchClassName", "filter", "match", "length", "file", "nativeModuleMatch"], "sources": ["../../src/config/findPackageClassName.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport glob from 'glob';\nimport path from 'path';\n\nexport default function getPackageClassName(folder: string) {\n  const files = glob.sync('**/+(*.java|*.kt)', {cwd: folder});\n\n  const packages = files\n    .map((filePath) => fs.readFileSync(path.join(folder, filePath), 'utf8'))\n    .map(matchClassName)\n    .filter((match) => match);\n\n  // @ts-ignore\n  return packages.length ? packages[0][1] : null;\n}\n\nexport function matchClassName(file: string) {\n  const nativeModuleMatch = file.match(\n    /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+implements\\s+|:)[\\s\\w():,]*[^{]*ReactPackage/,\n  );\n  // We first check for implementation of ReactPackage to find native\n  // modules and then for subclasses of TurboReactPackage to find turbo modules.\n  if (nativeModuleMatch) {\n    return nativeModuleMatch;\n  } else {\n    return file.match(\n      /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+extends\\s+|:)[\\s\\w():,]*[^{]*TurboReactPackage/,\n    );\n  }\n}\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAVxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAMe,SAASA,mBAAmB,CAACC,MAAc,EAAE;EAC1D,MAAMC,KAAK,GAAGC,eAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE;IAACC,GAAG,EAAEJ;EAAM,CAAC,CAAC;EAE3D,MAAMK,QAAQ,GAAGJ,KAAK,CACnBK,GAAG,CAAEC,QAAQ,IAAKC,aAAE,CAACC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACX,MAAM,EAAEO,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,CACvED,GAAG,CAACM,cAAc,CAAC,CACnBC,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAAC;;EAE3B;EACA,OAAOT,QAAQ,CAACU,MAAM,GAAGV,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AAChD;AAEO,SAASO,cAAc,CAACI,IAAY,EAAE;EAC3C,MAAMC,iBAAiB,GAAGD,IAAI,CAACF,KAAK,CAClC,gFAAgF,CACjF;EACD;EACA;EACA,IAAIG,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAOD,IAAI,CAACF,KAAK,CACf,kFAAkF,CACnF;EACH;AACF"}