﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect, RefObject } fromimport { useState, useEffect, RefObject } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport ResizeObserver fromimport ResizeObserver from 'resize-observer-polyfill';

// Créer une constanteconst useResizeObserver =
const useResizeObserver = (ref: RefObject<HTMLElement>) => {
  const [width, setWidth] = useState(0);

  useEffect(() => {
    // Créer une constanteconst handleResize =    const handleResize = (entries: ResizeObserverEntry[]) => {
      for (const entry of entries) {
        if (entry.contentRect) {
          setWidth(entry.contentRect.width);
        }
      }
    };

    // Créer une constanteconst observer =
    const observer = new ResizeObserver(handleResize);
    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [ref]);

  return width;
};

// Exporter comme élément principal de ce fichierexport default
export default useResizeObserver;
