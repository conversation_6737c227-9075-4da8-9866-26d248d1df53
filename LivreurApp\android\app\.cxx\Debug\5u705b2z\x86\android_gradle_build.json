{"buildFiles": ["E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\.cxx\\Debug\\5u705b2z\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "appmodules", "output": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5u705b2z\\obj\\x86\\libappmodules.so", "runtimeFiles": ["E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5u705b2z\\obj\\x86\\libreact_codegen_safeareacontext.so", "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5u705b2z\\obj\\x86\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNCGeolocationSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rncamerakit_specs::@d945e98661337998d651": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rncamerakit_specs"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnscreens", "output": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5u705b2z\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_safeareacontext", "output": "E:\\PFE\\Test\\AxiaLivraison\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\5u705b2z\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}