// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Models
namespace AxiaLivraisonAPI.Models
{
    // Classe publique qui définit un objetpublic class Permission    public class Permission
    {
        // Marquer comme clé primaire de la base de données[Key]        [Key]
        public int Id { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MaxLength(255)]
        public string PermissionName { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        public ICollection<UtilisateurPermission> UtilisateurPermissions { get; set; }
    }
}