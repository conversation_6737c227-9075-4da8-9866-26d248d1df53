﻿// Importer des fonctionnalités depuis un autre fichierimport React, { useState } fromimport React, { useState } from 'react';
import {
  TextField,
  Button,
  Paper,
  Typography,
  Container,
  InputAdornment,
  Grid,
} from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport PermissionTable fromimport PermissionTable from './PermissionTable';
// Importer des fonctionnalités depuis un autre fichierimport { Permission } fromimport { Permission } from './types';
// Importer des fonctionnalités depuis un autre fichierimport AddIcon fromimport AddIcon from '@mui/icons-material/Add';
// Importer des fonctionnalités depuis un autre fichierimport SearchIcon fromimport SearchIcon from '@mui/icons-material/Search';

// Définir une interface TypeScriptinterface PermissionListProps
interface PermissionListProps {
  permissions: Permission[];
  onAddPermission: () => void;
  onEditPermission: (permission: Permission) => void;
  onDeletePermission: (id: number) => void;
}

const PermissionList: React.FC<PermissionListProps> = ({
  permissions,
  onAddPermission,
  onEditPermission,
  onDeletePermission,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Créer une constanteconst handleSearchChange =
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value.toLowerCase());
  };

  // Créer une constanteconst filteredPermissions =
  const filteredPermissions = permissions.filter((permission) => {
    // Créer une constanteconst matchesName =    const matchesName = permission.permissionName.toLowerCase().includes(searchTerm);
    // Créer une constanteconst matchesDescription =    const matchesDescription = permission.description.toLowerCase().includes(searchTerm);
    return matchesName || matchesDescription;
  });

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 3 }}>
        Gestion des Permissions
      </Typography>

      <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Grid item xs={10}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={handleSearchChange}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  borderWidth: '1px',
                  borderStyle: 'solid',
                },
                '&.Mui-focused fieldset': {
                  borderWidth: '0px',
                },
              },
            }}
          />
        </Grid>
        <Grid item xs={2}>
          <Button
            fullWidth
            variant="contained"
            sx={{
              bgcolor: '#4caf50',
              '&:hover': { bgcolor: '#388e3c' },
              height: '40px',
              textTransform: 'none',
            }}
            startIcon={<AddIcon />}
            onClick={onAddPermission}
          >
            Ajouter
          </Button>
        </Grid>
      </Grid>

      <Paper elevation={0}>
        <PermissionTable
          permissions={filteredPermissions}
          onEditPermission={onEditPermission}
          onDeletePermission={onDeletePermission}
        />
      </Paper>
    </Container>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default PermissionList;
