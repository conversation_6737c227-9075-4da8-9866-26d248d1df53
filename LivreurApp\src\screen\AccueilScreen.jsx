import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
  StatusBar,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
  PermissionsAndroid,
  Linking,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import StatusUpdateForm from './StatusUpdateForm';
import Geolocation from '@react-native-community/geolocation';
import { colors, getStatusColor, getStatusBackgroundColor } from '../theme';

// Get screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive helper functions
const wp = (percentage) => {
  return (percentage * screenWidth) / 100;
};

const hp = (percentage) => {
  return (percentage * screenHeight) / 100;
};

// Device type detection
const isTablet = screenWidth >= 768;
const isSmallDevice = screenWidth < 350;

// Configure geolocation
Geolocation.setRNConfiguration({
  skipPermissionRequests: false,
  authorizationLevel: 'whenInUse',
  locationProvider: 'auto',
});

const API_BASE_URL = Platform.select({
  android: 'http://*************:5283', 
  ios: 'http://localhost:5283',
  default: 'http://localhost:5283',
});
const AccueilScreen = ({route}) => {
  const navigation = useNavigation();
  const {user} = route.params || {};
  const [commandes, setCommandes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showStatusForm, setShowStatusForm] = useState(false);
  const [selectedCommande, setSelectedCommande] = useState(null);
  const [updating, setUpdating] = useState(false);
  const [isTracking, setIsTracking] = useState(false);

  const formatDate = dateString => {
    if (!dateString) return 'Date inconnue';
    try {
      const date = new Date(dateString);
      const options = {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      };
      return date.toLocaleDateString('fr-FR', options);
    } catch (e) {
      return 'Date inconnue';
    }
  };



  const fetchCommandes = async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier si user existe et a un id
      if (!user || !user.id) {
        console.error('Utilisateur non défini ou sans ID');
        setError('Utilisateur non identifié. Veuillez vous reconnecter.');
        setLoading(false);
        return;
      }

      console.log(`Récupération des commandes pour l'utilisateur ID: ${user.id}`);
      const response = await fetch(
        `${API_BASE_URL}/api/Commandes/livreur/${user.id}`,
      );
      const data = await response.json();
      if (response.ok) {
        setCommandes(data || []);
      } else {
        setError(
          data.message || 'Erreur lors de la récupération des commandes',
        );
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
      console.error('Erreur fetchCommandes:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateCommandeStatus = async newStatus => {
    if (!selectedCommande) return;

    setUpdating(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/Commandes/modifier-statut/${selectedCommande.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            Statut: newStatus,
          }),
        },
      );

      const responseText = await response.text();
      console.log('Raw response:', responseText);

      if (!response.ok) {
        throw new Error(response.statusText || 'Échec de la mise à jour');
      }

      // Mise à jour optimiste de l'état local
      setCommandes(prev =>
        prev.map(cmd =>
          cmd.id === selectedCommande.id ? {...cmd, statut: newStatus} : cmd,
        ),
      );

      Alert.alert('Succès', 'Statut mis à jour avec succès');
    } catch (error) {
      console.error('Erreur:', error);
      Alert.alert('Erreur', error.message || 'Échec de la mise à jour');
    } finally {
      setUpdating(false);
      setShowStatusForm(false);
    }
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchCommandes();

      // Vérifier si on doit ouvrir le formulaire de modification de statut
      if (route.params?.openStatusForm && route.params?.selectedCommande) {
        setSelectedCommande(route.params.selectedCommande);
        setShowStatusForm(true);

        // Réinitialiser les paramètres pour éviter de rouvrir le formulaire
        // si l'utilisateur revient à cet écran
        navigation.setParams({ openStatusForm: undefined, selectedCommande: undefined });
      }
    });
    return unsubscribe;
  }, [navigation, route.params]);

  // Animation pour l'indicateur de suivi supprimée

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {text: 'Annuler', style: 'cancel'},
        {text: 'Déconnecter', onPress: () => navigation.navigate('Login')},
      ],
      {cancelable: false},
    );
  };

  // Function to directly check if location services are enabled on Android
  const isLocationEnabled = () => {
    return new Promise((resolve) => {
      if (Platform.OS === 'android') {
        // On Android, we can try a quick location request with low accuracy
        // If it fails immediately with a specific error, location is disabled
        Geolocation.getCurrentPosition(
          () => resolve(true), // Location services are enabled
          (error) => {
            console.log('Location check error:', error);
            // Check error code to determine if location services are disabled
            // Error code 2 typically means location is disabled
            if (error.code === 2) {
              resolve(false);
            } else if (error.code === 1) {
              // Permission denied
              resolve(false);
            } else {
              // For other errors, we'll assume location might be enabled
              resolve(true);
            }
          },
          {
            enableHighAccuracy: false,
            timeout: 2000, // Short timeout
            maximumAge: 3600000, // Accept a recent location (1 hour)
            forceRequestLocation: false // Don't force, just check
          }
        );
      } else {
        // On iOS, we'll just assume it's enabled and let the OS handle it
        resolve(true);
      }
    });
  };

  // Function to check if location services are enabled
  const checkLocationServices = async () => {
    try {
      // First check if location permission is granted
      const hasPermission = await checkLocationPermission();
      if (!hasPermission) {
        return; // Permission denied, exit early
      }

      // Then check if location services are enabled
      const enabled = await isLocationEnabled();
      if (!enabled) {
        // Location services are disabled
        Alert.alert(
          'Location Error',
          'Location services are disabled. Please enable location services in your device settings.',
          [
            {
              text: 'Open Settings',
              onPress: () => {
                Linking.openSettings();
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        return;
      }

      // If we get here, both permission is granted and location is enabled
      getPosition();
    } catch (error) {
      console.error('Error checking location services:', error);
      Alert.alert(
        'Location Error',
        'Failed to check location services. Please make sure location services are enabled in your device settings.',
        [
          {
            text: 'Open Settings',
            onPress: () => {
              Linking.openSettings();
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    }
  };

  // Function to check location permission on Android
  const checkLocationPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location to show your current position.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        return true;
      } else {
        Alert.alert(
          'Permission Denied',
          'Location permission is required to use this feature. Please enable it in app settings.',
          [
            {
              text: 'Open Settings',
              onPress: () => {
                // Open app settings so user can enable permissions
                if (Platform.OS === 'android') {
                  Linking.openSettings();
                }
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        return false;
      }
    } catch (err) {
      console.error('Error requesting location permission:', err);
      return false;
    }
  };



  const renderCommande = ({item}) => {
    const statusColor = getStatusColor(item.statut);

    return (
      <View style={styles.commandeCard}>
        <View style={styles.commandeHeader}>
          <Text style={styles.commandeId}>CMD-{item.id || 'N/A'}</Text>
          <View
            style={[styles.statusBadge, {backgroundColor: `${statusColor}15`}]}>
            <View style={[styles.statusDot, {backgroundColor: statusColor}]} />
            <Text style={[styles.statusText, {color: statusColor}]}>
              {item.statut}
            </Text>
          </View>
        </View>

        <View style={styles.commandeDetails}>
          <Text style={styles.clientName}>
            {item.nomClient || 'Client inconnu'}
          </Text>
          <Text style={styles.commandeDate}>
            {formatDate(item.dateCreation)}
          </Text>
        </View>

        <View style={styles.divider} />

        <View style={styles.commandeFooter}>
          <Text style={styles.itemCount}>
            {item.quantite || 0} {item.quantite > 1 ? 'articles' : 'article'}
          </Text>
          <Text style={styles.totalPrice}>
            {(item.montantTotale || 0).toFixed(2)} DT
          </Text>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.updateStatusButton, {backgroundColor: statusColor}]}
            onPress={() => {
              setSelectedCommande(item);
              setShowStatusForm(true);
            }}>
            <Text style={styles.updateStatusButtonText}>Modifier statut</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.detailsButton}
            onPress={() =>
              navigation.navigate('DetailCommande', {commande: item})
            }>
            <Text style={styles.detailsButtonText}>Détails</Text>
            <AntDesign name="right" size={16} color={colors.primary.main} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Add location tracking
  useEffect(() => {
    let watchId;

    const updateLocation = async (position) => {
      try {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;

        console.log('New position:', { lat, lng });

        // Pas d'alerte pour les mises à jour de position

        const response = await fetch(`${API_BASE_URL}/api/commandes/position`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            livreurId: user.id,
            latitude: lat,
            longitude: lng,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update position');
        }

        const responseData = await response.json();
        console.log('Server response:', responseData);

      } catch (error) {
        console.error('Error updating position:', error);
      }
    };

    const startLocationTracking = async () => {
      try {
        // First check if location permission is granted
        const hasPermission = await checkLocationPermission();
        if (!hasPermission) {
          console.log('Location permission denied for background tracking');
          return;
        }

        // Then check if location services are enabled
        const enabled = await isLocationEnabled();
        if (!enabled) {
          console.log('Location services disabled for background tracking');
          return;
        }

        // Only start watching position if both checks pass
        watchId = Geolocation.watchPosition(
          updateLocation,
          (error) => console.error('Location tracking error:', error),
          {
            enableHighAccuracy: false, // Use low accuracy to save battery
            distanceFilter: 0, // Update regardless of distance moved
            interval: 5000, // Update every 5 seconds
            fastestInterval: 5000, // But not faster than every 5 seconds
            timeout: 10000 // 10 second timeout
          }
        );

        console.log('Location tracking started with ID:', watchId);

        // Vérifier s'il y a des livraisons actives, mais ne pas afficher d'alerte
        const activeDeliveries = commandes.filter(cmd => cmd.statut?.toLowerCase() === 'en transit');
        // Mettre à jour l'état de tracking en interne, mais ne pas l'afficher à l'utilisateur
        if (activeDeliveries.length > 0) {
          // Activer le tracking en interne mais sans notification visuelle
          setIsTracking(false); // On met à false pour ne pas afficher l'indicateur visuel
        } else {
          setIsTracking(false);
        }
      } catch (error) {
        console.error('Failed to start location tracking:', error);
      }
    };

    if (user?.id) {
      // Delay starting location tracking to ensure app is fully loaded
      const timer = setTimeout(() => {
        startLocationTracking();
      }, 5000); // 5 second delay

      return () => {
        clearTimeout(timer);
        if (watchId) {
          console.log('Clearing location tracking:', watchId);
          Geolocation.clearWatch(watchId);
          setIsTracking(false);
        }
      };
    }

    return () => {
      if (watchId) {
        console.log('Clearing location tracking:', watchId);
        Geolocation.clearWatch(watchId);
        setIsTracking(false);
      }
    };
  }, [user, commandes]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFF" />

      <View style={styles.header}>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <AntDesign name="logout" size={24} color="#262626" />
        </TouchableOpacity>

        {/* Indicateur de suivi supprimé */}

        <TouchableOpacity
          onPress={() => navigation.navigate('Profile', {user})}
          style={styles.profileButton}>
          <Image
            source={
              user?.image
                ? {uri: user.image}
                : require('../assets/images/default-profile.png')
            }
            style={styles.profileImage}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#623AA2" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchCommandes}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={commandes}
            renderItem={renderCommande}
            keyExtractor={(item, index) =>
              item?.id?.toString() || index.toString()
            }
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Aucune commande disponible</Text>
              </View>
            }
          />
        )}
      </View>

      <StatusUpdateForm
        visible={showStatusForm}
        currentStatus={selectedCommande?.statut}
        onClose={() => setShowStatusForm(false)}
        onSave={updateCommandeStatus}
        loading={updating}
      />

      {/* Floating Scanner Button */}
      <TouchableOpacity
        style={styles.floatingScannerButton}
        onPress={() => navigation.navigate('QRScanner', { user })}>
        <MaterialIcons name="qr-code-scanner" size={28} color="#FFFFFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: isTablet ? wp(4) : wp(5),
    paddingVertical: isTablet ? hp(2) : hp(1.8),
    backgroundColor: colors.neutral.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral.border,
    elevation: 2,
  },
  headerTitle: {
    fontSize: isTablet ? wp(3.5) : wp(5),
    fontWeight: 'bold',
    color: colors.text.dark,
  },

  logoutButton: {
    padding: isTablet ? wp(1.5) : wp(1.2),
  },
  profileButton: {
    padding: isTablet ? wp(1.5) : wp(1.2),
  },
  profileImage: {
    width: isTablet ? wp(8) : wp(10),
    height: isTablet ? wp(8) : wp(10),
    borderRadius: isTablet ? wp(4) : wp(5),
    borderWidth: 1,
    borderColor: colors.neutral.border,
  },
  content: {
    flex: 1,
    padding: isTablet ? wp(3) : wp(4),
  },
  floatingScannerButton: {
    position: 'absolute',
    bottom: isTablet ? hp(4.8) : hp(4.3), // Déplacé vers le haut de 8px
    right: isTablet ? wp(4) : wp(5),
    width: isTablet ? wp(12) : wp(15),
    height: isTablet ? wp(12) : wp(15),
    borderRadius: isTablet ? wp(6) : wp(7.5),
    backgroundColor: colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.primary.main,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  commandeCard: {
    backgroundColor: colors.neutral.surface,
    borderRadius: isTablet ? wp(2) : wp(3),
    padding: isTablet ? wp(3) : wp(4),
    marginBottom: isTablet ? wp(2) : wp(3),
    shadowColor: colors.shadow.medium,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  commandeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: isTablet ? wp(2) : wp(3),
  },
  commandeId: {
    fontSize: isTablet ? wp(3) : wp(4),
    fontWeight: '600',
    color: colors.text.primary,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: isTablet ? wp(2) : wp(2.5),
    paddingVertical: isTablet ? wp(1) : wp(1),
    borderRadius: isTablet ? wp(2) : wp(3),
  },
  statusDot: {
    width: isTablet ? wp(1.5) : wp(2),
    height: isTablet ? wp(1.5) : wp(2),
    borderRadius: isTablet ? wp(0.75) : wp(1),
    marginRight: isTablet ? wp(1) : wp(1.5),
  },
  statusText: {
    fontSize: isTablet ? wp(2.2) : wp(3),
    fontWeight: '500',
  },
  commandeDetails: {
    marginBottom: isTablet ? wp(2) : wp(3),
  },
  clientName: {
    fontSize: isTablet ? wp(2.8) : wp(3.8),
    fontWeight: '500',
    color: colors.text.medium,
    marginBottom: isTablet ? wp(1) : wp(1),
  },
  commandeDate: {
    fontSize: isTablet ? wp(2.5) : wp(3.5),
    color: colors.text.secondary,
  },
  divider: {
    height: 1,
    backgroundColor: colors.neutral.divider,
    marginVertical: isTablet ? wp(2) : wp(3),
  },
  commandeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemCount: {
    fontSize: isTablet ? wp(2.5) : wp(3.5),
    color: colors.text.secondary,
  },
  totalPrice: {
    fontSize: isTablet ? wp(3) : wp(4),
    fontWeight: '600',
    color: colors.text.primary,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: isTablet ? wp(2) : wp(3),
  },
  updateStatusButton: {
    flex: 1,
    padding: isTablet ? wp(2) : wp(2.5),
    borderRadius: isTablet ? wp(1.5) : wp(2),
    marginRight: isTablet ? wp(1.5) : wp(2),
    alignItems: 'center',
  },
  updateStatusButtonText: {
    color: colors.text.inverse,
    fontWeight: 'bold',
    fontSize: isTablet ? wp(2.5) : wp(3.5),
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: isTablet ? wp(2) : wp(2.5),
    borderWidth: 1,
    borderColor: colors.primary.main,
    borderRadius: isTablet ? wp(1.5) : wp(2),
  },
  detailsButtonText: {
    color: colors.primary.main,
    fontWeight: 'bold',
    marginRight: isTablet ? wp(1.5) : wp(2),
    fontSize: isTablet ? wp(2.5) : wp(3.5),
  },
  listContainer: {
    paddingBottom: isTablet ? hp(3) : hp(2.5),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: isTablet ? wp(8) : wp(10),
  },
  emptyText: {
    fontSize: isTablet ? wp(3) : wp(4),
    color: colors.text.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: isTablet ? wp(4) : wp(5),
  },
  errorText: {
    fontSize: isTablet ? wp(3) : wp(4),
    color: colors.status.error,
    marginBottom: isTablet ? wp(4) : wp(5),
    textAlign: 'center',
  },
  retryButton: {
    padding: isTablet ? wp(2.5) : wp(3),
    backgroundColor: colors.primary.main,
    borderRadius: isTablet ? wp(1.5) : wp(2),
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontWeight: 'bold',
    fontSize: isTablet ? wp(2.5) : wp(3.5),
  },
  // Styles pour l'indicateur de suivi supprimés
});

export default AccueilScreen;
