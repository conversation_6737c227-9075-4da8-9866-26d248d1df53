﻿// Importer des fonctionnalités depuis un autre fichierimport React, { useEffect, useState } fromimport React, { useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport LivreurList fromimport LivreurList from './LivreurList';
// Importer des fonctionnalités depuis un autre fichierimport LivreurForm fromimport LivreurForm from './LivreurForm';
// Importer des fonctionnalités depuis un autre fichierimport { Utilisateur } fromimport { Utilisateur } from './types';
// Importer des fonctionnalités depuis un autre fichierimport { DeleteResult } fromimport { DeleteResult } from '../../types/deleteResult';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';

const GestionLivreur: React.FC = () => {
  const [livreurs, setLivreurs] = useState<Utilisateur[]>([]);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [currentLivreur, setCurrentLivreur] = useState<Utilisateur | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<boolean>(false);
  const [deleteResultOpen, setDeleteResultOpen] = useState<boolean>(false);
  const [livreurToDelete, setLivreurToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283'; // Backend URL

  useEffect(() => {
    fetchLivreurs();
  }, []);

  // Créer une constanteconst fetchLivreurs =
  const fetchLivreurs = async () => {
    try {
      // Créer une constanteconst response =      const response = await axios.get(`${API_URL}/api/utilisateurs/livreurs`);
      setLivreurs(response.data);
    } catch (error) {
      console.error('Erreur lors de la récupération des livreurs :', error);
    }
  };

  // Créer une constanteconst handleSave =
  const handleSave = async (livreurData: {
    nom: string;
    email: string;
    telephone: string;
    identifiant: string;
    motDePasse: string;
    imageFile?: File;
  }) => {
    // Créer une constanteconst formData =    const formData = new FormData();
    formData.append('Nom', livreurData.nom);
    formData.append('Email', livreurData.email);
    formData.append('Telephone', livreurData.telephone);
    formData.append('Identifiant', livreurData.identifiant);
    formData.append('MotDePasse', livreurData.motDePasse);
    if (livreurData.imageFile) {
      formData.append('ImageFile', livreurData.imageFile);
    }

    try {
      if (currentLivreur) {
        await axios.put(`${API_URL}/api/utilisateurs/modifier/${currentLivreur.id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
      } else {
        await axios.post(`${API_URL}/api/utilisateurs/ajouter-livreur`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
      }
      fetchLivreurs(); // Refresh the list
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du livreur :', error);
    }
  };

  // Créer une constanteconst handleDelete =
  const handleDelete = (id: number) => {
    setLivreurToDelete(id);
    setDeleteConfirmOpen(true);
  };

  // Créer une constanteconst handleDeleteConfirmed =
  const handleDeleteConfirmed = async () => {
    if (!livreurToDelete) return;

    try {
      setIsDeleting(true);
      // Créer une constanteconst response =      const response = await axios.delete(
        `${API_URL}/api/utilisateurs/supprimer/${livreurToDelete}`,
      );

      // Fermer le dialogue de confirmation
      setDeleteConfirmOpen(false);

      // Afficher le résultat
      setDeleteResult({
        success: true,
        message: response.data.message,
        details: response.data.details,
      });

      // Rafraîchir la liste des livreurs
      fetchLivreurs();
    } catch (error) {
      console.error('Erreur lors de la suppression du livreur:', error);

      let errorMessage = 'Une erreur est survenue lors de la suppression.';
      if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setDeleteResult({
        success: false,
        message: errorMessage,
      });
    } finally {
      setIsDeleting(false);
      setDeleteResultOpen(true);
      setLivreurToDelete(null);
    }
  };

  return (
    <>
      <LivreurList
        livreurs={livreurs}
        onAddLivreur={() => {
          setCurrentLivreur(null); // Reset for adding
          setOpenDialog(true);
        }}
        onEditLivreur={(livreur: Utilisateur) => {
          setCurrentLivreur(livreur); // Set livreur to edit
          setOpenDialog(true);
        }}
        onDeleteLivreur={handleDelete}
      />
      <LivreurForm
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        onSubmit={handleSave}
        initialData={currentLivreur || undefined}
      />

      {/* Dialogue de confirmation de suppression */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>Êtes-vous sûr de vouloir supprimer ce livreur ?</Typography>
          <Typography sx={{ mt: 2, color: 'warning.main' }}>
            Cette action est irréversible et entraînera la suppression de toutes les données
            associées à ce livreur (commandes, permissions, rapports, etc.).
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Annuler</Button>
          <Button onClick={handleDeleteConfirmed} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de résultat de suppression */}
      <Dialog open={deleteResultOpen} onClose={() => setDeleteResultOpen(false)}>
        <DialogTitle sx={{ color: deleteResult?.success ? 'success.main' : 'error.main' }}>
          {deleteResult?.success ? 'Suppression réussie' : 'Erreur de suppression'}
        </DialogTitle>
        <DialogContent>
          {isDeleting ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
              <CircularProgress />
            </div>
          ) : (
            <>
              <Typography>{deleteResult?.message}</Typography>
              {deleteResult?.success && deleteResult?.details && (
                <Typography sx={{ mt: 2, fontSize: '0.9em', color: 'text.secondary' }}>
                  Éléments supprimés :
                  <br />• {deleteResult.details.commandesCount} commande(s)
                  <br />• {deleteResult.details.permissionsCount} permission(s)
                  <br />• {deleteResult.details.rapportsCount} rapport(s)
                </Typography>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteResultOpen(false)} color="primary" variant="contained">
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default GestionLivreur;
