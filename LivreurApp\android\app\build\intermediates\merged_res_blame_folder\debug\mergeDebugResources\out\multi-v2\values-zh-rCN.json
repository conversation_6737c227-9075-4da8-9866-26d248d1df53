{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "33,45,66,68,69,71,84,85,86,121,122,123,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,3879,5902,6028,6094,6228,7065,7130,7199,9682,9755,9827,9895,10045,10118,10190,10267,10343,10415,10485,10554,10733,10801,10872,10939", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "2870,3944,5964,6089,6164,6288,7125,7194,7265,9750,9822,9890,9961,10113,10185,10262,10338,10410,10480,10549,10627,10796,10867,10934,11003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3949,4050,4179,4294,4396,4501,4617,4719,4910,5018,5119,5249,5364,5468,5576,5632,5689", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4045,4174,4289,4391,4496,4612,4714,4806,5013,5114,5244,5359,5463,5571,5627,5684,5758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,272,363,469,542,604,681,740,799,877,934,990,1049,1107,1161,1246,1302,1360,1414,1479,1571,1645,1721,1813,1875,1937,2016,2083,2149,2213,2282,2360,2421,2492,2559,2619,2698,2765,2848,2933,3007,3072,3148,3196,3260,3336,3414,3476,3540,3603,3683,3759,3837,3914", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "197,267,358,464,537,599,676,735,794,872,929,985,1044,1102,1156,1241,1297,1355,1409,1474,1566,1640,1716,1808,1870,1932,2011,2078,2144,2208,2277,2355,2416,2487,2554,2614,2693,2760,2843,2928,3002,3067,3143,3191,3255,3331,3409,3471,3535,3598,3678,3754,3832,3909,3978"}, "to": {"startLines": "2,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2875,3609,3700,3806,5763,5825,5969,6169,6293,6371,6428,6484,6543,6601,6655,6740,6796,6854,6908,6973,7270,7344,7420,7512,7574,7636,7715,7782,7848,7912,7981,8059,8120,8191,8258,8318,8397,8464,8547,8632,8706,8771,8847,8895,8959,9035,9113,9175,9239,9302,9382,9458,9536,9613", "endLines": "5,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "247,2940,3695,3801,3874,5820,5897,6023,6223,6366,6423,6479,6538,6596,6650,6735,6791,6849,6903,6968,7060,7339,7415,7507,7569,7631,7710,7777,7843,7907,7976,8054,8115,8186,8253,8313,8392,8459,8542,8627,8701,8766,8842,8890,8954,9030,9108,9170,9234,9297,9377,9453,9531,9608,9677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "35,36,37,38,39,40,41,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2945,3037,3138,3232,3326,3419,3513,10632", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3032,3133,3227,3321,3414,3508,3604,10728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,442,542,624,721,827,904,979,1070,1163,1260,1356,1450,1543,1638,1730,1821,1912,1990,2086,2181,2276,2373,2469,2567,2715,9966", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "342,437,537,619,716,822,899,974,1065,1158,1255,1351,1445,1538,1633,1725,1816,1907,1985,2081,2176,2271,2368,2464,2562,2710,2804,10040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "4811", "endColumns": "98", "endOffsets": "4905"}}]}]}