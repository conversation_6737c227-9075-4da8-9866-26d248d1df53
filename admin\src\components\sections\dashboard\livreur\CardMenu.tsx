﻿// Importer des fonctionnalités depuis un autre fichierimport { useState } fromimport { useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport IconButton fromimport IconButton from '@mui/material/IconButton';
// Importer des fonctionnalités depuis un autre fichierimport Menu fromimport Menu from '@mui/material/Menu';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from '@mui/material/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from '@mui/material/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport ListItemText fromimport ListItemText from '@mui/material/ListItemText';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface CardMenuProps
interface CardMenuProps {
  onEdit: () => void;
  onDelete: () => void;
}

// Créer une constanteconst CardMenu =
const CardMenu = ({ onEdit, onDelete }: CardMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  // Créer une constanteconst open =  const open = Boolean(anchorEl);

  // Créer une constanteconst handleActionButtonClick =
  const handleActionButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Créer une constanteconst handleActionMenuClose =
  const handleActionMenuClose = () => {
    setAnchorEl(null);
  };

  // Créer une constanteconst handleEdit =
  const handleEdit = () => {
    onEdit();
    handleActionMenuClose();
  };

  // Créer une constanteconst handleDelete =
  const handleDelete = () => {
    onDelete();
    handleActionMenuClose();
  };

  return (
    <>
      <IconButton
        edge="start"
        color="inherit"
        aria-label="card-menu"
        onClick={handleActionButtonClick}
        sx={{ bgcolor: 'transparent', '&:hover': { bgcolor: 'transparent' } }}
      >
        <IconifyIcon icon="ic:baseline-more-vert" color="text.disabled" />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleActionMenuClose}
        onClick={handleActionMenuClose}
        sx={{
          mt: 0.5,
          '& .MuiList-root': {
            width: 140,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEdit}>
          <ListItemIcon sx={{ mr: 1, fontSize: 'h5.fontSize' }}>
            <IconifyIcon icon="ic:baseline-edit" color="text.primary" />
          </ListItemIcon>
          <ListItemText>
            <Typography>Modifier</Typography>
          </ListItemText>
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <ListItemIcon sx={{ mr: 1, fontSize: 'h5.fontSize' }}>
            <IconifyIcon icon="ic:baseline-delete-outline" color="error.main" />
          </ListItemIcon>
          <ListItemText>
            <Typography color="error.main">Supprimer</Typography>
          </ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default CardMenu;
