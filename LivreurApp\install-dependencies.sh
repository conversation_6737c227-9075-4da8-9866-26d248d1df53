#!/bin/bash

echo "🔧 Installation des dépendances manquantes pour LivreurApp..."

# Vérifier si nous sommes dans le bon dossier
if [ ! -f "package.json" ]; then
    echo "❌ Erreur: package.json non trouvé. Assurez-vous d'être dans le dossier LivreurApp"
    exit 1
fi

echo "📦 Installation d'axios..."
npm install axios

echo "📦 Installation d'AsyncStorage..."
npm install @react-native-async-storage/async-storage

echo "🧹 Nettoyage du cache Metro..."
npx react-native start --reset-cache &

echo "✅ Installation terminée!"
echo ""
echo "🚀 Prochaines étapes:"
echo "1. Arrêtez Metro (Ctrl+C) si il démarre automatiquement"
echo "2. Redémarrez avec: npx react-native start --reset-cache"
echo "3. Dans un autre terminal: npx react-native run-android"
echo ""
echo "📱 Testez ensuite la connexion dans l'app mobile!"
