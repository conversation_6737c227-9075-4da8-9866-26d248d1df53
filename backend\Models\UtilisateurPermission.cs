// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;
// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations.Schema;using System.ComponentModel.DataAnnotations.Schema;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Models
namespace AxiaLivraisonAPI.Models
{
    
    // Classe publique qui définit un objetpublic class UtilisateurPermission    
    public class UtilisateurPermission
    {
        // Cette propriété est obligatoire[Required]        [Required]
        public int UtilisateurId { get; set; }

        [ForeignKey("UtilisateurId")]
        public Utilisateur Utilisateur { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public int PermissionId { get; set; }

        [ForeignKey("PermissionId")]
        public Permission Permission { get; set; }
    }
}