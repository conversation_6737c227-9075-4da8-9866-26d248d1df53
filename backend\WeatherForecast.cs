// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPInamespace AxiaLivraisonAPI
{
    // Classe publique qui définit un objetpublic class WeatherForecast    public class WeatherForecast
    {
        public DateOnly Date { get; set; }

        public int TemperatureC { get; set; }

        public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);

        public string? Summary { get; set; }
    }
}
