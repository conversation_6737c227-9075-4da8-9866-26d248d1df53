.back-link {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: var(--secondary-color);
  font-size: 16.5px;
  margin-bottom: 32px;
  cursor: pointer;
  border: none;
  background: none;
  padding: 4px 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.back-link:hover {
  color: var(--primary-color);
  transform: translateX(-1px);
}

.back-link svg {
  width: 26px;
  height: 26px;
  margin-right: 6px;
  position: relative;
  top: 1px;
}

.back-link-text {
  font-weight: 500;
  letter-spacing: -0.3px;
  position: relative;
  top: 1px;
  line-height: 1.2;
}

.locate-button {
  margin-top: -25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 4px 12px rgba(4, 190, 254, 0.3);
}

.locate-button:hover:not(.disabled) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(67, 24, 255, 0.4);
}

.locate-button.disabled {
  background: #9CA3AF !important;
  color: #6B7280 !important;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none !important;
}

.locate-button.disabled:hover {
  background: #9CA3AF !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Animations pour les nouvelles cartes */
.MuiPaper-root {
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.MuiPaper-root:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(67, 24, 255, 0.15) !important;
}

/* Style pour les cartes de statistiques */
.MuiCard-root {
  transition: all 0.3s ease !important;
}

.MuiCard-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(4, 190, 254, 0.2) !important;
}

/* Animation pour la barre de progression */
.MuiLinearProgress-root {
  animation: progressAnimation 2s ease-in-out;
}

@keyframes progressAnimation {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

/* Style pour les boutons d'action */
.MuiButton-root {
  transition: all 0.2s ease !important;
}

.MuiButton-root:hover {
  transform: translateY(-1px);
}



.locate-button svg {
  width: 24px;
  height: 24px;
}

/* Ajuster le style responsive */
@media (max-width: 600px) {
  .locate-button {
    padding: 8px 16px;
    font-size: 14px;
  }

  .locate-button svg {
    width: 20px;
    height: 20px;
  }
}

/* Styles personnalisés pour les sections */
.commande-details-section {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border: 1px solid rgba(67, 24, 255, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.commande-details-section:hover {
  border-color: rgba(67, 24, 255, 0.2);
  box-shadow: 0 8px 25px rgba(67, 24, 255, 0.1);
}

.financial-section {
  background: linear-gradient(135deg, #f0fdff 0%, #e6fbff 100%);
  border: 1px solid rgba(4, 190, 254, 0.2);
  border-radius: 20px;
}

.delivery-section {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border: 1px solid rgba(105, 70, 255, 0.2);
  border-radius: 20px;
}

.status-chip-blue {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  color: white;
  font-weight: 600;
  border-radius: 16px;
  padding: 6px 16px;
}

.progress-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border: 1px solid rgba(67, 24, 255, 0.1);
  border-radius: 20px;
}

/* Styles pour tous les éléments Paper */
.MuiPaper-root {
  border-radius: 20px !important;
}

/* Styles pour les cartes spécifiques */
.info-card {
  border-radius: 18px;
}

/* Styles pour les conteneurs internes */
.inner-content {
  border-radius: 16px;
}

/* Styles pour les éléments de détails */
.detail-item {
  border-radius: 12px;
  padding: 12px;
  margin: 8px 0;
  transition: all 0.3s ease;
}

.detail-item:hover {
  background-color: rgba(67, 24, 255, 0.05);
  transform: translateX(4px);
}















