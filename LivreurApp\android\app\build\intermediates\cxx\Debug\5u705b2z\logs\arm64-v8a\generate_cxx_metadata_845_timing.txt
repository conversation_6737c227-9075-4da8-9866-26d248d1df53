# C/C++ build system timings
generate_cxx_metadata
  [gap of 42ms]
  create-invalidation-state 397ms
  generate-prefab-packages
    [gap of 28ms]
    exec-prefab 8704ms
    [gap of 23ms]
  generate-prefab-packages completed in 8755ms
  execute-generate-process
    exec-configure 716ms
  execute-generate-process completed in 719ms
  [gap of 20ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 9963ms

