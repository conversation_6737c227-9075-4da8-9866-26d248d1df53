{"version": 3, "names": ["findComponentDescriptors", "packageRoot", "files", "glob", "sync", "cwd", "nodir", "ignore", "codegenComponent", "map", "filePath", "fs", "readFileSync", "path", "join", "extractComponentDescriptors", "filter", "Boolean", "Array", "from", "Set"], "sources": ["../../src/config/findComponentDescriptors.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport glob from 'glob';\nimport {extractComponentDescriptors} from './extractComponentDescriptors';\n\nexport function findComponentDescriptors(packageRoot: string) {\n  const files = glob.sync('**/+(*.js|*.jsx|*.ts|*.tsx)', {\n    cwd: packageRoot,\n    nodir: true,\n    ignore: '**/node_modules/**',\n  });\n  const codegenComponent = files\n    .map((filePath) =>\n      fs.readFileSync(path.join(packageRoot, filePath), 'utf8'),\n    )\n    .map(extractComponentDescriptors)\n    .filter(Boolean);\n\n  // Filter out duplicates as it happens that libraries contain multiple outputs due to package publishing.\n  // TODO: consider using \"codegenConfig\" to avoid this.\n  return Array.from(new Set(codegenComponent as string[]));\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA0E;AAEnE,SAASA,wBAAwB,CAACC,WAAmB,EAAE;EAC5D,MAAMC,KAAK,GAAGC,eAAI,CAACC,IAAI,CAAC,6BAA6B,EAAE;IACrDC,GAAG,EAAEJ,WAAW;IAChBK,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGN,KAAK,CAC3BO,GAAG,CAAEC,QAAQ,IACZC,aAAE,CAACC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACb,WAAW,EAAES,QAAQ,CAAC,EAAE,MAAM,CAAC,CAC1D,CACAD,GAAG,CAACM,wDAA2B,CAAC,CAChCC,MAAM,CAACC,OAAO,CAAC;;EAElB;EACA;EACA,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACZ,gBAAgB,CAAa,CAAC;AAC1D"}