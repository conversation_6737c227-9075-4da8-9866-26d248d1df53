// Importer une bibliothèque nécessaireusing System.ComponentModel.DataAnnotations;using System.ComponentModel.DataAnnotations;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.DTO
namespace AxiaLivraisonAPI.DTO
{
    // Classe publique qui définit un objetpublic class RegisterClientDTO    public class RegisterClientDTO
    {
        // Cette propriété est obligatoire[Required]        [Required]
        public string Nom { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public string Telephone { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        public string Identifiant { get; set; }

        // Cette propriété est obligatoire[Required]
        [Required]
        [MinLength(6)]
        public string MotDePasse { get; set; }
    }
}
