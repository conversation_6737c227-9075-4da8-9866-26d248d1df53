// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { useNavigate } fromimport { useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Menu fromimport Menu from '@mui/material/Menu';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Avatar fromimport Avatar from '@mui/material/Avatar';
// Importer des fonctionnalités depuis un autre fichierimport Divider fromimport Divider from '@mui/material/Divider';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from '@mui/material/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport ButtonBase fromimport ButtonBase from '@mui/material/ButtonBase';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from '@mui/material/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';
// Importer des fonctionnalités depuis un autre fichierimport paths fromimport paths from 'routes/paths';

// Définir une interface TypeScriptinterface MenuItems
interface MenuItems {
  id: number;
  title: string;
  icon: string;
  action?: () => void;
}

// Définir une interface TypeScriptinterface User
interface User {
  Id: number;
  Nom: string;
  Email: string;
  ImagePath?: string;
}

// Type guard for User
// Définir une fonctionfunction isUserfunction isUser(data: unknown): data is User {
  return (
    typeof data === 'object' && data !== null && 'Id' in data && 'Nom' in data && 'Email' in data
  );
}

// Créer une constanteconst ProfileMenu =
const ProfileMenu = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  // Créer une constanteconst navigate =  const navigate = useNavigate();
  // Créer une constanteconst open =  const open = Boolean(anchorEl);

  useEffect(() => {
    try {
      // Créer une constanteconst userData =      const userData = localStorage.getItem('user');

      if (!userData) {
        setLoadingError('No user data found');
        return;
      }

      // Créer une constanteconst parsedUser =
      const parsedUser = JSON.parse(userData);

      if (!isUser(parsedUser)) {
        throw new Error('Invalid user data format');
      }

      setUser(parsedUser);
    } catch (error) {
      console.error('Failed to load user data:', error);
      setLoadingError(error instanceof Error ? error.message : 'Unknown error');
      // Clear invalid data from storage
      localStorage.removeItem('user');
    }
  }, []);

  // Créer une constanteconst handleProfileClick =
  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Créer une constanteconst handleProfileMenuClose =
  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  // Créer une constanteconst handleLogout =
  const handleLogout = async () => {
    handleProfileMenuClose();
    navigate(paths.logout);
  };

  const menuItems: MenuItems[] = [
    {
      id: 1,
      title: 'Logout',
      icon: 'material-symbols:logout',
      action: handleLogout,
    },
  ];

  // Fallback content if there's an error loading user data
  if (loadingError) {
    return (
      <Avatar
        sx={{
          height: 44,
          width: 44,
          bgcolor: 'error.main',
        }}
      >
        !
      </Avatar>
    );
  }

  return (
    <>
      <ButtonBase
        onClick={handleProfileClick}
        aria-controls={open ? 'account-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        disableRipple
      >
        <Avatar
          src={`http://localhost:5283${user?.ImagePath || '/default_profile_image.png'}`}
          sx={{
            height: 44,
            width: 44,
            bgcolor: user ? 'primary.main' : 'grey.400',
          }}
        />
      </ButtonBase>

      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        sx={{
          mt: 1.5,
          '& .MuiList-root': {
            p: 0,
            width: 230,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box p={1}>
          <MenuItem onClick={handleProfileMenuClose} sx={{ '&:hover': { bgcolor: 'info.dark' } }}>
            <Avatar
              src={`http://localhost:5283${user?.ImagePath || '/default_profile_image.png'}`}
              sx={{ mr: 1, height: 42, width: 42 }}
            />
            <Stack direction="column">
              <Typography variant="body2" color="text.primary" fontWeight={600}>
                {user?.Nom || 'Guest User'}
              </Typography>
              <Typography variant="caption" color="text.secondary" fontWeight={400}>
                {user?.Email || '<EMAIL>'}
              </Typography>
            </Stack>
          </MenuItem>
        </Box>

        <Divider sx={{ my: 0 }} />

        <Box p={1}>
          {menuItems.map((item) => (
            <MenuItem key={item.id} onClick={item.action || handleProfileMenuClose} sx={{ py: 1 }}>
              <ListItemIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 'h5.fontSize' }}>
                <IconifyIcon icon={item.icon} />
              </ListItemIcon>
              <Typography variant="body2" color="text.secondary" fontWeight={500}>
                {item.title}
              </Typography>
            </MenuItem>
          ))}
        </Box>
      </Menu>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default ProfileMenu;
