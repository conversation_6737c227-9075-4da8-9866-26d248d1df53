// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';
// Importer des fonctionnalités depuis un autre fichierimport scrollbar fromimport scrollbar from 'theme/styles/scrollbar';
// Importer des fonctionnalités depuis un autre fichierimport echart fromimport echart from 'theme/styles/echart';

const CssBaseline: Components<Omit<Theme, 'components'>>['MuiCssBaseline'] = {
  defaultProps: {},
  styleOverrides: (theme) => ({
    '*, *::before, *::after': {
      margin: 0,
      padding: 0,
    },
    html: {
      scrollBehavior: 'smooth',
    },
    body: {
      fontVariantLigatures: 'none',
      backgroundColor: theme.palette.info.main,
      ...scrollbar(theme),
    },
    ...echart(),
  }),
};

// Exporter comme élément principal de ce fichierexport default
export default CssBaseline;
