import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  DialogContentText,
} from '@mui/material';
import axios from 'axios';
import { Utilisateur } from './types';

interface LivreurTableProps {
  livreurs: Utilisateur[];
  onEditLivreur: (livreur: Utilisateur) => void;
  onDeleteLivreur: (id: number) => void;
}

const LivreurTable: React.FC<LivreurTableProps> = ({
  livreurs,
  onEditLivreur,
  onDeleteLivreur,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [livreurToDelete, setLivreurToDelete] = useState<Utilisateur | null>(null);
  const [forceDeleteDialogOpen, setForceDeleteDialogOpen] = useState(false);
  const API_URL = 'http://localhost:5283';

  const handleDeleteClick = (livreur: Utilisateur) => {
    setLivreurToDelete(livreur);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!livreurToDelete) return;

    try {
      await axios.delete(`${API_URL}/api/utilisateurs/supprimer/${livreurToDelete.id}`);
      onDeleteLivreur(livreurToDelete.id);
      setDeleteDialogOpen(false);
      setLivreurToDelete(null);
    } catch (error: unknown) {
      console.error('Erreur lors de la suppression:', error);

      // Si le livreur a des commandes, proposer la suppression forcée
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number } };
        if (axiosError.response?.status === 400) {
          setDeleteDialogOpen(false);
          setForceDeleteDialogOpen(true);
        }
      }
    }
  };

  const confirmForceDelete = async () => {
    if (!livreurToDelete) return;

    try {
      await axios.delete(`${API_URL}/api/utilisateurs/supprimer-force/${livreurToDelete.id}`);
      onDeleteLivreur(livreurToDelete.id);
      setForceDeleteDialogOpen(false);
      setLivreurToDelete(null);
    } catch (error) {
      console.error('Erreur lors de la suppression forcée:', error);
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setForceDeleteDialogOpen(false);
    setLivreurToDelete(null);
  };

  return (
    <>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>Image</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Nom</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Téléphone</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Identifiant</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {livreurs.map((livreur) => (
              <TableRow
                key={livreur.id}
                hover
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell>
                  {livreur.imagePath && (
                    <img
                      src={`http://localhost:5283${livreur.imagePath}`}
                      alt={livreur.nom}
                      style={{ width: '50px', height: '50px', borderRadius: '50%' }}
                    />
                  )}
                </TableCell>
                <TableCell>{livreur.nom}</TableCell>
                <TableCell>{livreur.email}</TableCell>
                <TableCell>{livreur.telephone}</TableCell>
                <TableCell>{livreur.identifiant}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={() => onEditLivreur(livreur)}
                      sx={{ textTransform: 'none' }}
                    >
                      Modifier
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      onClick={() => handleDeleteClick(livreur)}
                      sx={{ textTransform: 'none' }}
                    >
                      Supprimer
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Êtes-vous sûr de vouloir supprimer le livreur <strong>{livreurToDelete?.nom}</strong> ?
            <br />
            Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de suppression forcée */}
      <Dialog
        open={forceDeleteDialogOpen}
        onClose={cancelDelete}
        aria-labelledby="force-delete-dialog-title"
        aria-describedby="force-delete-dialog-description"
      >
        <DialogTitle id="force-delete-dialog-title">Suppression avec données associées</DialogTitle>
        <DialogContent>
          <DialogContentText id="force-delete-dialog-description">
            Le livreur <strong>{livreurToDelete?.nom}</strong> est associé à des commandes
            existantes.
            <br />
            <br />
            <Typography color="error" variant="body2">
              ⚠️ Attention : Supprimer ce livreur supprimera également toutes ses commandes et
              rapports associés. Cette action est irréversible.
            </Typography>
            <br />
            Voulez-vous continuer avec la suppression forcée ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button onClick={confirmForceDelete} color="error" variant="contained">
            Supprimer tout
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LivreurTable;
