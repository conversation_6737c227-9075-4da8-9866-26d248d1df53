﻿// Importer des fonctionnalités depuis un autre fichierimport { createTheme } fromimport { createTheme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport type {} fromimport type {} from '@mui/x-data-grid/themeAugmentation';
// Importer des fonctionnalités depuis un autre fichierimport type {} fromimport type {} from '@mui/x-date-pickers/themeAugmentation';
// Importer des fonctionnalités depuis un autre fichierimport palette fromimport palette from './palette';
// Importer des fonctionnalités depuis un autre fichierimport typography fromimport typography from './typography';
// Importer des fonctionnalités depuis un autre fichierimport customShadows fromimport customShadows from './shadows';
// Importer des fonctionnalités depuis un autre fichierimport CssBaseline fromimport CssBaseline from './components/utils/CssBaseline';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from './components/layout/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from './components/surfaces/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Button fromimport Button from './components/buttons/Button';
// Importer des fonctionnalités depuis un autre fichierimport ButtonBase fromimport ButtonBase from './components/buttons/ButtonBase';
// Importer des fonctionnalités depuis un autre fichierimport IconButton fromimport IconButton from './components/buttons/IconButton';
// Importer des fonctionnalités depuis un autre fichierimport Toolbar fromimport Toolbar from './components/buttons/Toolbar';
// Importer des fonctionnalités depuis un autre fichierimport Chip fromimport Chip from './components/data-display/Chip';
// Importer des fonctionnalités depuis un autre fichierimport Badge fromimport Badge from './components/data-display/Badge';
// Importer des fonctionnalités depuis un autre fichierimport Checkbox fromimport Checkbox from './components/inputs/Checkbox';
// Importer des fonctionnalités depuis un autre fichierimport FilledInput fromimport FilledInput from './components/inputs/FilledInput';
// Importer des fonctionnalités depuis un autre fichierimport FormControlLabel fromimport FormControlLabel from './components/inputs/FormControlLabel';
// Importer des fonctionnalités depuis un autre fichierimport InputAdornment fromimport InputAdornment from './components/inputs/InputAdornment';
// Importer des fonctionnalités depuis un autre fichierimport InputBase fromimport InputBase from './components/inputs/InputBase';
// Importer des fonctionnalités depuis un autre fichierimport OutlinedInput fromimport OutlinedInput from './components/inputs/OutlinedInput';
// Importer des fonctionnalités depuis un autre fichierimport Select fromimport Select from './components/inputs/Select';
// Importer des fonctionnalités depuis un autre fichierimport Collapse fromimport Collapse from './components/list/Collapse';
// Importer des fonctionnalités depuis un autre fichierimport List fromimport List from './components/list/List';
// Importer des fonctionnalités depuis un autre fichierimport ListItemButton fromimport ListItemButton from './components/list/ListItemButton';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from './components/list/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport ListItemText fromimport ListItemText from './components/list/ListItemText';
// Importer des fonctionnalités depuis un autre fichierimport MenuItem fromimport MenuItem from './components/list/MenuItem';
// Importer des fonctionnalités depuis un autre fichierimport AppBar fromimport AppBar from './components/navigation/AppBar';
// Importer des fonctionnalités depuis un autre fichierimport Drawer fromimport Drawer from './components/navigation/Drawer';
// Importer des fonctionnalités depuis un autre fichierimport Link fromimport Link from './components/navigation/Link';
// Importer des fonctionnalités depuis un autre fichierimport YearCalendar fromimport YearCalendar from './components/date-picker/YearCalendar';
// Importer des fonctionnalités depuis un autre fichierimport MonthCalendar fromimport MonthCalendar from './components/date-picker/MonthCalendar';
// Importer des fonctionnalités depuis un autre fichierimport PaginationItem fromimport PaginationItem from './components/pagination/PaginationItem';
// Importer des fonctionnalités depuis un autre fichierimport DataGrid fromimport DataGrid from './components/data-grid/DataGrid';
// Importer des fonctionnalités depuis un autre fichierimport Avatar fromimport Avatar from './components/data-display/Avatar';
// Importer des fonctionnalités depuis un autre fichierimport AvatarGroup fromimport AvatarGroup from './components/data-display/AvatarGroup';
// Importer des fonctionnalités depuis un autre fichierimport Card fromimport Card from './components/cards/Card';
// Importer des fonctionnalités depuis un autre fichierimport CardMedia fromimport CardMedia from './components/cards/CardMedia';
// Importer des fonctionnalités depuis un autre fichierimport CardContent fromimport CardContent from './components/cards/CardContent';
// Importer des fonctionnalités depuis un autre fichierimport DateCalendar fromimport DateCalendar from './components/date-picker/DateCalendar';
// Importer des fonctionnalités depuis un autre fichierimport InputLabel fromimport InputLabel from './components/inputs/InputLabel';
// Importer des fonctionnalités depuis un autre fichierimport Divider fromimport Divider from './components/data-display/Divider';

export const theme = createTheme({
  palette,
  typography,
  customShadows,
  mixins: {
    toolbar: {
      minHeight: 130,
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1420,
      xl: 1780,
    },
  },
  components: {
    MuiStack: Stack,
    MuiPaper: Paper,
    MuiButton: Button,
    MuiButtonBase: ButtonBase,
    MuiIconButton: IconButton,
    MuiToolbar: Toolbar,
    MuiBadge: Badge,
    MuiChip: Chip,
    MuiCheckbox: Checkbox,
    MuiFilledInput: FilledInput,
    MuiFormControlLabel: FormControlLabel,
    MuiInputAdornment: InputAdornment,
    MuiInputBase: InputBase,
    MuiOutlinedInput: OutlinedInput,
    MuiSelect: Select,
    MuiCollapse: Collapse,
    MuiList: List,
    MuiListItemButton: ListItemButton,
    MuiListItemIcon: ListItemIcon,
    MuiListItemText: ListItemText,
    MuiMenuItem: MenuItem,
    MuiInputLabel: InputLabel,
    MuiAppBar: AppBar,
    MuiDrawer: Drawer,
    MuiLink: Link,
    MuiCard: Card,
    MuiCardMedia: CardMedia,
    MuiCardContent: CardContent,
    MuiDivider: Divider,
    MuiAvatar: Avatar,
    MuiDataGrid: DataGrid,
    MuiAvatarGroup: AvatarGroup,
    MuiDateCalendar: DateCalendar,
    MuiMonthCalendar: MonthCalendar,
    MuiYearCalendar: YearCalendar,
    MuiPaginationItem: PaginationItem,
    MuiCssBaseline: CssBaseline,
  },
});
