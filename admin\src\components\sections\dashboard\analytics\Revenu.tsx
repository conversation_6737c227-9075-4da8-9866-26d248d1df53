﻿// Importer des fonctionnalités depuis un autre fichierimport { useState, useEffect } fromimport { useState, useEffect } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport axios fromimport axios from 'axios';
// Importer des fonctionnalités depuis un autre fichierimport Paper fromimport Paper from '@mui/material/Paper';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface RevenusMois
interface RevenusMois {
  revenusMois: number;
  pourcentageChangement: number;
}

// Créer une constanteconst Revenu =
const Revenu = () => {
  const [revenusMois, setRevenusMois] = useState<number>(0);
  const [pourcentageChangement, setPourcentageChangement] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // Créer une constanteconst API_URL =
  const API_URL = 'http://localhost:5283';

  useEffect(() => {
    // Créer une constanteconst fetchRevenusMois =    const fetchRevenusMois = async () => {
      try {
        setLoading(true);
        // Créer une constanteconst response =        const response = await axios.get<RevenusMois>(`${API_URL}/api/statistiques/revenus-mois`);
        setRevenusMois(response.data.revenusMois);
        setPourcentageChangement(response.data.pourcentageChangement);
      } catch (err) {
        console.error('Erreur lors de la récupération des revenus du mois:', err);
        setRevenusMois(0);
        setPourcentageChangement(0);
      } finally {
        setLoading(false);
      }
    };

    fetchRevenusMois();
  }, []);

  // Fonction pour formater le montant en devise
  // Créer une constanteconst formatCurrency =  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 3,
    }).format(amount);
  };

  return (
    <Stack component={Paper} p={2.5} alignItems="center" spacing={2.25} height={100}>
      <Stack
        alignItems="center"
        justifyContent="center"
        height={56}
        width={56}
        bgcolor="info.main"
        borderRadius="50%"
      >
        <IconifyIcon icon="ic:round-attach-money" fontSize="h2.fontSize" color="primary.main" />
      </Stack>
      <div>
        <Typography variant="body2" color="text.disabled" noWrap>
          Revenus du Mois
        </Typography>
        <Stack alignItems="center">
          <Typography mt={0.25} variant="h3">
            {loading ? '...' : formatCurrency(revenusMois)}
          </Typography>
        </Stack>
        {!loading && (
          <Typography
            variant="caption"
            color={pourcentageChangement >= 0 ? 'success.main' : 'error.main'}
            fontWeight={700}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <IconifyIcon
              icon={
                pourcentageChangement >= 0
                  ? 'ic:baseline-arrow-drop-up'
                  : 'ic:baseline-arrow-drop-down'
              }
              fontSize="h6.fontSize"
            />
            {Math.abs(pourcentageChangement)}%
          </Typography>
        )}
      </div>
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default Revenu;
