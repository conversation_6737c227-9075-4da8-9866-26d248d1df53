{"version": 3, "names": ["_core", "require", "_globalTypes", "_constEnum", "getFirstIdentifier", "node", "t", "isIdentifier", "left", "transpileNamespace", "path", "allowNamespaces", "declare", "id", "type", "remove", "get", "buildCodeFrameError", "name", "value", "handleNested", "program", "findParent", "p", "isProgram", "registerGlobalType", "scope", "hasOwnBinding", "replaceWith", "registerDeclaration", "replaceWithMultiple", "getDeclaration", "variableDeclaration", "variableDeclarator", "identifier", "getMemberExpression", "itemName", "memberExpression", "handleVariableDeclaration", "hub", "kind", "file", "declarations", "every", "declarator", "init", "assignmentExpression", "bindingIdentifiers", "getBindingIdentifiers", "assignments", "idName", "push", "cloneNode", "expressionStatement", "sequenceExpression", "buildNestedAmbientModuleError", "buildError", "Error", "parentExport", "names", "Set", "realName", "generateUid", "body", "namespaceTopLevel", "isTSModuleBlock", "exportNamedDeclaration", "isEmpty", "i", "length", "subNode", "transformed", "moduleName", "has", "add", "splice", "isTypeScript", "declaration", "EXPORTED_CONST_ENUMS_IN_NAMESPACE", "nodes", "fallthroughValue", "objectExpression", "memberExpr", "template", "expression", "ast", "statement"], "sources": ["../src/namespace.ts"], "sourcesContent": ["import { template, types as t, type NodePath } from \"@babel/core\";\n\nimport { registerGlobalType } from \"./global-types.ts\";\nimport { EXPORTED_CONST_ENUMS_IN_NAMESPACE } from \"./const-enum.ts\";\n\nexport function getFirstIdentifier(node: t.TSEntityName): t.Identifier {\n  if (t.isIdentifier(node)) {\n    return node;\n  }\n  // In Babel 8 TSEntityName also includes ThisExpression, however, a namespace\n  // id must not be a ThisExpression or a TSQualifiedName { left: ThisExpression }.\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n  return getFirstIdentifier((node as t.TSQualifiedName).left);\n}\n\nexport default function transpileNamespace(\n  path: NodePath<t.TSModuleDeclaration>,\n  allowNamespaces: boolean,\n) {\n  if (path.node.declare || path.node.id.type === \"StringLiteral\") {\n    path.remove();\n    return;\n  }\n\n  if (!allowNamespaces) {\n    throw path\n      .get(\"id\")\n      .buildCodeFrameError(\n        \"Namespace not marked type-only declare.\" +\n          \" Non-declarative namespaces are only supported experimentally in Babel.\" +\n          \" To enable and review caveats see:\" +\n          \" https://babeljs.io/docs/en/babel-plugin-transform-typescript\",\n      );\n  }\n\n  const name = getFirstIdentifier(path.node.id).name;\n  const value = handleNested(path, path.node);\n  if (value === null) {\n    // This means that `path` is a type-only namespace.\n    // We call `registerGlobalType` here to allow it to be stripped.\n    const program = path.findParent(p => p.isProgram());\n    registerGlobalType(program.scope, name);\n\n    path.remove();\n  } else if (path.scope.hasOwnBinding(name)) {\n    path.replaceWith(value);\n  } else {\n    path.scope.registerDeclaration(\n      path.replaceWithMultiple([getDeclaration(name), value])[0],\n    );\n  }\n}\n\nfunction getDeclaration(name: string) {\n  return t.variableDeclaration(\"let\", [\n    t.variableDeclarator(t.identifier(name)),\n  ]);\n}\n\nfunction getMemberExpression(name: string, itemName: string) {\n  return t.memberExpression(t.identifier(name), t.identifier(itemName));\n}\n\n/**\n * Convert export const foo = 1 to Namespace.foo = 1;\n *\n * @param {t.VariableDeclaration} node given variable declaration, e.g. `const foo = 1`\n * @param {string} name the generated unique namespace member name\n * @param {*} hub An instance implements HubInterface defined in `@babel/traverse` that can throw a code frame error\n */\nfunction handleVariableDeclaration(\n  node: t.VariableDeclaration,\n  name: string,\n  hub: any,\n): t.Statement[] {\n  if (node.kind !== \"const\") {\n    throw hub.file.buildCodeFrameError(\n      node,\n      \"Namespaces exporting non-const are not supported by Babel.\" +\n        \" Change to const or see:\" +\n        \" https://babeljs.io/docs/en/babel-plugin-transform-typescript\",\n    );\n  }\n  const { declarations } = node;\n  if (\n    declarations.every(\n      (declarator): declarator is t.VariableDeclarator & { id: t.Identifier } =>\n        t.isIdentifier(declarator.id),\n    )\n  ) {\n    // `export const a = 1` transforms to `const a = N.a = 1`, the output\n    // is smaller than `const a = 1; N.a = a`;\n    for (const declarator of declarations) {\n      declarator.init = t.assignmentExpression(\n        \"=\",\n        getMemberExpression(name, declarator.id.name),\n        declarator.init,\n      );\n    }\n    return [node];\n  }\n  // Now we have pattern in declarators\n  // `export const [a] = 1` transforms to `const [a] = 1; N.a = a`\n  const bindingIdentifiers = t.getBindingIdentifiers(node);\n  const assignments = [];\n  // getBindingIdentifiers returns an object without prototype.\n  // eslint-disable-next-line guard-for-in\n  for (const idName in bindingIdentifiers) {\n    assignments.push(\n      t.assignmentExpression(\n        \"=\",\n        getMemberExpression(name, idName),\n        t.cloneNode(bindingIdentifiers[idName]),\n      ),\n    );\n  }\n  return [node, t.expressionStatement(t.sequenceExpression(assignments))];\n}\n\nfunction buildNestedAmbientModuleError(path: NodePath, node: t.Node) {\n  return path.hub.buildError(\n    node,\n    \"Ambient modules cannot be nested in other modules or namespaces.\",\n    Error,\n  );\n}\n\nfunction handleNested(\n  path: NodePath,\n  node: t.TSModuleDeclaration,\n  parentExport?: t.Expression,\n): t.Statement | null {\n  const names = new Set();\n  const realName =\n    !process.env.BABEL_8_BREAKING || t.isIdentifier(node.id)\n      ? (node.id as t.Identifier)\n      : getFirstIdentifier(node.id as unknown as t.TSQualifiedName);\n\n  const name = path.scope.generateUid(realName.name);\n\n  const body = node.body;\n  let id = node.id;\n  let namespaceTopLevel: t.Statement[];\n  if (process.env.BABEL_8_BREAKING) {\n    if (t.isTSQualifiedName(id)) {\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      namespaceTopLevel = body.body;\n      while (t.isTSQualifiedName(id)) {\n        namespaceTopLevel = [\n          t.exportNamedDeclaration(\n            t.tsModuleDeclaration(\n              // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n              t.cloneNode(id.right),\n              t.tsModuleBlock(namespaceTopLevel),\n            ),\n          ),\n        ];\n\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n        id = id.left;\n      }\n    } else {\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      namespaceTopLevel = body.body;\n    }\n  } else {\n    namespaceTopLevel = t.isTSModuleBlock(body)\n      ? body.body\n      : // We handle `namespace X.Y {}` as if it was\n        //   namespace X {\n        //     export namespace Y {}\n        //   }\n        [t.exportNamedDeclaration(body)];\n  }\n\n  let isEmpty = true;\n\n  for (let i = 0; i < namespaceTopLevel.length; i++) {\n    const subNode = namespaceTopLevel[i];\n\n    // The first switch is mainly to detect name usage. Only export\n    // declarations require further transformation.\n    switch (subNode.type) {\n      case \"TSModuleDeclaration\": {\n        if (!t.isIdentifier(subNode.id)) {\n          throw buildNestedAmbientModuleError(path, subNode);\n        }\n\n        const transformed = handleNested(path, subNode);\n        if (transformed !== null) {\n          isEmpty = false;\n          const moduleName = subNode.id.name;\n          if (names.has(moduleName)) {\n            namespaceTopLevel[i] = transformed;\n          } else {\n            names.add(moduleName);\n            namespaceTopLevel.splice(\n              i++,\n              1,\n              getDeclaration(moduleName),\n              transformed,\n            );\n          }\n        }\n        continue;\n      }\n      case \"TSEnumDeclaration\":\n      case \"FunctionDeclaration\":\n      case \"ClassDeclaration\":\n        isEmpty = false;\n        names.add(subNode.id.name);\n        continue;\n      case \"VariableDeclaration\": {\n        isEmpty = false;\n        // getBindingIdentifiers returns an object without prototype.\n        // eslint-disable-next-line guard-for-in\n        for (const name in t.getBindingIdentifiers(subNode)) {\n          names.add(name);\n        }\n        continue;\n      }\n      default:\n        isEmpty &&= t.isTypeScript(subNode);\n        // Neither named declaration nor export, continue to next item.\n        continue;\n      case \"ExportNamedDeclaration\":\n      // Export declarations get parsed using the next switch.\n    }\n\n    if (\"declare\" in subNode.declaration && subNode.declaration.declare) {\n      continue;\n    }\n\n    // Transform the export declarations that occur inside of a namespace.\n    switch (subNode.declaration.type) {\n      case \"TSEnumDeclaration\":\n        EXPORTED_CONST_ENUMS_IN_NAMESPACE.add(subNode.declaration);\n      // fallthrough\n      case \"FunctionDeclaration\":\n      case \"ClassDeclaration\": {\n        isEmpty = false;\n        const itemName = subNode.declaration.id.name;\n        names.add(itemName);\n        namespaceTopLevel.splice(\n          i++,\n          1,\n          subNode.declaration,\n          t.expressionStatement(\n            t.assignmentExpression(\n              \"=\",\n              getMemberExpression(name, itemName),\n              t.identifier(itemName),\n            ),\n          ),\n        );\n        break;\n      }\n      case \"VariableDeclaration\": {\n        isEmpty = false;\n        const nodes = handleVariableDeclaration(\n          subNode.declaration,\n          name,\n          path.hub,\n        );\n        namespaceTopLevel.splice(i, nodes.length, ...nodes);\n        i += nodes.length - 1;\n        break;\n      }\n      case \"TSModuleDeclaration\": {\n        if (!t.isIdentifier(subNode.declaration.id)) {\n          throw buildNestedAmbientModuleError(path, subNode.declaration);\n        }\n\n        const transformed = handleNested(\n          path,\n          subNode.declaration,\n          t.identifier(name),\n        );\n        if (transformed !== null) {\n          isEmpty = false;\n          const moduleName = subNode.declaration.id.name;\n          if (names.has(moduleName)) {\n            namespaceTopLevel[i] = transformed;\n          } else {\n            names.add(moduleName);\n            namespaceTopLevel.splice(\n              i++,\n              1,\n              getDeclaration(moduleName),\n              transformed,\n            );\n          }\n        } else {\n          namespaceTopLevel.splice(i, 1);\n          i--;\n        }\n      }\n    }\n  }\n\n  if (isEmpty) return null;\n\n  // {}\n  let fallthroughValue: t.Expression = t.objectExpression([]);\n\n  if (parentExport) {\n    const memberExpr = t.memberExpression(parentExport, realName);\n    fallthroughValue = template.expression.ast`\n      ${t.cloneNode(memberExpr)} ||\n        (${t.cloneNode(memberExpr)} = ${fallthroughValue})\n    `;\n  }\n\n  return template.statement.ast`\n    (function (${t.identifier(name)}) {\n      ${namespaceTopLevel}\n    })(${realName} || (${t.cloneNode(realName)} = ${fallthroughValue}));\n  `;\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEO,SAASG,kBAAkBA,CAACC,IAAoB,EAAgB;EACrE,IAAIC,WAAC,CAACC,YAAY,CAACF,IAAI,CAAC,EAAE;IACxB,OAAOA,IAAI;EACb;EAIA,OAAOD,kBAAkB,CAAEC,IAAI,CAAuBG,IAAI,CAAC;AAC7D;AAEe,SAASC,kBAAkBA,CACxCC,IAAqC,EACrCC,eAAwB,EACxB;EACA,IAAID,IAAI,CAACL,IAAI,CAACO,OAAO,IAAIF,IAAI,CAACL,IAAI,CAACQ,EAAE,CAACC,IAAI,KAAK,eAAe,EAAE;IAC9DJ,IAAI,CAACK,MAAM,CAAC,CAAC;IACb;EACF;EAEA,IAAI,CAACJ,eAAe,EAAE;IACpB,MAAMD,IAAI,CACPM,GAAG,CAAC,IAAI,CAAC,CACTC,mBAAmB,CAClB,yCAAyC,GACvC,yEAAyE,GACzE,oCAAoC,GACpC,+DACJ,CAAC;EACL;EAEA,MAAMC,IAAI,GAAGd,kBAAkB,CAACM,IAAI,CAACL,IAAI,CAACQ,EAAE,CAAC,CAACK,IAAI;EAClD,MAAMC,KAAK,GAAGC,YAAY,CAACV,IAAI,EAAEA,IAAI,CAACL,IAAI,CAAC;EAC3C,IAAIc,KAAK,KAAK,IAAI,EAAE;IAGlB,MAAME,OAAO,GAAGX,IAAI,CAACY,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IACnD,IAAAC,+BAAkB,EAACJ,OAAO,CAACK,KAAK,EAAER,IAAI,CAAC;IAEvCR,IAAI,CAACK,MAAM,CAAC,CAAC;EACf,CAAC,MAAM,IAAIL,IAAI,CAACgB,KAAK,CAACC,aAAa,CAACT,IAAI,CAAC,EAAE;IACzCR,IAAI,CAACkB,WAAW,CAACT,KAAK,CAAC;EACzB,CAAC,MAAM;IACLT,IAAI,CAACgB,KAAK,CAACG,mBAAmB,CAC5BnB,IAAI,CAACoB,mBAAmB,CAAC,CAACC,cAAc,CAACb,IAAI,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3D,CAAC;EACH;AACF;AAEA,SAASY,cAAcA,CAACb,IAAY,EAAE;EACpC,OAAOZ,WAAC,CAAC0B,mBAAmB,CAAC,KAAK,EAAE,CAClC1B,WAAC,CAAC2B,kBAAkB,CAAC3B,WAAC,CAAC4B,UAAU,CAAChB,IAAI,CAAC,CAAC,CACzC,CAAC;AACJ;AAEA,SAASiB,mBAAmBA,CAACjB,IAAY,EAAEkB,QAAgB,EAAE;EAC3D,OAAO9B,WAAC,CAAC+B,gBAAgB,CAAC/B,WAAC,CAAC4B,UAAU,CAAChB,IAAI,CAAC,EAAEZ,WAAC,CAAC4B,UAAU,CAACE,QAAQ,CAAC,CAAC;AACvE;AASA,SAASE,yBAAyBA,CAChCjC,IAA2B,EAC3Ba,IAAY,EACZqB,GAAQ,EACO;EACf,IAAIlC,IAAI,CAACmC,IAAI,KAAK,OAAO,EAAE;IACzB,MAAMD,GAAG,CAACE,IAAI,CAACxB,mBAAmB,CAChCZ,IAAI,EACJ,4DAA4D,GAC1D,0BAA0B,GAC1B,+DACJ,CAAC;EACH;EACA,MAAM;IAAEqC;EAAa,CAAC,GAAGrC,IAAI;EAC7B,IACEqC,YAAY,CAACC,KAAK,CACfC,UAAU,IACTtC,WAAC,CAACC,YAAY,CAACqC,UAAU,CAAC/B,EAAE,CAChC,CAAC,EACD;IAGA,KAAK,MAAM+B,UAAU,IAAIF,YAAY,EAAE;MACrCE,UAAU,CAACC,IAAI,GAAGvC,WAAC,CAACwC,oBAAoB,CACtC,GAAG,EACHX,mBAAmB,CAACjB,IAAI,EAAE0B,UAAU,CAAC/B,EAAE,CAACK,IAAI,CAAC,EAC7C0B,UAAU,CAACC,IACb,CAAC;IACH;IACA,OAAO,CAACxC,IAAI,CAAC;EACf;EAGA,MAAM0C,kBAAkB,GAAGzC,WAAC,CAAC0C,qBAAqB,CAAC3C,IAAI,CAAC;EACxD,MAAM4C,WAAW,GAAG,EAAE;EAGtB,KAAK,MAAMC,MAAM,IAAIH,kBAAkB,EAAE;IACvCE,WAAW,CAACE,IAAI,CACd7C,WAAC,CAACwC,oBAAoB,CACpB,GAAG,EACHX,mBAAmB,CAACjB,IAAI,EAAEgC,MAAM,CAAC,EACjC5C,WAAC,CAAC8C,SAAS,CAACL,kBAAkB,CAACG,MAAM,CAAC,CACxC,CACF,CAAC;EACH;EACA,OAAO,CAAC7C,IAAI,EAAEC,WAAC,CAAC+C,mBAAmB,CAAC/C,WAAC,CAACgD,kBAAkB,CAACL,WAAW,CAAC,CAAC,CAAC;AACzE;AAEA,SAASM,6BAA6BA,CAAC7C,IAAc,EAAEL,IAAY,EAAE;EACnE,OAAOK,IAAI,CAAC6B,GAAG,CAACiB,UAAU,CACxBnD,IAAI,EACJ,kEAAkE,EAClEoD,KACF,CAAC;AACH;AAEA,SAASrC,YAAYA,CACnBV,IAAc,EACdL,IAA2B,EAC3BqD,YAA2B,EACP;EACpB,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB,MAAMC,QAAQ,GAEPxD,IAAI,CAACQ,EACqD;EAEjE,MAAMK,IAAI,GAAGR,IAAI,CAACgB,KAAK,CAACoC,WAAW,CAACD,QAAQ,CAAC3C,IAAI,CAAC;EAElD,MAAM6C,IAAI,GAAG1D,IAAI,CAAC0D,IAAI;EACtB,IAAIlD,EAAE,GAAGR,IAAI,CAACQ,EAAE;EAChB,IAAImD,iBAAgC;EAuB7B;IACLA,iBAAiB,GAAG1D,WAAC,CAAC2D,eAAe,CAACF,IAAI,CAAC,GACvCA,IAAI,CAACA,IAAI,GAKT,CAACzD,WAAC,CAAC4D,sBAAsB,CAACH,IAAI,CAAC,CAAC;EACtC;EAEA,IAAII,OAAO,GAAG,IAAI;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,iBAAiB,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACjD,MAAME,OAAO,GAAGN,iBAAiB,CAACI,CAAC,CAAC;IAIpC,QAAQE,OAAO,CAACxD,IAAI;MAClB,KAAK,qBAAqB;QAAE;UAC1B,IAAI,CAACR,WAAC,CAACC,YAAY,CAAC+D,OAAO,CAACzD,EAAE,CAAC,EAAE;YAC/B,MAAM0C,6BAA6B,CAAC7C,IAAI,EAAE4D,OAAO,CAAC;UACpD;UAEA,MAAMC,WAAW,GAAGnD,YAAY,CAACV,IAAI,EAAE4D,OAAO,CAAC;UAC/C,IAAIC,WAAW,KAAK,IAAI,EAAE;YACxBJ,OAAO,GAAG,KAAK;YACf,MAAMK,UAAU,GAAGF,OAAO,CAACzD,EAAE,CAACK,IAAI;YAClC,IAAIyC,KAAK,CAACc,GAAG,CAACD,UAAU,CAAC,EAAE;cACzBR,iBAAiB,CAACI,CAAC,CAAC,GAAGG,WAAW;YACpC,CAAC,MAAM;cACLZ,KAAK,CAACe,GAAG,CAACF,UAAU,CAAC;cACrBR,iBAAiB,CAACW,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDrC,cAAc,CAACyC,UAAU,CAAC,EAC1BD,WACF,CAAC;YACH;UACF;UACA;QACF;MACA,KAAK,mBAAmB;MACxB,KAAK,qBAAqB;MAC1B,KAAK,kBAAkB;QACrBJ,OAAO,GAAG,KAAK;QACfR,KAAK,CAACe,GAAG,CAACJ,OAAO,CAACzD,EAAE,CAACK,IAAI,CAAC;QAC1B;MACF,KAAK,qBAAqB;QAAE;UAC1BiD,OAAO,GAAG,KAAK;UAGf,KAAK,MAAMjD,IAAI,IAAIZ,WAAC,CAAC0C,qBAAqB,CAACsB,OAAO,CAAC,EAAE;YACnDX,KAAK,CAACe,GAAG,CAACxD,IAAI,CAAC;UACjB;UACA;QACF;MACA;QACEiD,OAAO,KAAPA,OAAO,GAAK7D,WAAC,CAACsE,YAAY,CAACN,OAAO,CAAC;QAEnC;MACF,KAAK,wBAAwB;IAE/B;IAEA,IAAI,SAAS,IAAIA,OAAO,CAACO,WAAW,IAAIP,OAAO,CAACO,WAAW,CAACjE,OAAO,EAAE;MACnE;IACF;IAGA,QAAQ0D,OAAO,CAACO,WAAW,CAAC/D,IAAI;MAC9B,KAAK,mBAAmB;QACtBgE,4CAAiC,CAACJ,GAAG,CAACJ,OAAO,CAACO,WAAW,CAAC;MAE5D,KAAK,qBAAqB;MAC1B,KAAK,kBAAkB;QAAE;UACvBV,OAAO,GAAG,KAAK;UACf,MAAM/B,QAAQ,GAAGkC,OAAO,CAACO,WAAW,CAAChE,EAAE,CAACK,IAAI;UAC5CyC,KAAK,CAACe,GAAG,CAACtC,QAAQ,CAAC;UACnB4B,iBAAiB,CAACW,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDE,OAAO,CAACO,WAAW,EACnBvE,WAAC,CAAC+C,mBAAmB,CACnB/C,WAAC,CAACwC,oBAAoB,CACpB,GAAG,EACHX,mBAAmB,CAACjB,IAAI,EAAEkB,QAAQ,CAAC,EACnC9B,WAAC,CAAC4B,UAAU,CAACE,QAAQ,CACvB,CACF,CACF,CAAC;UACD;QACF;MACA,KAAK,qBAAqB;QAAE;UAC1B+B,OAAO,GAAG,KAAK;UACf,MAAMY,KAAK,GAAGzC,yBAAyB,CACrCgC,OAAO,CAACO,WAAW,EACnB3D,IAAI,EACJR,IAAI,CAAC6B,GACP,CAAC;UACDyB,iBAAiB,CAACW,MAAM,CAACP,CAAC,EAAEW,KAAK,CAACV,MAAM,EAAE,GAAGU,KAAK,CAAC;UACnDX,CAAC,IAAIW,KAAK,CAACV,MAAM,GAAG,CAAC;UACrB;QACF;MACA,KAAK,qBAAqB;QAAE;UAC1B,IAAI,CAAC/D,WAAC,CAACC,YAAY,CAAC+D,OAAO,CAACO,WAAW,CAAChE,EAAE,CAAC,EAAE;YAC3C,MAAM0C,6BAA6B,CAAC7C,IAAI,EAAE4D,OAAO,CAACO,WAAW,CAAC;UAChE;UAEA,MAAMN,WAAW,GAAGnD,YAAY,CAC9BV,IAAI,EACJ4D,OAAO,CAACO,WAAW,EACnBvE,WAAC,CAAC4B,UAAU,CAAChB,IAAI,CACnB,CAAC;UACD,IAAIqD,WAAW,KAAK,IAAI,EAAE;YACxBJ,OAAO,GAAG,KAAK;YACf,MAAMK,UAAU,GAAGF,OAAO,CAACO,WAAW,CAAChE,EAAE,CAACK,IAAI;YAC9C,IAAIyC,KAAK,CAACc,GAAG,CAACD,UAAU,CAAC,EAAE;cACzBR,iBAAiB,CAACI,CAAC,CAAC,GAAGG,WAAW;YACpC,CAAC,MAAM;cACLZ,KAAK,CAACe,GAAG,CAACF,UAAU,CAAC;cACrBR,iBAAiB,CAACW,MAAM,CACtBP,CAAC,EAAE,EACH,CAAC,EACDrC,cAAc,CAACyC,UAAU,CAAC,EAC1BD,WACF,CAAC;YACH;UACF,CAAC,MAAM;YACLP,iBAAiB,CAACW,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC;YAC9BA,CAAC,EAAE;UACL;QACF;IACF;EACF;EAEA,IAAID,OAAO,EAAE,OAAO,IAAI;EAGxB,IAAIa,gBAA8B,GAAG1E,WAAC,CAAC2E,gBAAgB,CAAC,EAAE,CAAC;EAE3D,IAAIvB,YAAY,EAAE;IAChB,MAAMwB,UAAU,GAAG5E,WAAC,CAAC+B,gBAAgB,CAACqB,YAAY,EAAEG,QAAQ,CAAC;IAC7DmB,gBAAgB,GAAGG,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC9C,QAAQ/E,WAAC,CAAC8C,SAAS,CAAC8B,UAAU,CAAC;AAC/B,WAAW5E,WAAC,CAAC8C,SAAS,CAAC8B,UAAU,CAAC,MAAMF,gBAAgB;AACxD,KAAK;EACH;EAEA,OAAOG,cAAQ,CAACG,SAAS,CAACD,GAAG;AAC/B,iBAAiB/E,WAAC,CAAC4B,UAAU,CAAChB,IAAI,CAAC;AACnC,QAAQ8C,iBAAiB;AACzB,SAASH,QAAQ,QAAQvD,WAAC,CAAC8C,SAAS,CAACS,QAAQ,CAAC,MAAMmB,gBAAgB;AACpE,GAAG;AACH", "ignoreList": []}