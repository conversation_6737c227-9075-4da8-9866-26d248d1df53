{"version": 3, "names": ["gracefulFs", "gracefulify", "realFs"], "sources": ["../../src/tools/gracefulifyFs.ts"], "sourcesContent": ["import realFs from 'fs';\nimport gracefulFs from 'graceful-fs';\n\ngracefulFs.gracefulify(realFs);\n\nexport default gracefulFs;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAqC;AAErCA,qBAAU,CAACC,WAAW,CAACC,aAAM,CAAC;AAAC,eAEhBF,qBAAU;AAAA"}