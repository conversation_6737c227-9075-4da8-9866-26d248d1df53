﻿// Importer des fonctionnalités depuis un autre fichierimport { Outlet, useNavigate } fromimport { Outlet, useNavigate } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport { ReactNode, useEffect, useState } fromimport { ReactNode, useEffect, useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { rootPaths } fromimport { rootPaths } from 'routes/paths';
// Importer des fonctionnalités depuis un autre fichierimport { authService } fromimport { authService } from 'services/authService';

// Définir une interface TypeScriptinterface AuthLayoutProps
interface AuthLayoutProps {
  children?: ReactNode;
}

// Créer une constanteconst AuthLayout =
const AuthLayout = ({ children }: AuthLayoutProps) => {
  // Créer une constanteconst navigate =  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    // Créer une constanteconst checkAuth =    const checkAuth = async () => {
      if (authService.isAuthenticated()) {
        // Créer une constanteconst userStr =        const userStr = localStorage.getItem('user');
        if (userStr) {
          // Créer une constanteconst user =          const user = JSON.parse(userStr);
          // If user is a livreur, log them out
          if (user.EstLivreur) {
            await authService.logout();
            setErrorMessage(
              "Les livreurs ne peuvent pas accéder à cette application. Veuillez utiliser l'application dédiée aux livreurs.",
            );
            setDialogOpen(true);
          } else {
            navigate(rootPaths.appRoot); // Redirect to app if user is not a livreur
          }
        } else {
          navigate(rootPaths.appRoot);
        }
      }
    };

    checkAuth();
  }, [navigate]);

  // Créer une constanteconst handleCloseDialog =
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <>
      <Stack
        direction="column"
        justifyContent="center"
        alignItems="center"
        spacing={2}
        sx={{ minHeight: '100vh' }}
      >
        {children || <Outlet />}
      </Stack>

      {/* Error Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{'Accès refusé'}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">{errorMessage}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary" autoFocus>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default AuthLayout;
