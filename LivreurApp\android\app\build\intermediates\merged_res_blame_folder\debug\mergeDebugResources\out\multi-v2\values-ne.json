{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,299,401,511,598,664,760,826,887,992,1064,1122,1196,1258,1312,1425,1485,1546,1605,1683,1807,1888,1973,2079,2160,2243,2326,2393,2459,2536,2615,2703,2772,2848,2929,2997,3088,3166,3259,3356,3430,3509,3607,3667,3733,3821,3909,3971,4039,4102,4207,4325,4420,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "212,294,396,506,593,659,755,821,882,987,1059,1117,1191,1253,1307,1420,1480,1541,1600,1678,1802,1883,1968,2074,2155,2238,2321,2388,2454,2531,2610,2698,2767,2843,2924,2992,3083,3161,3254,3351,3425,3504,3602,3662,3728,3816,3904,3966,4034,4097,4202,4320,4415,4535,4619"}, "to": {"startLines": "2,33,41,42,43,63,64,65,68,70,71,72,73,74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3034,3836,3938,4048,6539,6605,6701,6914,7043,7148,7220,7278,7352,7414,7468,7581,7641,7702,7761,7839,8030,8111,8196,8302,8383,8466,8549,8616,8682,8759,8838,8926,8995,9071,9152,9220,9311,9389,9482,9579,9653,9732,9830,9890,9956,10044,10132,10194,10262,10325,10430,10548,10643,10763", "endLines": "5,33,41,42,43,63,64,65,68,70,71,72,73,74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "262,3111,3933,4043,4130,6600,6696,6762,6970,7143,7215,7273,7347,7409,7463,7576,7636,7697,7756,7834,7958,8106,8191,8297,8378,8461,8544,8611,8677,8754,8833,8921,8990,9066,9147,9215,9306,9384,9477,9574,9648,9727,9825,9885,9951,10039,10127,10189,10257,10320,10425,10543,10638,10758,10842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "34,35,36,37,38,39,40,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3116,3219,3322,3424,3530,3628,3728,11066", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3214,3317,3419,3525,3623,3723,3831,11162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "44,66,67,69,82,118,119", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4135,6767,6835,6975,7963,10927,10997", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4207,6830,6909,7038,8025,10992,11061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5254", "endColumns": "163", "endOffsets": "5413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "267,376,487,595,686,793,913,997,1076,1167,1260,1355,1449,1549,1642,1737,1831,1922,2013,2099,2212,2313,2416,2529,2639,2756,2923,10847", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "371,482,590,681,788,908,992,1071,1162,1255,1350,1444,1544,1637,1732,1826,1917,2008,2094,2207,2308,2411,2524,2634,2751,2918,3029,10922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4212,4323,4481,4615,4732,4903,5039,5149,5418,5598,5712,5876,6009,6157,6309,6375,6447", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4318,4476,4610,4727,4898,5034,5144,5249,5593,5707,5871,6004,6152,6304,6370,6442,6534"}}]}]}