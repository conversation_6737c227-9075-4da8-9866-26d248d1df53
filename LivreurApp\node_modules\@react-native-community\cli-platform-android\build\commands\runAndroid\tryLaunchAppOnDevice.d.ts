/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { Flags } from '.';
declare function tryLaunchAppOnDevice(device: string | void, packageName: string, adbPath: string, args: Flags): void;
export default tryLaunchAppOnDevice;
//# sourceMappingURL=tryLaunchAppOnDevice.d.ts.map