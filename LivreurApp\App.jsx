// Importer React pour créer des composants
import React from 'react';
// Importer le conteneur de navigation pour gérer la navigation entre écrans
import {NavigationContainer} from '@react-navigation/native';
// Importer la barre de statut pour contrôler l'affichage du haut de l'écran
import {StatusBar} from 'react-native';
// Importer le navigateur en pile pour organiser les écrans
import {createNativeStackNavigator} from '@react-navigation/native-stack';
// Importer l'écran de connexion
import LoginScreen from './src/screen/LoginScreen';
// Importer l'écran d'accueil principal
import AccueilScreen from './src/screen/AccueilScreen';
// Importer l'écran de profil utilisateur
import ProfileScreen from './src/screen/ProfileScreen';
// Importer l'écran de détails d'une commande
import DetailCommandeScreen from './src/screen/DetailCommandeScreen';
// Importer l'écran de scanner QR code
import QRScannerScreen from './src/screen/QRScannerScreen';

// Créer un navigateur en pile pour gérer les écrans
const Stack = createNativeStackNavigator();

// Composant principal de l'application
const App = () => {
  // Retourner la structure de navigation de l'app
  return (
    // Conteneur principal qui gère toute la navigation
    <NavigationContainer>
      {/* Cacher la barre de statut du téléphone */}
      <StatusBar hidden={true} />

      {/* Navigateur qui gère les écrans en pile */}
      <Stack.Navigator
        initialRouteName="Login" // Écran de démarrage
        screenOptions={{
          headerShown: false, // Cacher l'en-tête par défaut
        }}>
        {/* Écran de connexion */}
        <Stack.Screen name="Login" component={LoginScreen} />
        {/* Écran d'accueil avec options spéciales */}
        <Stack.Screen
          name="Accueil"
          component={AccueilScreen}
          options={{
            gestureEnabled: false, // Désactiver le retour par geste
          }}
        />
        {/* Écran de profil */}
        <Stack.Screen name="Profile" component={ProfileScreen} />
        {/* Écran de détails de commande */}
        <Stack.Screen name="DetailCommande" component={DetailCommandeScreen} />
        {/* Écran de scanner QR */}
        <Stack.Screen name="QRScanner" component={QRScannerScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

// Exporter le composant App pour qu'il soit utilisé comme point d'entrée
export default App;
