// Importer des fonctionnalités depuis un autre fichierimport { useState } fromimport { useState } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { MenuItem } fromimport { MenuItem } from 'routes/sitemap';
// Importer des fonctionnalités depuis un autre fichierimport Box fromimport Box from '@mui/material/Box';
// Importer des fonctionnalités depuis un autre fichierimport Link fromimport Link from '@mui/material/Link';
// Importer des fonctionnalités depuis un autre fichierimport List fromimport List from '@mui/material/List';
// Importer des fonctionnalités depuis un autre fichierimport Collapse fromimport Collapse from '@mui/material/Collapse';
// Importer des fonctionnalités depuis un autre fichierimport ListItemIcon fromimport ListItemIcon from '@mui/material/ListItemIcon';
// Importer des fonctionnalités depuis un autre fichierimport ListItemText fromimport ListItemText from '@mui/material/ListItemText';
// Importer des fonctionnalités depuis un autre fichierimport ListItemButton fromimport ListItemButton from '@mui/material/ListItemButton';
// Importer des fonctionnalités depuis un autre fichierimport IconifyIcon fromimport IconifyIcon from 'components/base/IconifyIcon';

// Définir une interface TypeScriptinterface CollapseListItemProps
interface CollapseListItemProps extends MenuItem {
  onClick?: () => void;
}

// Créer une constanteconst CollapseListItem =
const CollapseListItem = ({ subheader, active, items, icon, onClick }: CollapseListItemProps) => {
  const [open, setOpen] = useState(false);

  // Créer une constanteconst handleClick =
  const handleClick = () => {
    setOpen(!open);
    if (onClick) onClick();
  };

  return (
    <Box sx={{ pb: 1.5 }}>
      <ListItemButton onClick={handleClick}>
        <ListItemIcon>
          {icon && (
            <IconifyIcon
              icon={icon}
              sx={{
                color: active ? 'primary.main' : null,
              }}
            />
          )}
        </ListItemIcon>
        <ListItemText
          primary={subheader}
          sx={{
            '& .MuiListItemText-primary': {
              color: active ? 'primary.main' : null,
            },
          }}
        />
        <IconifyIcon
          icon="iconamoon:arrow-down-2-duotone"
          sx={{
            color: active ? 'primary.main' : 'text.disabled',
            transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s ease-in-out',
          }}
        />
      </ListItemButton>

      <Collapse in={open} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          {items?.map((route) => {
            return (
              <ListItemButton
                key={route.pathName}
                component={Link}
                href={route.path}
                sx={{ ml: 2.25, bgcolor: route.active ? 'info.main' : null }}
              >
                <ListItemText
                  primary={route.pathName}
                  sx={{
                    '& .MuiListItemText-primary': {
                      color: 'text.disabled',
                    },
                  }}
                />
              </ListItemButton>
            );
          })}
        </List>
      </Collapse>
    </Box>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default CollapseListItem;
