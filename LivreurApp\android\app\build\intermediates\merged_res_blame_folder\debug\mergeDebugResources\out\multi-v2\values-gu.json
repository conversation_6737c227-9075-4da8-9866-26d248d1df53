{"logs": [{"outputFile": "com.livreurapp-mergeDebugResources-46:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cf7bafef873f44c654e32eb384e37ad\\transformed\\play-services-basement-18.4.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5189", "endColumns": "146", "endOffsets": "5331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2e85007912d4b927031f578e26c9bbb\\transformed\\play-services-base-18.5.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4207,4315,4481,4606,4718,4856,4978,5089,5336,5484,5592,5756,5881,6024,6174,6235,6301", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "4310,4476,4601,4713,4851,4973,5084,5184,5479,5587,5751,5876,6019,6169,6230,6296,6378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "35,36,37,38,39,40,41,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3107,3201,3304,3401,3503,3605,3703,11840", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3196,3299,3396,3498,3600,3698,3820,11936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51cc5b71adb4e194efe386bde3184a3f\\transformed\\material-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,305,404,524,608,671,762,829,888,978,1043,1107,1176,1238,1292,1407,1465,1526,1580,1653,1780,1866,1950,2053,2128,2204,2290,2357,2423,2496,2576,2661,2732,2808,2887,2956,3052,3130,3225,3321,3395,3470,3569,3620,3687,3774,3864,3926,3990,4053,4155,4260,4357,4463", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "218,300,399,519,603,666,757,824,883,973,1038,1102,1171,1233,1287,1402,1460,1521,1575,1648,1775,1861,1945,2048,2123,2199,2285,2352,2418,2491,2571,2656,2727,2803,2882,2951,3047,3125,3220,3316,3390,3465,3564,3615,3682,3769,3859,3921,3985,4048,4150,4255,4352,4458,4536"}, "to": {"startLines": "2,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3825,3924,4044,6383,6446,6608,6822,6948,7038,7103,7167,7236,7298,7352,7467,7525,7586,7640,7713,8057,8143,8227,8330,8405,8481,8567,8634,8700,8773,8853,8938,9009,9085,9164,9233,9329,9407,9502,9598,9672,9747,9846,9897,9964,10051,10141,10203,10267,10330,10432,10537,10634,10740", "endLines": "5,34,42,43,44,64,65,67,70,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "268,3102,3919,4039,4123,6441,6532,6670,6876,7033,7098,7162,7231,7293,7347,7462,7520,7581,7635,7708,7835,8138,8222,8325,8400,8476,8562,8629,8695,8768,8848,8933,9004,9080,9159,9228,9324,9402,9497,9593,9667,9742,9841,9892,9959,10046,10136,10198,10262,10325,10427,10532,10629,10735,10813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,342,421,488,555,629,705,785,866,934,1013,1091,1166,1245,1325,1405,1476,1547,1646,1718,1793,1862", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "119,198,269,337,416,483,550,624,700,780,861,929,1008,1086,1161,1240,1320,1400,1471,1542,1641,1713,1788,1857,1930"}, "to": {"startLines": "33,45,66,68,69,71,84,85,86,121,122,123,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2956,4128,6537,6675,6743,6881,7840,7907,7981,10818,10898,10979,11047,11207,11285,11360,11439,11519,11599,11670,11741,11941,12013,12088,12157", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "3020,4202,6603,6738,6817,6943,7902,7976,8052,10893,10974,11042,11121,11280,11355,11434,11514,11594,11665,11736,11835,12008,12083,12152,12225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,380,484,591,678,778,898,976,1053,1144,1237,1332,1426,1526,1619,1714,1808,1899,1990,2070,2176,2277,2374,2483,2583,2693,2853,11126", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "375,479,586,673,773,893,971,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2065,2171,2272,2369,2478,2578,2688,2848,2951,11202"}}]}]}