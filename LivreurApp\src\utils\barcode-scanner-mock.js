// Mock for expo-barcode-scanner
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import React, { useEffect } from 'react';

// Mock BarCodeScanner component with simulated scanning
export const BarCodeScanner = ({ style, onBarCodeScanned }) => {
  useEffect(() => {
    // Simulate scanning a QR code after 2 seconds
    const timer = setTimeout(() => {
      if (onBarCodeScanned) {
        onBarCodeScanned({ type: 'qr', data: 'CMD-123456' });
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [onBarCodeScanned]);

  // Return a styled View component that looks like a scanner
  return (
    <View style={[styles.container, style]}>
      <View style={styles.scannerOverlay}>
        <View style={styles.scannerFrame} />
        <ActivityIndicator size="large" color="#FFAA80" style={styles.indicator} />
        <Text style={styles.scanningText}>Recherche de QR code...</Text>
      </View>
    </View>
  );
};

// Styles for the mock scanner
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scannerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#FFAA80',
    backgroundColor: 'transparent',
    borderRadius: 12,
  },
  indicator: {
    marginTop: 20,
  },
  scanningText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
});

// Mock static methods
BarCodeScanner.Constants = {
  Type: {
    qr: 'qr',
  },
};

// Mock permission request function
BarCodeScanner.requestPermissionsAsync = async () => {
  return { status: 'granted' };
};

export default {
  BarCodeScanner,
};
