"use client";
import {
  require_createSvgIcon
} from "./chunk-F6HH2RS4.js";
import {
  require_interopRequireDefault
} from "./chunk-TDKLIJLH.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/ArrowBack.js
var require_ArrowBack = __commonJS({
  "node_modules/@mui/icons-material/ArrowBack.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"
    }), "ArrowBack");
  }
});
export default require_ArrowBack();
//# sourceMappingURL=@mui_icons-material_ArrowBack.js.map
