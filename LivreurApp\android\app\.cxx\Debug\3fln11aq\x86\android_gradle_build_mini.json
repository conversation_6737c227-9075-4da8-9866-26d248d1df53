{"buildFiles": ["E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\react-native-camera-kit\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\.cxx\\Debug\\3fln11aq\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\.cxx\\Debug\\3fln11aq\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86", "output": "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86", "output": "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86", "output": "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libappmodules.so", "runtimeFiles": ["E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_safeareacontext.so", "E:\\PFE\\Test\\AxiaLivraison\\Livreur\\LivreurApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3fln11aq\\obj\\x86\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\072209afb1c52d43fe8bcfe0160879d7\\transformed\\react-android-0.79.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rncamerakit_specs::@d945e98661337998d651": {"artifactName": "react_codegen_rncamerakit_specs", "abi": "x86", "runtimeFiles": []}, "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba": {"artifactName": "react_codegen_RNCGeolocationSpec", "abi": "x86", "runtimeFiles": []}}}