{"version": 3, "names": ["getAndroidProject", "config", "androidProject", "project", "android", "CLIError", "discoverPackageName", "manifestPath", "buildGradlePath", "androidManifest", "fs", "readFileSync", "packageNameFromManifest", "parsePackageNameFromAndroidManifestFile", "buildGradle", "namespace", "parseNamespaceFromBuildGradleFile", "chalk", "underline", "dim", "getPackageName", "packageName", "validatePackageName", "logger", "warn", "bgRed", "matchArray", "match", "length", "test"], "sources": ["../../src/config/getAndroidProject.ts"], "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport chalk from 'chalk';\n\nexport function getAndroidProject(config: Config) {\n  const androidProject = config.project.android;\n\n  if (!androidProject) {\n    throw new CLIError(`\n      Android project not found. Are you sure this is a React Native project?\n      If your Android files are located in a non-standard location (e.g. not inside 'android' folder), consider setting\n      \\`project.android.sourceDir\\` option to point to a new location.\n    `);\n  }\n  return androidProject;\n}\n\n/**\n * Util function to discover the package name from either the Manifest file or the build.gradle file.\n * @param manifestPath The path to the AndroidManifest.xml\n * @param buildGradlePath The path to the build.gradle[.kts] file.\n */\nfunction discoverPackageName(\n  manifestPath: string | null,\n  buildGradlePath: string | null,\n) {\n  if (manifestPath) {\n    const androidManifest = fs.readFileSync(manifestPath, 'utf8');\n    const packageNameFromManifest = parsePackageNameFromAndroidManifestFile(\n      androidManifest,\n    );\n    // We got the package from the AndroidManifest.xml\n    if (packageNameFromManifest) {\n      return packageNameFromManifest;\n    }\n  }\n\n  if (buildGradlePath) {\n    // We didn't get the package from the AndroidManifest.xml,\n    // so we'll try to get it from the build.gradle[.kts] file\n    // via the namespace field.\n    const buildGradle = fs.readFileSync(buildGradlePath, 'utf8');\n    const namespace = parseNamespaceFromBuildGradleFile(buildGradle);\n    if (namespace) {\n      return namespace;\n    }\n  }\n\n  throw new CLIError(\n    `Failed to build the app: No package name found. \n    We couldn't parse the namespace from neither your build.gradle[.kts] file at ${chalk.underline.dim(\n      `${buildGradlePath}`,\n    )} \n    nor your package in the AndroidManifest at ${chalk.underline.dim(\n      `${manifestPath}`,\n    )}\n    `,\n  );\n}\n\n/**\n * Get the package name/namespace of the running React Native app\n * @param manifestPath The path to the AndroidManifest.xml\n * @param buildGradlePath The path to the build.gradle[.kts] file.\n */\nexport function getPackageName(\n  manifestPath: string | null,\n  buildGradlePath: string | null,\n) {\n  let packageName = discoverPackageName(manifestPath, buildGradlePath);\n  if (!validatePackageName(packageName)) {\n    logger.warn(\n      `Invalid application's package name \"${chalk.bgRed(\n        packageName,\n      )}\" in either 'AndroidManifest.xml' or 'build.gradle'. Read guidelines for setting the package name here: ${chalk.underline.dim(\n        'https://developer.android.com/studio/build/application-id',\n      )}`,\n    );\n  }\n  return packageName;\n}\n\nexport function parsePackageNameFromAndroidManifestFile(\n  androidManifest: string,\n) {\n  const matchArray = androidManifest.match(/package=\"(.+?)\"/);\n  if (matchArray && matchArray.length > 0) {\n    return matchArray[1];\n  } else {\n    return null;\n  }\n}\n\nexport function parseNamespaceFromBuildGradleFile(buildGradle: string) {\n  // search for namespace = inside the build.gradle file via regex\n  const matchArray = buildGradle.match(/namespace\\s*[=]*\\s*[\"'](.+?)[\"']/);\n  if (matchArray && matchArray.length > 0) {\n    return matchArray[1];\n  } else {\n    return null;\n  }\n}\n\n// Validates that the package name is correct\nexport function validatePackageName(packageName: string) {\n  return /^[a-z][a-z0-9_]*(\\.[a-z][a-z0-9_]*)+$/i.test(packageName);\n}\n"], "mappings": ";;;;;;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAEnB,SAASA,iBAAiB,CAACC,MAAc,EAAE;EAChD,MAAMC,cAAc,GAAGD,MAAM,CAACE,OAAO,CAACC,OAAO;EAE7C,IAAI,CAACF,cAAc,EAAE;IACnB,MAAM,KAAIG,oBAAQ,EAAE;AACxB;AACA;AACA;AACA,KAAK,CAAC;EACJ;EACA,OAAOH,cAAc;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,mBAAmB,CAC1BC,YAA2B,EAC3BC,eAA8B,EAC9B;EACA,IAAID,YAAY,EAAE;IAChB,MAAME,eAAe,GAAGC,aAAE,CAACC,YAAY,CAACJ,YAAY,EAAE,MAAM,CAAC;IAC7D,MAAMK,uBAAuB,GAAGC,uCAAuC,CACrEJ,eAAe,CAChB;IACD;IACA,IAAIG,uBAAuB,EAAE;MAC3B,OAAOA,uBAAuB;IAChC;EACF;EAEA,IAAIJ,eAAe,EAAE;IACnB;IACA;IACA;IACA,MAAMM,WAAW,GAAGJ,aAAE,CAACC,YAAY,CAACH,eAAe,EAAE,MAAM,CAAC;IAC5D,MAAMO,SAAS,GAAGC,iCAAiC,CAACF,WAAW,CAAC;IAChE,IAAIC,SAAS,EAAE;MACb,OAAOA,SAAS;IAClB;EACF;EAEA,MAAM,KAAIV,oBAAQ,EACf;AACL,mFAAmFY,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC/F,GAAEX,eAAgB,EAAC,CACpB;AACN,iDAAiDS,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC7D,GAAEZ,YAAa,EAAC,CACjB;AACN,KAAK,CACF;AACH;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASa,cAAc,CAC5Bb,YAA2B,EAC3BC,eAA8B,EAC9B;EACA,IAAIa,WAAW,GAAGf,mBAAmB,CAACC,YAAY,EAAEC,eAAe,CAAC;EACpE,IAAI,CAACc,mBAAmB,CAACD,WAAW,CAAC,EAAE;IACrCE,kBAAM,CAACC,IAAI,CACR,uCAAsCP,gBAAK,CAACQ,KAAK,CAChDJ,WAAW,CACX,2GAA0GJ,gBAAK,CAACC,SAAS,CAACC,GAAG,CAC7H,2DAA2D,CAC3D,EAAC,CACJ;EACH;EACA,OAAOE,WAAW;AACpB;AAEO,SAASR,uCAAuC,CACrDJ,eAAuB,EACvB;EACA,MAAMiB,UAAU,GAAGjB,eAAe,CAACkB,KAAK,CAAC,iBAAiB,CAAC;EAC3D,IAAID,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOF,UAAU,CAAC,CAAC,CAAC;EACtB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AAEO,SAASV,iCAAiC,CAACF,WAAmB,EAAE;EACrE;EACA,MAAMY,UAAU,GAAGZ,WAAW,CAACa,KAAK,CAAC,kCAAkC,CAAC;EACxE,IAAID,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOF,UAAU,CAAC,CAAC,CAAC;EACtB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACO,SAASJ,mBAAmB,CAACD,WAAmB,EAAE;EACvD,OAAO,wCAAwC,CAACQ,IAAI,CAACR,WAAW,CAAC;AACnE"}