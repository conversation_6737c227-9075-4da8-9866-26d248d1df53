﻿// Importer des fonctionnalités depuis un autre fichierimport { forwardRef } fromimport { forwardRef } from 'react';
// Importer des fonctionnalités depuis un autre fichierimport { Box, BoxProps } fromimport { Box, BoxProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Swiper, SwiperProps } fromimport { Swiper, SwiperProps } from 'swiper/react';
// Importer des fonctionnalités depuis un autre fichierimport { Swiper as SwiperClass } fromimport { Swiper as SwiperClass } from 'swiper/types';
// Importer des fonctionnalités depuis un autre fichierimport { Navigation, Pagination, Scrollbar, A11y } fromimport { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';
import 'swiper/css/navigation';
import 'swiper/css';

export interface ReactSwiperProps extends BoxProps {
  slidesPerView: number;
  children: React.ReactNode;
  onBeforeInit?: (swiper: SwiperClass) => void;
  onSlideChange?: (swiper: SwiperClass) => void;
}

// Créer une constanteconst ReactSwiper =
const ReactSwiper = forwardRef<null | SwiperProps, ReactSwiperProps>(
  ({ children, slidesPerView, onBeforeInit, onSlideChange, ...rest }, ref) => {
    return (
      <Box
        component={Swiper}
        ref={ref}
        modules={[Navigation, Pagination, Scrollbar, A11y]}
        spaceBetween={20}
        slidesPerView={slidesPerView}
        onBeforeInit={onBeforeInit}
        onSlideChange={onSlideChange}
        width={1}
        {...rest}
      >
        {children}
      </Box>
    );
  },
);

// Exporter comme élément principal de ce fichierexport default
export default ReactSwiper;
