// Importer une bibliothèque nécessaireusing AxiaLivraisonAPI.Models;using AxiaLivraisonAPI.Models;
// Importer une bibliothèque nécessaireusing AxiaLivraisonAPI.DTO;using AxiaLivraisonAPI.DTO;
// Importer une bibliothèque nécessaireusing System.Security.Claims;using System.Security.Claims;

// Définir l'espace de noms pour organiser le codenamespace AxiaLivraisonAPI.Services
namespace AxiaLivraisonAPI.Services
{
    public interface IJwtService
    {
        string GenerateAccessToken(Utilisateur user, List<PermissionInfo>? permissions = null);
        RefreshToken GenerateRefreshToken(int userId, string? ipAddress = null);
        ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);
        Task<bool> IsTokenBlacklistedAsync(string tokenId);
        Task BlacklistTokenAsync(string tokenId, DateTime expiry, string? reason = null, int? userId = null);
        Task<RefreshToken?> GetRefreshTokenAsync(string token);
        Task RevokeRefreshTokenAsync(RefreshToken refreshToken, string? ipAddress = null, string? reason = null, string? replacedByToken = null);
        Task<bool> ValidateRefreshTokenAsync(string token);
        Task CleanupExpiredTokensAsync();
    }
}
