// Importer des fonctionnalités depuis un autre fichierimport { Box, SxProps } fromimport { Box, SxProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { ImgHTMLAttributes } fromimport { ImgHTMLAttributes } from 'react';

// Définir une interface TypeScriptinterface ImageProps
interface ImageProps extends ImgHTMLAttributes<HTMLImageElement> {
  sx?: SxProps;
}
// Créer une constanteconst Image =const Image = ({ sx, ...rest }: ImageProps) => {
  return <Box component="img" sx={sx} {...rest} />;
};

// Exporter comme élément principal de ce fichierexport default
export default Image;
