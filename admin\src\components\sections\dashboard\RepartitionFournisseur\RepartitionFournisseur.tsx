﻿// Importer des fonctionnalités depuis un autre fichierimport { SxProps } fromimport { SxProps } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport ReactEchart fromimport ReactEchart from 'components/base/ReactEchart';
// Importer des fonctionnalités depuis un autre fichierimport EChartsReactCore fromimport EChartsReactCore from 'echarts-for-react/lib/core';
// Importer des fonctionnalités depuis un autre fichierimport * as echarts fromimport * as echarts from 'echarts/core';
// Importer des fonctionnalités depuis un autre fichierimport { PieChart } fromimport { PieChart } from 'echarts/charts';
// Importer des fonctionnalités depuis un autre fichierimport { TooltipComponent, LegendComponent } fromimport { TooltipComponent, LegendComponent } from 'echarts/components';
// Importer des fonctionnalités depuis un autre fichierimport { CanvasRenderer } fromimport { CanvasRenderer } from 'echarts/renderers';
// Importer des fonctionnalités depuis un autre fichierimport { useMemo } fromimport { useMemo } from 'react';

echarts.use([PieChart, TooltipComponent, LegendComponent, CanvasRenderer]);

// Définir une interface TypeScriptinterface DistributionChartProps
interface DistributionChartProps {
  sx?: SxProps;
  chartRef: React.RefObject<EChartsReactCore>;
  data: {
    id: number;
    value: number;
    name: string;
    visible: boolean;
    amount: number;
  }[];
  getColorForId: (id: number) => string;
}

// Créer une constanteconst DistributionChart =
const DistributionChart = ({ chartRef, data, getColorForId, ...rest }: DistributionChartProps) => {
  // Créer une constanteconst option =  const option = useMemo(() => {
    // Créer une constanteconst visibleData =    const visibleData = data.filter((item) => item.visible);

    return {
      tooltip: {
        trigger: 'item',
        formatter: (params: {
          name: string;
          value: number;
          percent: number;
          data?: {
            amount?: number;
            [key: string]: unknown;
          };
        }) => {
          // Créer une constanteconst amount =          const amount = params.data?.amount || 0;
          return `
            <strong>${params.name}</strong><br/>
            Commandes: ${params.value}<br/>
            Montant: ${amount.toLocaleString('fr-FR', { style: 'currency', currency: 'TND', minimumFractionDigits: 3 })}<br/>
            ${params.percent.toFixed(2)}%
          `;
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          data: visibleData.map((item) => ({
            value: item.value,
            name: item.name,
            amount: item.amount,
            itemStyle: { color: getColorForId(item.id) },
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
  }, [data, getColorForId]);

  return <ReactEchart ref={chartRef} echarts={echarts} option={option} {...rest} />;
};

// Exporter comme élément principal de ce fichierexport default
export default DistributionChart;
