// Importer des fonctionnalités depuis un autre fichierimport { Theme } fromimport { Theme } from '@mui/material';
// Importer des fonctionnalités depuis un autre fichierimport { Components } fromimport { Components } from '@mui/material/styles/components';
// Importer des fonctionnalités depuis un autre fichierimport { Link as RouterLink, LinkProps as RouterLinkProps } fromimport { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router-dom';
// Importer des fonctionnalités depuis un autre fichierimport { forwardRef } fromimport { forwardRef } from 'react';

// Créer une constanteconst LinkBehavior =
const LinkBehavior = forwardRef<
  HTMLAnchorElement,
  Omit<RouterLinkProps, 'to'> & { href: RouterLinkProps['to'] }
>((props, ref) => {
  const { href, ...other } = props;
  // Map href (Material UI) -> to (react-router)
  return <RouterLink ref={ref} to={href} {...other} />;
});

const Link: Components<Omit<Theme, 'components'>>['MuiLink'] = {
  defaultProps: {
    underline: 'none',
    component: LinkBehavior,
  },
};

// Exporter comme élément principal de ce fichierexport default
export default Link;
