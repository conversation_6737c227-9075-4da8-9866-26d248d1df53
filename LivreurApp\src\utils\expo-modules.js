// Mock EventEmitter for Expo modules
class EventEmitter {
  constructor() {
    this.listeners = {};
  }

  addListener(eventName, listener) {
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = [];
    }
    this.listeners[eventName].push(listener);
    return {
      remove: () => this.removeListener(eventName, listener)
    };
  }

  removeListener(eventName, listener) {
    if (!this.listeners[eventName]) return;
    // Créer une constanteconst index =    const index = this.listeners[eventName].indexOf(listener);
    if (index !== -1) {
      this.listeners[eventName].splice(index, 1);
    }
  }

  emit(eventName, ...args) {
    if (!this.listeners[eventName]) return;
    this.listeners[eventName].forEach(listener => {
      listener(...args);
    });
  }
}

// Export a mock EventEmitter
// Exporter comme élément principal de ce fichierexport defaultexport default new EventEmitter();
