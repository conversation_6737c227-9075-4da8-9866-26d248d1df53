﻿// Importer des fonctionnalités depuis un autre fichierimport Stack fromimport Stack from '@mui/material/Stack';
// Importer des fonctionnalités depuis un autre fichierimport Typography fromimport Typography from '@mui/material/Typography';
// Importer des fonctionnalités depuis un autre fichierimport Pagination fromimport Pagination from '@mui/material/Pagination';
import {
  gridPageSelector,
  gridPageCountSelector,
  useGridApiContext,
  useGridSelector,
  gridPageSizeSelector,
} from '@mui/x-data-grid';

// Créer une constanteconst DataGridFooter =
const DataGridFooter = () => {
  // Créer une constanteconst apiRef =  const apiRef = useGridApiContext();
  // Créer une constanteconst page =  const page = useGridSelector(apiRef, gridPageSelector);
  // Créer une constanteconst pageCount =  const pageCount = useGridSelector(apiRef, gridPageCountSelector);
  // Créer une constanteconst pageSize =  const pageSize = useGridSelector(apiRef, gridPageSizeSelector);
  // Créer une constanteconst rowsCount =  const rowsCount = apiRef.current.getRowsCount();

  return (
    <Stack alignItems="center" justifyContent="space-between" pl={3} pr={1.6} width={1}>
      <Typography
        variant="body2"
        color="text.primary"
      >{`${page * pageSize + 1}-${page * pageSize + pageSize} of ${rowsCount}`}</Typography>
      <Pagination
        color="primary"
        count={pageCount}
        page={page + 1}
        onChange={(event, value) => {
          event.preventDefault();
          apiRef.current.setPage(value - 1);
        }}
      />
    </Stack>
  );
};

// Exporter comme élément principal de ce fichierexport default
export default DataGridFooter;
